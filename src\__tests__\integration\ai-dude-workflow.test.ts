/**
 * AI Dude Workflow Integration Tests
 * Tests the complete AI Dude content generation workflow
 */

import { AIContentGenerator } from '@/lib/ai/content-generator';
import { PromptManager } from '@/lib/ai/prompt-manager';
import { EnhancedContentGenerationPipeline } from '@/lib/content-generation/enhanced-pipeline';

// Mock all dependencies
jest.mock('@/lib/ai/content-generator');
jest.mock('@/lib/ai/prompt-manager');
jest.mock('@/lib/ai/providers/openai-client');
jest.mock('@/lib/ai/providers/openrouter-client');

// Mock Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn()
        }))
      })),
      insert: jest.fn(() => ({
        select: jest.fn()
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn()
        }))
      }))
    }))
  }))
}));

describe('AI Dude Workflow Integration', () => {
  let generator: AIContentGenerator;
  let pipeline: EnhancedContentGenerationPipeline;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock PromptManager methods
    (PromptManager.buildAIDudeSystemPrompt as jest.Mock).mockResolvedValue(
      'You are AI Dude, the irreverent curator of AI tools...'
    );
    (PromptManager.buildAIDudeUserPrompt as jest.Mock).mockResolvedValue(
      'Tool URL: https://example.com\n\nScraped Content:\nTest content'
    );
    (PromptManager.getAIDudeDatabaseSchema as jest.Mock).mockResolvedValue({
      name: 'string',
      description: 'string',
      features: 'array',
      pros_and_cons: 'object',
      releases: 'array'
    });
    (PromptManager.processAIDudeResponse as jest.Mock).mockReturnValue({
      name: 'Test AI Tool',
      description: 'An awesome AI tool that does amazing things',
      detailed_description: 'This tool revolutionizes AI workflows with its cutting-edge features',
      features: ['Feature 1', 'Feature 2', 'Feature 3'],
      pros_and_cons: {
        pros: ['Easy to use', 'Powerful features', 'Great support'],
        cons: ['Learning curve', 'Premium pricing']
      },
      pricing: 'Free tier with premium options starting at $29/month',
      meta_title: 'Test AI Tool - Revolutionary AI Platform',
      meta_description: 'Discover Test AI Tool, the revolutionary AI platform that transforms workflows',
      meta_keywords: 'AI tool, automation, workflow, productivity',
      releases: [
        {
          version: '2.1.0',
          date: '2024-01-15',
          features: ['New dashboard', 'API improvements']
        }
      ]
    });

    generator = new AIContentGenerator();
    pipeline = new EnhancedContentGenerationPipeline();
  });

  describe('Complete Content Generation Workflow', () => {
    it('should generate complete content using AI Dude methodology', async () => {
      // Mock successful generation
      (generator.generateContent as jest.Mock).mockResolvedValue({
        success: true,
        content: {
          name: 'Test AI Tool',
          description: 'An awesome AI tool',
          detailed_description: 'Detailed description',
          features: ['Feature 1', 'Feature 2'],
          pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] },
          pricing: 'Free with premium options',
          releases: []
        },
        methodology: 'ai_dude',
        validation: { isValid: true, score: 95, issues: [] },
        tokenUsage: { total: 1500 },
        timestamp: new Date().toISOString()
      });

      const result = await generator.generateContent(
        'Test scraped content about an AI tool',
        'https://example-ai-tool.com'
      );

      expect(result.success).toBe(true);
      expect(result.methodology).toBe('ai_dude');
      expect(result.content).toBeDefined();
      expect(result.content.name).toBe('Test AI Tool');
      expect(result.validation.isValid).toBe(true);
    });

    it('should handle partial content generation for specific sections', async () => {
      // Mock partial generation
      (generator.generatePartialContentAIDude as jest.Mock).mockResolvedValue({
        success: true,
        content: {
          features: ['New Feature 1', 'New Feature 2', 'Enhanced Feature 3']
        },
        methodology: 'ai_dude_partial',
        validation: { isValid: true, score: 90, issues: [] },
        tokenUsage: { total: 800 },
        timestamp: new Date().toISOString()
      });

      const existingData = {
        name: 'Existing Tool',
        description: 'Existing description'
      };

      const result = await generator.generatePartialContentAIDude(
        'features',
        existingData,
        'New scraped content with updated features',
        'https://example.com'
      );

      expect(result.success).toBe(true);
      expect(result.methodology).toBe('ai_dude_partial');
      expect(result.content.features).toHaveLength(3);
    });

    it('should validate AI Dude content quality', async () => {
      const mockContent = {
        name: 'Test Tool',
        description: 'A comprehensive AI tool that revolutionizes workflows',
        detailed_description: 'This tool provides advanced AI capabilities with an intuitive interface',
        features: ['AI-powered automation', 'Real-time analytics', 'Custom integrations'],
        pros_and_cons: {
          pros: ['User-friendly interface', 'Powerful AI features', 'Excellent support'],
          cons: ['Learning curve for advanced features', 'Premium pricing']
        },
        pricing: 'Free tier available, premium plans start at $29/month',
        meta_title: 'Test Tool - Advanced AI Platform',
        meta_description: 'Discover Test Tool, the advanced AI platform for workflow automation',
        meta_keywords: 'AI, automation, workflow, productivity, analytics'
      };

      // Test validation through the generator
      const validationResult = await (generator as any).validateAIDudeContent(mockContent);

      expect(validationResult.isValid).toBe(true);
      expect(validationResult.score).toBeGreaterThan(80);
      expect(validationResult.issues).toHaveLength(0);
    });
  });

  describe('Enhanced Pipeline Integration', () => {
    it('should process tools with AI Dude methodology through enhanced pipeline', async () => {
      // Mock pipeline methods
      (pipeline.generateWithAutoTriggers as jest.Mock).mockResolvedValue({
        success: true,
        toolId: 'test-tool-123',
        generationResults: {
          content: {
            name: 'Pipeline Test Tool',
            description: 'Generated through enhanced pipeline'
          },
          methodology: 'ai_dude'
        },
        autoTriggers: {
          scrapingTriggered: true,
          mediaTriggered: true
        },
        processingTime: 5000
      });

      const result = await pipeline.generateWithAutoTriggers({
        toolId: 'test-tool-123',
        url: 'https://example.com',
        autoTriggerScraping: true,
        autoTriggerMedia: true,
        includeReleases: true,
        priority: 'high',
        methodology: 'ai_dude'
      });

      expect(result.success).toBe(true);
      expect(result.generationResults.methodology).toBe('ai_dude');
      expect(result.autoTriggers.scrapingTriggered).toBe(true);
    });

    it('should handle bulk processing with AI Dude methodology', async () => {
      const tools = [
        { id: 'tool-1', url: 'https://tool1.com' },
        { id: 'tool-2', url: 'https://tool2.com' },
        { id: 'tool-3', url: 'https://tool3.com' }
      ];

      // Mock bulk processing
      (pipeline.processBulkTools as jest.Mock).mockResolvedValue({
        success: true,
        processedCount: 3,
        results: tools.map(tool => ({
          toolId: tool.id,
          success: true,
          methodology: 'ai_dude',
          content: {
            name: `Tool ${tool.id}`,
            description: 'Generated with AI Dude methodology'
          }
        })),
        totalProcessingTime: 15000
      });

      const result = await pipeline.processBulkTools(tools, {
        methodology: 'ai_dude',
        autoTriggerScraping: true,
        includeReleases: true
      });

      expect(result.success).toBe(true);
      expect(result.processedCount).toBe(3);
      expect(result.results.every(r => r.methodology === 'ai_dude')).toBe(true);
    });
  });

  describe('Error Handling and Fallbacks', () => {
    it('should handle AI provider failures gracefully', async () => {
      // Mock AI failure
      (generator.generateContent as jest.Mock).mockResolvedValue({
        success: false,
        error: 'AI provider temporarily unavailable',
        methodology: 'ai_dude',
        timestamp: new Date().toISOString()
      });

      const result = await generator.generateContent(
        'Test content',
        'https://example.com'
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('AI provider temporarily unavailable');
      expect(result.methodology).toBe('ai_dude');
    });

    it('should validate prompt template availability', async () => {
      // Mock database template retrieval failure
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: null,
        error: new Error('Template not found')
      });

      // Should fall back to hardcoded templates
      const systemPrompt = await PromptManager.buildAIDudeSystemPrompt({});
      const userPrompt = await PromptManager.buildAIDudeUserPrompt('test', 'https://test.com');

      expect(systemPrompt).toContain('AI Dude');
      expect(userPrompt).toContain('test');
    });

    it('should handle malformed AI responses', async () => {
      // Mock malformed response
      (PromptManager.extractJsonFromResponse as jest.Mock).mockReturnValue({});
      (PromptManager.processAIDudeResponse as jest.Mock).mockReturnValue({
        name: '',
        description: '',
        features: [],
        pros_and_cons: { pros: [], cons: [] }
      });

      const result = await generator.generateContent(
        'Test content',
        'https://example.com'
      );

      // Should handle gracefully with validation errors
      expect(result).toBeDefined();
    });
  });

  describe('Template System Integration', () => {
    it('should load all 11 AI Dude templates correctly', async () => {
      const templateKeys = [
        'prompt_ai_dude_complete_system',
        'prompt_ai_dude_complete_user',
        'prompt_ai_dude_partial_system',
        'prompt_ai_dude_partial_context',
        'prompt_ai_dude_features',
        'prompt_ai_dude_pricing',
        'prompt_ai_dude_pros_cons',
        'prompt_ai_dude_seo',
        'prompt_ai_dude_faqs',
        'prompt_ai_dude_releases',
        'prompt_ai_dude_validation'
      ];

      for (const key of templateKeys) {
        mockSupabaseClient.from().select().eq().single.mockResolvedValue({
          data: {
            config_value: {
              template: `Template for ${key}`,
              promptType: key.includes('system') ? 'system' : 'user'
            }
          },
          error: null
        });

        // Test template loading (this would be done internally by PromptManager)
        expect(mockSupabaseClient.from).toBeDefined();
      }
    });
  });
});
