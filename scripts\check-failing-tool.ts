import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTool() {
  const toolId = '2d25e305-1924-49e2-a5b4-c1e80b951123';
  
  console.log('🔍 Checking tool that failed in logs...');
  console.log(`Tool ID: ${toolId}`);
  
  const { data: tools, error } = await supabase
    .from('tools')
    .select('submission_source, submission_type, name, website, id, created_at')
    .eq('id', toolId);
    
  console.log('Query result:', { 
    tools: tools?.length || 0, 
    error: error?.message || 'none' 
  });
  
  if (tools && tools.length > 0) {
    console.log('✅ Tool found:', tools[0]);
  } else {
    console.log('❌ Tool not found');
    
    // Check if there are any tools for Foodieprep
    const { data: foodieTools, error: foodieError } = await supabase
      .from('tools')
      .select('id, name, website, submission_source, created_at')
      .ilike('name', '%foodie%')
      .order('created_at', { ascending: false })
      .limit(5);
      
    console.log(`\n📋 Found ${foodieTools?.length || 0} Foodieprep-related tools:`);
    if (foodieTools && foodieTools.length > 0) {
      foodieTools.forEach((tool, i) => {
        console.log(`  ${i + 1}. ${tool.id}`);
        console.log(`     Name: ${tool.name}`);
        console.log(`     Website: ${tool.website}`);
        console.log(`     Source: ${tool.submission_source}`);
        console.log(`     Created: ${tool.created_at}`);
        console.log('');
      });
    }
  }

  // Check recent AI generation jobs for this tool
  console.log('\n🔍 Checking recent AI generation jobs...');
  const { data: jobs, error: jobError } = await supabase
    .from('ai_generation_jobs')
    .select('id, tool_id, job_type, status, created_at, error_message')
    .eq('tool_id', toolId)
    .order('created_at', { ascending: false })
    .limit(10);

  if (jobError) {
    console.error('❌ Job query error:', jobError);
  } else {
    console.log(`📋 Found ${jobs?.length || 0} jobs for this tool:`);
    if (jobs && jobs.length > 0) {
      jobs.forEach((job, i) => {
        console.log(`  ${i + 1}. ${job.id} - ${job.job_type} - ${job.status} (${job.created_at})`);
        if (job.error_message) {
          console.log(`     Error: ${job.error_message}`);
        }
      });
    }
  }
}

checkTool().catch(console.error);
