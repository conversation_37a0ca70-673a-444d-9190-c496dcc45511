import { createClient } from '@supabase/supabase-js';
import { PromptManager } from '../src/lib/ai/prompt-manager';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function testFieldParsingFixes() {
  console.log('🧪 TESTING FIELD PARSING FIXES');
  console.log('=' .repeat(70));
  
  // Get the existing tool with generated content
  const { data: tool, error } = await supabaseAdmin
    .from('tools')
    .select('*')
    .eq('id', '84e9f98d-2d29-4cb5-8ac8-9ecc39c5d9ca')
    .single();
    
  if (error || !tool) {
    console.log('❌ Could not fetch test tool');
    return;
  }
  
  console.log(`\n📋 Testing with tool: ${tool.name}`);
  
  // Extract the AI-generated content from the JSONB field
  const aiGeneratedContent = tool.generated_content?.content;
  
  if (!aiGeneratedContent) {
    console.log('❌ No AI-generated content found in tool');
    return;
  }
  
  console.log('\n🔍 ORIGINAL AI-GENERATED CONTENT:');
  console.log('Fields in generated_content.content:');
  Object.keys(aiGeneratedContent).forEach(key => {
    const value = aiGeneratedContent[key];
    const type = Array.isArray(value) ? 'array' : typeof value;
    console.log(`   ${key}: ${type}`);
  });
  
  // Test the fixed PromptManager.processAIDudeResponse
  console.log('\n🔧 TESTING FIXED FIELD MAPPING:');
  const mappedContent = PromptManager.processAIDudeResponse(aiGeneratedContent);
  
  console.log('Mapped fields:');
  Object.keys(mappedContent).forEach(key => {
    const value = mappedContent[key];
    const type = Array.isArray(value) ? 'array' : typeof value;
    const preview = JSON.stringify(value)?.substring(0, 100);
    console.log(`   ${key}: ${type} - ${preview}...`);
  });
  
  // Test the specific problematic fields
  console.log('\n🎯 PROBLEMATIC FIELDS TEST:');
  
  const problematicFields = [
    { field: 'category_id', aiField: 'category_id', expected: 'Content Creation' },
    { field: 'subcategory', aiField: 'subcategory', expected: 'AI Tools' },
    { field: 'tooltip', aiField: 'tooltip', expected: 'Generate, optimize, and publish content in seconds.' }
  ];
  
  problematicFields.forEach(({ field, aiField, expected }) => {
    const aiValue = aiGeneratedContent[aiField];
    const mappedValue = mappedContent[field];
    
    console.log(`\n📋 ${field}:`);
    console.log(`   AI generated: ${JSON.stringify(aiValue)}`);
    console.log(`   Mapped value: ${JSON.stringify(mappedValue)}`);
    console.log(`   Expected: ${JSON.stringify(expected)}`);
    
    if (mappedValue === expected) {
      console.log(`   ✅ FIXED: Mapping working correctly`);
    } else if (mappedValue && mappedValue !== '') {
      console.log(`   ⚠️ PARTIAL: Mapped but different value`);
    } else {
      console.log(`   ❌ STILL BROKEN: Not mapped correctly`);
    }
  });
  
  // Test the validDatabaseFields filter
  console.log('\n🔍 VALID DATABASE FIELDS FILTER TEST:');
  
  const validDatabaseFields = [
    'name', 'description', 'short_description', 'detailed_description',
    'company', 'category_id', 'subcategory', 'features', 'pricing',
    'pros_and_cons', 'social_links', 'hashtags', 'tooltip', 'haiku', 'releases',
    'faqs', 'meta_title', 'meta_description', 'meta_keywords'
  ];
  
  const filteredFields: any = {};
  validDatabaseFields.forEach(field => {
    if (mappedContent[field] !== undefined && mappedContent[field] !== null && mappedContent[field] !== '') {
      filteredFields[field] = mappedContent[field];
    }
  });
  
  console.log(`📊 Mapped fields: ${Object.keys(mappedContent).length}`);
  console.log(`📊 Valid database fields: ${validDatabaseFields.length}`);
  console.log(`📊 Filtered fields: ${Object.keys(filteredFields).length}`);
  
  console.log('\nFiltered fields that will be stored:');
  Object.keys(filteredFields).forEach(key => {
    const value = filteredFields[key];
    const type = Array.isArray(value) ? 'array' : typeof value;
    console.log(`   ✅ ${key}: ${type}`);
  });
  
  // Check for missing fields
  const missingFields = validDatabaseFields.filter(field => 
    !filteredFields.hasOwnProperty(field) || 
    filteredFields[field] === undefined || 
    filteredFields[field] === null || 
    filteredFields[field] === ''
  );
  
  if (missingFields.length > 0) {
    console.log('\nMissing or empty fields:');
    missingFields.forEach(field => {
      console.log(`   ❌ ${field}`);
    });
  }
  
  // Test update simulation
  console.log('\n🧪 SIMULATING DATABASE UPDATE:');
  
  const updateData = {
    ...filteredFields,
    generated_content: tool.generated_content,
    ai_generation_status: 'completed',
    content_quality_score: 95,
    last_ai_update: new Date().toISOString()
  };
  
  console.log('Update data structure:');
  Object.keys(updateData).forEach(key => {
    const value = updateData[key];
    const type = Array.isArray(value) ? 'array' : typeof value;
    console.log(`   ${key}: ${type}`);
  });
  
  // Calculate success rate
  const expectedFields = ['category_id', 'subcategory', 'tooltip'];
  const fixedFields = expectedFields.filter(field => 
    filteredFields[field] && filteredFields[field] !== ''
  );
  
  const successRate = (fixedFields.length / expectedFields.length) * 100;
  
  console.log('\n🎯 FIX VERIFICATION SUMMARY:');
  console.log('=' .repeat(50));
  console.log(`📊 Previously problematic fields: ${expectedFields.length}`);
  console.log(`📊 Now correctly mapped: ${fixedFields.length}`);
  console.log(`📊 Fix success rate: ${successRate.toFixed(1)}%`);
  
  if (successRate === 100) {
    console.log('\n🎉 SUCCESS: All field parsing issues have been fixed!');
    console.log('✅ category_id mapping working');
    console.log('✅ subcategory mapping working');
    console.log('✅ tooltip field included in valid fields');
    console.log('✅ All workflow paths now parse content to individual columns');
  } else if (successRate >= 80) {
    console.log('\n⚠️ MOSTLY FIXED: Most issues resolved, some may remain');
    console.log('🔧 Review the missing fields above');
  } else {
    console.log('\n❌ STILL BROKEN: Significant issues remain');
    console.log('🔧 Additional fixes needed');
  }
  
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Restart the Next.js application to apply the fixes');
  console.log('2. Test with a new content generation job');
  console.log('3. Verify that all fields are parsed to individual database columns');
  console.log('4. Monitor both bulk processing and manual review workflows');
}

async function testWithMockData() {
  console.log('\n🧪 TESTING WITH MOCK AI RESPONSE');
  console.log('=' .repeat(50));
  
  // Create mock AI response that matches what OpenAI actually generates
  const mockAIResponse = {
    name: 'Test Tool',
    description: 'Test description for the tool',
    short_description: 'Short test description',
    detailed_description: 'Detailed description of the test tool with more information',
    company: 'Test Company',
    category_id: 'AI Tools',  // AI generates this directly
    subcategory: 'Content Creation',  // AI generates this directly
    tooltip: 'Test tooltip for the tool',  // AI generates this
    features: ['Feature 1', 'Feature 2', 'Feature 3'],
    pricing: {
      type: 'Freemium',
      plans: [
        { name: 'Free', price: '$0', features: ['Basic features'] },
        { name: 'Pro', price: '$10/month', features: ['All features'] }
      ],
      details: 'Freemium pricing model'
    },
    pros_and_cons: {
      pros: ['Easy to use', 'Great features'],
      cons: ['Limited free plan', 'Learning curve']
    },
    social_links: {
      twitter: 'https://twitter.com/testtool',
      linkedin: null,
      github: null
    },
    hashtags: ['#AI', '#Tools', '#Productivity'],
    haiku: {
      lines: ['AI helps us work', 'Faster than ever before', 'Future is bright'],
      theme: 'Productivity'
    },
    releases: [
      {
        version: '1.0.0',
        releaseDate: '2025-01-01',
        changes: ['Initial release', 'Basic features']
      }
    ],
    faqs: [
      {
        id: 'faq-1',
        question: 'How does it work?',
        answer: 'It uses AI to help you work faster',
        category: 'general'
      }
    ],
    meta_title: 'Test Tool - AI-Powered Solution',
    meta_description: 'Test tool description for SEO purposes',
    meta_keywords: 'test, tool, AI, productivity'
  };
  
  console.log('Mock AI response created with all expected fields');
  
  // Test the mapping
  const mappedContent = PromptManager.processAIDudeResponse(mockAIResponse);
  
  console.log('\n🔍 MAPPING TEST RESULTS:');
  
  const criticalFields = ['category_id', 'subcategory', 'tooltip'];
  criticalFields.forEach(field => {
    const originalValue = mockAIResponse[field as keyof typeof mockAIResponse];
    const mappedValue = mappedContent[field];
    
    console.log(`\n📋 ${field}:`);
    console.log(`   Original: ${JSON.stringify(originalValue)}`);
    console.log(`   Mapped: ${JSON.stringify(mappedValue)}`);
    
    if (mappedValue === originalValue) {
      console.log(`   ✅ PERFECT: Direct mapping working`);
    } else {
      console.log(`   ❌ ISSUE: Mapping not working correctly`);
    }
  });
  
  console.log(`\n📊 Total mapped fields: ${Object.keys(mappedContent).length}`);
  console.log('✅ Mock data test completed');
}

async function runAllTests() {
  try {
    await testFieldParsingFixes();
    await testWithMockData();
    
    console.log('\n🎯 ALL FIELD PARSING TESTS COMPLETED');
    console.log('=' .repeat(70));
    console.log('✅ Review the results above to verify fixes are working');
    console.log('✅ Restart the application to apply the changes');
    console.log('✅ Test with new content generation jobs');
    
  } catch (error) {
    console.error('❌ Field parsing tests failed:', error);
  }
}

runAllTests().catch(console.error);
