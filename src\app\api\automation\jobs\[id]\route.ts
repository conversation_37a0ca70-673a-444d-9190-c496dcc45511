import { NextRequest, NextResponse } from 'next/server';
import { getJobQueue, getJobManager } from '@/lib/jobs';
import { validateApi<PERSON>ey } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Use enhanced job system for better data
    const jobManager = getJobManager();
    const job = await jobManager.getJob(id);

    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: job.id,
        type: job.type,
        status: job.status,
        priority: job.priority,
        attempts: job.attempts,
        maxAttempts: job.maxAttempts,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
        scheduledFor: job.scheduledFor,
        completedAt: job.completedAt,
        error: job.error,
        result: job.result,
        data: job.data,
        progress: job.progress,
        progressDetails: job.progressDetails,
        toolId: job.toolId,
      },
    });
  } catch (error) {
    console.error('Error fetching job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch job' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const { action } = await request.json();

    if (!action) {
      return NextResponse.json(
        { success: false, error: 'Action is required' },
        { status: 400 }
      );
    }

    const queue = getJobQueue();

    switch (action) {
      case 'retry':
        try {
          const retriedJob = await queue.retryJob(id);
          return NextResponse.json({
            success: true,
            data: {
              id: retriedJob.id,
              status: retriedJob.status,
              attempts: retriedJob.attempts,
              updatedAt: retriedJob.updatedAt,
            },
            message: 'Job queued for retry successfully',
          });
        } catch (retryError) {
          console.error(`Retry job ${id} failed:`, retryError);
          return NextResponse.json(
            {
              success: false,
              error: retryError instanceof Error ? retryError.message : 'Failed to retry job',
              details: 'Check job status and try again. Some jobs cannot be retried while processing.'
            },
            { status: 400 }
          );
        }

      case 'pause':
        try {
          const jobManager = getJobManager();
          const pausedJob = await jobManager.pauseJob(id, { reason: 'Admin request' });
          return NextResponse.json({
            success: true,
            data: {
              id: pausedJob.id,
              status: pausedJob.status,
              updatedAt: pausedJob.updatedAt,
            },
          });
        } catch (error) {
          return NextResponse.json(
            { success: false, error: 'Failed to pause job' },
            { status: 500 }
          );
        }

      case 'resume':
        try {
          const jobManager = getJobManager();
          const resumedJob = await jobManager.resumeJob(id, { reason: 'Admin request' });
          return NextResponse.json({
            success: true,
            data: {
              id: resumedJob.id,
              status: resumedJob.status,
              updatedAt: resumedJob.updatedAt,
            },
          });
        } catch (error) {
          return NextResponse.json(
            { success: false, error: 'Failed to resume job' },
            { status: 500 }
          );
        }

      case 'stop':
        try {
          const jobManager = getJobManager();
          const stoppedJob = await jobManager.stopJob(id, { reason: 'Admin request' });
          return NextResponse.json({
            success: true,
            data: {
              id: stoppedJob.id,
              status: stoppedJob.status,
              updatedAt: stoppedJob.updatedAt,
            },
          });
        } catch (error) {
          return NextResponse.json(
            { success: false, error: 'Failed to stop job' },
            { status: 500 }
          );
        }

      case 'cancel':
        const job = await queue.getJob(id);
        if (!job) {
          return NextResponse.json(
            { success: false, error: 'Job not found' },
            { status: 404 }
          );
        }

        if (job.status === 'processing') {
          return NextResponse.json(
            { success: false, error: 'Cannot cancel job that is currently processing' },
            { status: 400 }
          );
        }

        job.status = 'cancelled' as any;
        job.updatedAt = new Date();

        return NextResponse.json({
          success: true,
          data: {
            id: job.id,
            status: job.status,
            updatedAt: job.updatedAt,
          },
        });

      case 'delete':
        try {
          const jobManager = getJobManager();
          const removed = await jobManager.deleteJob(id);

          if (!removed) {
            return NextResponse.json(
              { success: false, error: 'Job not found' },
              { status: 404 }
            );
          }

          return NextResponse.json({
            success: true,
            message: 'Job deleted successfully',
            data: {
              id: id,
              deleted: true
            }
          });
        } catch (error) {
          console.error('Error deleting job:', error);
          return NextResponse.json(
            { success: false, error: 'Failed to delete job' },
            { status: 500 }
          );
        }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error performing job action:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform job action' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Use enhanced job system for deletion
    const jobManager = getJobManager();
    const removed = await jobManager.deleteJob(id);

    if (!removed) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Job removed successfully',
    });
  } catch (error) {
    console.error('Error removing job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to remove job' },
      { status: 500 }
    );
  }
}
