/**
 * Enhanced Content Generation Pipeline with Automatic Triggers
 * 
 * Extends the existing pipeline with:
 * - Automatic webscraper triggering for missing scraped_data
 * - Automatic media collection triggering for missing assets
 * - Enhanced releases field support
 * - Integration with unified workflow system
 */

import { ContentGenerationPipeline } from './pipeline';
import { workflowManager } from '@/lib/workflow/workflow-manager';
import { getJobManager } from '@/lib/jobs';
import { JobType, JobPriority } from '@/lib/jobs/types';
import { supabase } from '@/lib/supabase';

export interface EnhancedGenerationRequest {
  toolId: string;
  url: string;
  autoTriggerScraping?: boolean;
  autoTriggerMedia?: boolean;
  includeReleases?: boolean;
  priority?: 'low' | 'normal' | 'high';
  methodology?: 'standard' | 'ai_dude';
}

export interface AutoTriggerResult {
  scrapingTriggered: boolean;
  mediaTriggered: boolean;
  scrapingJobId?: string;
  mediaJobId?: string;
  existingData?: any;
}

export class EnhancedContentGenerationPipeline extends ContentGenerationPipeline {
  private jobManager = getJobManager();

  /**
   * Enhanced content generation with automatic triggers
   */
  async generateWithAutoTriggers(request: EnhancedGenerationRequest): Promise<any> {
    try {
      // Check for missing data and trigger automatic collection
      const autoTriggerResult = await this.handleAutoTriggers(request);
      
      // Wait for auto-triggered jobs to complete if any
      if (autoTriggerResult.scrapingTriggered || autoTriggerResult.mediaTriggered) {
        await this.waitForAutoTriggerCompletion(autoTriggerResult);
      }

      // Get updated tool data after auto-triggers
      const toolData = await this.getToolData(request.toolId);
      
      // Proceed with content generation using enhanced methodology
      const generationRequest = {
        toolId: request.toolId,
        url: request.url,
        scrapedData: toolData.scraped_data,
        methodology: request.methodology || 'ai_dude',
        includeReleases: request.includeReleases !== false
      };

      return await this.generateContent(generationRequest);

    } catch (error) {
      console.error('Enhanced content generation failed:', error);
      throw error;
    }
  }

  /**
   * Handle automatic scraping and media collection triggers
   */
  private async handleAutoTriggers(request: EnhancedGenerationRequest): Promise<AutoTriggerResult> {
    const result: AutoTriggerResult = {
      scrapingTriggered: false,
      mediaTriggered: false
    };

    // Get current tool data
    const toolData = await this.getToolData(request.toolId);
    result.existingData = toolData;

    // Check if scraping is needed
    if (request.autoTriggerScraping !== false && this.needsScraping(toolData)) {
      result.scrapingJobId = await this.triggerWebscraping(request);
      result.scrapingTriggered = true;
    }

    // Check if media collection is needed
    if (request.autoTriggerMedia !== false && this.needsMediaCollection(toolData)) {
      result.mediaJobId = await this.triggerMediaCollection(request);
      result.mediaTriggered = true;
    }

    return result;
  }

  /**
   * Check if tool needs web scraping
   */
  private needsScraping(toolData: any): boolean {
    // No scraped data or data is older than 30 days
    if (!toolData.scraped_data) {
      return true;
    }

    if (toolData.last_scraped_at) {
      const lastScraped = new Date(toolData.last_scraped_at);
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      return lastScraped < thirtyDaysAgo;
    }

    return true;
  }

  /**
   * Check if tool needs media collection
   */
  private needsMediaCollection(toolData: any): boolean {
    // Missing logo or screenshots
    return !toolData.logo_url || !toolData.screenshots || 
           (Array.isArray(toolData.screenshots) && toolData.screenshots.length === 0);
  }

  /**
   * Trigger web scraping job
   */
  private async triggerWebscraping(request: EnhancedGenerationRequest): Promise<string> {
    const job = await this.jobManager.createJob(
      JobType.WEB_SCRAPING,
      {
        toolId: request.toolId,
        url: request.url,
        options: {
          render: true,
          waitUntil: 'networkidle0',
          extractContent: true,
          extractMedia: false // Media handled separately
        }
      },
      {
        priority: request.priority === 'high' ? JobPriority.HIGH : JobPriority.NORMAL
      }
    );

    console.log(`🕷️ Auto-triggered web scraping job ${job.id} for tool ${request.toolId}`);
    return job.id;
  }

  /**
   * Trigger media collection job
   */
  private async triggerMediaCollection(request: EnhancedGenerationRequest): Promise<string> {
    const job = await this.jobManager.createJob(
      JobType.MEDIA_COLLECTION,
      {
        toolId: request.toolId,
        url: request.url,
        options: {
          extractFavicon: true,
          extractScreenshots: true,
          extractOgImages: true,
          maxScreenshots: 3
        }
      },
      {
        priority: request.priority === 'high' ? JobPriority.HIGH : JobPriority.NORMAL
      }
    );

    console.log(`📸 Auto-triggered media collection job ${job.id} for tool ${request.toolId}`);
    return job.id;
  }

  /**
   * Wait for auto-triggered jobs to complete
   */
  private async waitForAutoTriggerCompletion(result: AutoTriggerResult): Promise<void> {
    const jobsToWait = [];
    
    if (result.scrapingJobId) {
      jobsToWait.push(result.scrapingJobId);
    }
    
    if (result.mediaJobId) {
      jobsToWait.push(result.mediaJobId);
    }

    if (jobsToWait.length === 0) {
      return;
    }

    console.log(`⏳ Waiting for ${jobsToWait.length} auto-triggered jobs to complete...`);

    // Wait for jobs with timeout
    const timeout = 60000; // 60 seconds
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const completedJobs = [];
      
      for (const jobId of jobsToWait) {
        const job = await this.jobManager.getJob(jobId);
        if (job && (job.status === 'completed' || job.status === 'failed')) {
          completedJobs.push(jobId);
        }
      }

      // Remove completed jobs
      completedJobs.forEach(jobId => {
        const index = jobsToWait.indexOf(jobId);
        if (index > -1) {
          jobsToWait.splice(index, 1);
        }
      });

      // All jobs completed
      if (jobsToWait.length === 0) {
        console.log('✅ All auto-triggered jobs completed');
        return;
      }

      // Wait 2 seconds before checking again
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.warn('⚠️ Auto-triggered jobs did not complete within timeout, proceeding anyway');
  }

  /**
   * Get tool data from database
   */
  private async getToolData(toolId: string): Promise<any> {
    const { data, error } = await supabase
      .from('tools')
      .select('*')
      .eq('id', toolId)
      .single();

    if (error) {
      throw new Error(`Failed to get tool data: ${error.message}`);
    }

    return data;
  }

  /**
   * Enhanced content generation with releases support
   */
  async generateContent(request: any): Promise<any> {
    // Add releases field to database schema if not present
    const enhancedSchema = this.getEnhancedDatabaseSchema(request.includeReleases);
    
    // Use enhanced schema for generation
    const originalRequest = {
      ...request,
      databaseSchema: enhancedSchema
    };

    return await super.generateContent(originalRequest);
  }

  /**
   * Get enhanced database schema with releases field
   */
  private getEnhancedDatabaseSchema(includeReleases: boolean = true): any {
    const baseSchema = {
      name: "string (required, max 255 chars)",
      description: "string (required, brief description)",
      short_description: "string (required, max 150 chars for cards)",
      detailed_description: "string (required, 150-300 words)",
      company: "string (required, company/organization name)",
      main_category: "string (required, primary category)",
      sub_category: "string (optional, secondary category)",
      category_confidence: "number (required, 0.0-1.0 confidence score)",
      features: ["array of 3-8 feature strings (required)"],
      pricing: {
        type: "Free|Paid|Freemium|Open Source (required)",
        plans: [{"name": "string", "price": "string", "features": ["array"]}],
        details: "string (pricing description)"
      },
      pros_and_cons: {
        pros: ["array of 3-10 pros (required)"],
        cons: ["array of 3-10 cons (required)"]
      },
      social_links: {
        twitter: "url or null",
        linkedin: "url or null",
        github: "url or null",
        facebook: "url or null",
        youtube: "url or null"
      },
      hashtags: ["array of 5-10 hashtags (required)"],
      haiku: {
        lines: ["line1", "line2", "line3"],
        theme: "string"
      },
      faqs: [
        {
          id: "uuid",
          question: "string (required)",
          answer: "string (required)",
          category: "general|pricing|features|support|getting-started",
          displayOrder: "number",
          priority: "number (1-10)",
          isActive: true,
          isFeatured: "boolean",
          source: "ai_generated",
          sourceMetadata: {
            aiModel: "string",
            confidence: "number (0.0-1.0)"
          },
          metaKeywords: "string"
        }
      ],
      meta_title: "string (required, max 60 chars, SEO optimized)",
      meta_description: "string (required, 150-160 chars, SEO optimized)",
      meta_keywords: "string (future implementation, SEO keywords comma-separated)"
    };

    // Add releases field if requested
    if (includeReleases) {
      baseSchema.releases = [
        {
          version: "string (e.g., '2.1.0')",
          releaseDate: "YYYY-MM-DD",
          changes: ["array of change descriptions"],
          type: "major|minor|patch|hotfix",
          notes: "string (optional release notes)"
        }
      ];
    }

    return baseSchema;
  }

  /**
   * Bulk content generation with auto-triggers
   */
  async bulkGenerateWithAutoTriggers(requests: EnhancedGenerationRequest[]): Promise<any[]> {
    const results = [];
    
    // Process in batches to avoid overwhelming the system
    const batchSize = 3;
    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      
      const batchPromises = batch.map(request => 
        this.generateWithAutoTriggers(request).catch(error => ({
          toolId: request.toolId,
          success: false,
          error: error.message
        }))
      );
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Small delay between batches
      if (i + batchSize < requests.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return results;
  }
}
