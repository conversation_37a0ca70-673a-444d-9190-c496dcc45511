#!/usr/bin/env tsx

/**
 * Test Tool Creation Fix
 *
 * This script tests the tool creation manager's ability to handle
 * duplicate name conflicts by updating existing tools.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST before any other imports
dotenv.config({ path: '.env.local' });

// Now import modules that depend on environment variables
import { getToolCreationManager } from '../src/lib/bulk-processing/tool-creation-manager';

async function testToolCreationFix() {
  console.log('🧪 Testing Tool Creation Fix...\n');

  try {
    const toolManager = getToolCreationManager();

    // Test data for FoodiePrep
    const testToolData = {
      url: 'https://www.foodieprep.ai/',
      providedData: {
        name: 'Foodieprep',
        description: 'AI-powered meal planning and recipe generation tool'
      }
    };

    console.log('🔧 Testing tool creation/update for FoodiePrep...');
    console.log(`📍 URL: ${testToolData.url}`);
    console.log(`📝 Name: ${testToolData.providedData.name}`);
    console.log('');

    // This should either:
    // 1. Find existing tool by URL and return its ID
    // 2. Find existing tool by name and update it with new URL
    // 3. Create new tool if neither exists
    // 4. Handle any constraint violations gracefully
    
    const toolId = await toolManager.ensureToolExists(testToolData);
    
    console.log('✅ Tool creation/update successful!');
    console.log(`🆔 Tool ID: ${toolId}`);
    console.log('');

    // Test with a slightly different URL to see if it finds by name
    const testToolData2 = {
      url: 'http://www.foodieprep.ai/', // Different protocol
      providedData: {
        name: 'Foodieprep',
        description: 'Updated description for FoodiePrep'
      }
    };

    console.log('🔧 Testing with different URL format...');
    console.log(`📍 URL: ${testToolData2.url}`);
    console.log(`📝 Name: ${testToolData2.providedData.name}`);
    console.log('');

    const toolId2 = await toolManager.ensureToolExists(testToolData2);
    
    console.log('✅ Second test successful!');
    console.log(`🆔 Tool ID: ${toolId2}`);
    console.log('');

    if (toolId === toolId2) {
      console.log('🎯 SUCCESS: Both requests returned the same tool ID');
      console.log('   This means the system correctly identified the existing tool');
      console.log('   and updated it instead of creating a duplicate.');
    } else {
      console.log('⚠️ NOTICE: Different tool IDs returned');
      console.log('   This might be expected if the tools are genuinely different');
      console.log(`   Tool 1: ${toolId}`);
      console.log(`   Tool 2: ${toolId2}`);
    }

    console.log('');
    console.log('🎯 Test Summary:');
    console.log('   ✅ No constraint violation errors');
    console.log('   ✅ Tool creation/update completed successfully');
    console.log('   ✅ System handles duplicate names gracefully');
    console.log('');
    console.log('💡 The bulk processing should now work for FoodiePrep!');

  } catch (error) {
    console.error('💥 Test failed:', error);
    console.error('');
    console.error('🔍 Error details:');
    console.error('   Type:', error instanceof Error ? error.constructor.name : typeof error);
    console.error('   Message:', error instanceof Error ? error.message : String(error));
    
    if (error instanceof Error && error.stack) {
      console.error('   Stack:', error.stack);
    }
    
    console.error('');
    console.error('🔧 This indicates the fix may need further refinement.');
  }
}

// Run the test
testToolCreationFix().catch(console.error);
