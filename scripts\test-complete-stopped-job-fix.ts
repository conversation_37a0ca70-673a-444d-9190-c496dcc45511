#!/usr/bin/env tsx

/**
 * Test Complete Stopped Job Fix
 * 
 * This script tests that stopped jobs can now be properly retried
 * after fixing both UI and backend logic.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function testCompleteStoppedJobFix() {
  console.log('🧪 Testing Complete Stopped Job Fix...\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. Find the stopped job from previous test
    console.log('1. 🔍 Finding stopped jobs...');
    const { data: stoppedJobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .eq('status', 'stopped')
      .order('updated_at', { ascending: false })
      .limit(5);

    if (jobsError) {
      throw new Error(`Failed to fetch stopped jobs: ${jobsError.message}`);
    }

    if (!stoppedJobs || stoppedJobs.length === 0) {
      console.log('❌ No stopped jobs found to test with');
      console.log('   Creating a test scenario by checking other job statuses...');
      
      // Check what jobs we have
      const { data: allJobs } = await supabase
        .from('ai_generation_jobs')
        .select('id, status, job_type')
        .order('updated_at', { ascending: false })
        .limit(10);
        
      if (allJobs && allJobs.length > 0) {
        console.log('\n   Available jobs for testing:');
        allJobs.forEach((job, index) => {
          console.log(`      ${index + 1}. ${job.id} - ${job.status} (${job.job_type})`);
        });
      }
      return;
    }

    console.log(`✅ Found ${stoppedJobs.length} stopped job(s):\n`);

    // 2. Test each stopped job
    for (const job of stoppedJobs) {
      console.log(`📋 Testing stopped job: ${job.id}`);
      console.log(`   📊 Status: ${job.status}`);
      console.log(`   📋 Type: ${job.job_type}`);
      console.log(`   📅 Updated: ${job.updated_at}`);
      console.log(`   🔄 Attempts: ${job.attempts}/${job.max_attempts}`);

      // Test retry action
      console.log('\n   🧪 Testing retry action...');
      try {
        const retryResponse = await fetch(`http://localhost:3000/api/automation/jobs/${job.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789',
          },
          body: JSON.stringify({ action: 'retry' }),
        });

        if (retryResponse.ok) {
          const result = await retryResponse.json();
          console.log('   ✅ Retry action successful!');
          console.log(`      📊 New Status: ${result.data.status}`);
          console.log(`      🔄 New Attempts: ${result.data.attempts}`);
          console.log(`      📅 Updated: ${result.data.updatedAt}`);
          
          // Verify in database
          const { data: updatedJob } = await supabase
            .from('ai_generation_jobs')
            .select('status, attempts, updated_at')
            .eq('id', job.id)
            .single();
            
          if (updatedJob) {
            console.log(`      ✅ Database verification: Status is now "${updatedJob.status}"`);
            if (updatedJob.status === 'pending') {
              console.log('      🎯 Perfect! Job is ready to be processed again');
            }
          }
          
        } else {
          const errorData = await retryResponse.json().catch(() => ({}));
          console.log(`   ❌ Retry action failed: ${errorData.error || `HTTP ${retryResponse.status}`}`);
          console.log('      This indicates the backend fix may not be working');
        }
      } catch (error) {
        console.log(`   ❌ Retry test failed: ${error}`);
      }

      // Test resume action (should still fail)
      console.log('\n   🧪 Testing resume action (should fail)...');
      try {
        const resumeResponse = await fetch(`http://localhost:3000/api/automation/jobs/${job.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789',
          },
          body: JSON.stringify({ action: 'resume' }),
        });

        if (resumeResponse.ok) {
          console.log('   ❌ ERROR: Resume should not work for stopped jobs!');
        } else {
          console.log('   ✅ Resume correctly rejected (as expected)');
        }
      } catch (error) {
        console.log(`   ✅ Resume correctly failed: ${error}`);
      }

      console.log('\n' + '─'.repeat(60) + '\n');
    }

    console.log('🎯 Summary of Fixes Applied:');
    console.log('');
    console.log('✅ UI Fixes:');
    console.log('   • JobListTable: Added explicit case for STOPPED jobs');
    console.log('   • JobDetailsModal: Added explicit case for STOPPED jobs');
    console.log('   • Stopped jobs now show: retry, delete (NOT resume)');
    console.log('');
    console.log('✅ Backend Fixes:');
    console.log('   • Removed restriction preventing retry of stopped jobs');
    console.log('   • Added retry strategy for stopped jobs (reset attempts)');
    console.log('   • Backend now allows retrying manually stopped jobs');
    console.log('');
    console.log('🚀 Expected Behavior:');
    console.log('   • Stopped jobs show retry/delete buttons in UI');
    console.log('   • Clicking retry on stopped jobs works without errors');
    console.log('   • Jobs are reset to pending status with fresh attempts');
    console.log('   • Resume is still correctly rejected for stopped jobs');

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
testCompleteStoppedJobFix().catch(console.error);
