#!/usr/bin/env tsx

/**
 * Fix Submission Source Detection
 * 
 * This script fixes the submission source detection issue by ensuring
 * all tools created through bulk processing have the correct submission_source.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function fixSubmissionSourceDetection() {
  console.log('🔧 FIXING SUBMISSION SOURCE DETECTION\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. Find all tools that should be bulk processing but aren't marked correctly
    console.log('1. 🔍 Finding tools that need submission_source fixes...');
    
    // Look for tools that were likely created through bulk processing but don't have the right source
    const { data: suspiciousTools, error: suspiciousError } = await supabase
      .from('tools')
      .select('*')
      .or('submission_source.is.null,submission_source.neq.bulk_processing')
      .eq('submission_type', 'admin')
      .order('created_at', { ascending: false })
      .limit(20);

    if (suspiciousError) {
      throw new Error(`Failed to fetch suspicious tools: ${suspiciousError.message}`);
    }

    console.log(`✅ Found ${suspiciousTools?.length || 0} tools to check\n`);

    // 2. Check each tool and fix if needed
    let fixedCount = 0;
    const toolsToFix = [];

    if (suspiciousTools && suspiciousTools.length > 0) {
      for (const tool of suspiciousTools) {
        console.log(`📋 Checking: ${tool.name} (${tool.id})`);
        console.log(`   📊 Current submission_source: ${tool.submission_source || 'null'}`);
        console.log(`   📝 Submission type: ${tool.submission_type}`);
        console.log(`   📅 Created: ${tool.created_at}`);

        // Criteria for bulk processing tools:
        // 1. submission_type = 'admin'
        // 2. Created recently (likely through bulk processing)
        // 3. Has basic description pattern or specific URLs we know are from bulk processing
        const isBulkProcessingCandidate = 
          tool.submission_type === 'admin' &&
          (tool.description?.includes('AI tool at') || 
           tool.website?.includes('foodieprep.ai') ||
           tool.name?.includes('FoodiePrep'));

        if (isBulkProcessingCandidate && tool.submission_source !== 'bulk_processing') {
          console.log(`   🔧 NEEDS FIX: Should be bulk_processing`);
          toolsToFix.push(tool);
        } else {
          console.log(`   ✅ OK: Correctly configured`);
        }
        console.log('');
      }

      // 3. Fix the tools that need it
      if (toolsToFix.length > 0) {
        console.log(`2. 🔧 Fixing ${toolsToFix.length} tools...\n`);

        for (const tool of toolsToFix) {
          console.log(`   Fixing: ${tool.name} (${tool.id})`);
          
          const { error: updateError } = await supabase
            .from('tools')
            .update({
              submission_source: 'bulk_processing',
              updated_at: new Date().toISOString()
            })
            .eq('id', tool.id);

          if (updateError) {
            console.log(`   ❌ Failed to update: ${updateError.message}`);
          } else {
            console.log(`   ✅ Updated to bulk_processing`);
            fixedCount++;
          }
        }
      } else {
        console.log('2. ✅ No tools need fixing');
      }
    }

    // 4. Specifically check and fix the tool from the logs
    const specificToolId = '34f7391e-76bb-4ac8-9a48-496a9b6a3d1e';
    console.log(`\n3. 🎯 Checking specific tool from logs: ${specificToolId}`);
    
    const { data: specificTool, error: specificError } = await supabase
      .from('tools')
      .select('*')
      .eq('id', specificToolId)
      .single();

    if (specificError) {
      console.log(`   ❌ Tool not found: ${specificError.message}`);
    } else if (specificTool) {
      console.log(`   ✅ Found: ${specificTool.name}`);
      console.log(`   📋 Current submission_source: ${specificTool.submission_source}`);
      
      if (specificTool.submission_source !== 'bulk_processing') {
        console.log(`   🔧 Fixing specific tool...`);
        
        const { error: fixError } = await supabase
          .from('tools')
          .update({
            submission_source: 'bulk_processing',
            submission_type: 'admin',
            updated_at: new Date().toISOString()
          })
          .eq('id', specificToolId);

        if (fixError) {
          console.log(`   ❌ Failed to fix: ${fixError.message}`);
        } else {
          console.log(`   ✅ Fixed specific tool`);
          fixedCount++;
        }
      } else {
        console.log(`   ✅ Already correctly configured`);
      }
    }

    // 5. Summary
    console.log('\n🎯 Summary:');
    console.log(`   📊 Tools checked: ${suspiciousTools?.length || 0}`);
    console.log(`   🔧 Tools fixed: ${fixedCount}`);
    console.log(`   ✅ Tools now have correct submission_source`);
    
    if (fixedCount > 0) {
      console.log('\n🚀 Next Steps:');
      console.log('   1. Process tools through bulk processing again');
      console.log('   2. Should now see "🚀 Bulk processing detected" message');
      console.log('   3. Tools should be published directly without manual review');
    }

    // 6. Verify the fix by checking the pipeline detection logic
    console.log('\n4. 🧪 Testing pipeline detection logic...');
    
    if (specificTool) {
      // Simulate the pipeline detection
      const { data: testTool, error: testError } = await supabase
        .from('tools')
        .select('submission_source')
        .eq('id', specificToolId)
        .single();

      if (testError) {
        console.log(`   ❌ Test failed: ${testError.message}`);
      } else {
        const isBulkProcessing = testTool.submission_source === 'bulk_processing';
        console.log(`   📋 Tool submission_source: ${testTool.submission_source}`);
        console.log(`   🎯 Pipeline detection result: ${isBulkProcessing ? 'BULK PROCESSING' : 'USER SUBMISSION'}`);
        
        if (isBulkProcessing) {
          console.log(`   ✅ Pipeline should now detect bulk processing correctly`);
        } else {
          console.log(`   ❌ Pipeline will still default to user submission`);
        }
      }
    }

  } catch (error) {
    console.error('💥 Fix failed:', error);
  }
}

// Run the fix
fixSubmissionSourceDetection().catch(console.error);
