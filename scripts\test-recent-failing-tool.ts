import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testRecentFailingTool() {
  const toolId = '960612f1-d311-4bc9-babc-d66d67565067';
  
  console.log('🔍 Testing tool that failed in recent logs...');
  console.log(`Tool ID: ${toolId}`);
  
  const { data: tools, error } = await supabase
    .from('tools')
    .select('submission_source, submission_type, name, website, id, created_at')
    .eq('id', toolId);
    
  if (error) {
    console.log('❌ Error:', error.message);
  } else if (tools && tools.length > 0) {
    console.log('✅ Tool exists:', tools[0]);
    console.log('This confirms the race condition - tool exists but pipeline lookup failed');
    
    // Test our enhanced race condition fix
    console.log('\n🧪 Testing enhanced race condition fix...');
    const result = await testEnhancedLookup(toolId);
    console.log(`Result: ${result ? 'SUCCESS - would be detected as bulk processing' : 'FAILED - would default to user submission'}`);
  } else {
    console.log('❌ Tool not found');
  }
}

// Enhanced lookup with race condition fixes
async function testEnhancedLookup(toolId: string): Promise<boolean> {
  const maxRetries = 5;
  const retryDelay = 1000;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`  🔍 Attempt ${attempt}/${maxRetries}`);

      if (attempt === 1) {
        await new Promise(resolve => setTimeout(resolve, 750)); // Initial delay
      }

      const { data: tools, error: queryError } = await supabase
        .from('tools')
        .select('submission_source, submission_type, name, website, id')
        .eq('id', toolId.trim());

      if (queryError) {
        console.log(`  ❌ Database error: ${queryError.message}`);
        if (attempt === maxRetries) return false;
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        continue;
      }

      if (!tools || tools.length === 0) {
        console.log(`  ❌ Tool not found`);
        if (attempt < maxRetries) {
          console.log(`  🔄 Retrying in ${retryDelay * attempt}ms...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
          continue;
        }
        return false;
      }

      const tool = tools[0];
      const isBulkProcessing = tool.submission_source === 'bulk_processing';
      console.log(`  ✅ Found: ${tool.name} - ${tool.submission_source}`);
      console.log(`  🎯 Result: ${isBulkProcessing ? 'BULK PROCESSING' : 'USER SUBMISSION'}`);
      return isBulkProcessing;
    } catch (error) {
      console.log(`  💥 Exception: ${error}`);
      if (attempt === maxRetries) return false;
      await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
    }
  }

  return false;
}

testRecentFailingTool().catch(console.error);
