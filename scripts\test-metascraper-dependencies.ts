/**
 * Test script to verify all metascraper dependencies are properly installed
 * and functional for the media collection refactor implementation.
 */

async function testMetascraperDependencies() {
  console.log('🧪 TESTING METASCRAPER DEPENDENCIES');
  console.log('=' .repeat(70));
  
  // Test 1: Package Installation Verification
  console.log('\n📦 PACKAGE INSTALLATION VERIFICATION');
  console.log('=' .repeat(50));
  
  const requiredPackages = [
    'metascraper',
    'metascraper-logo', 
    'metascraper-logo-favicon',
    'metascraper-image'
  ];
  
  console.log('✅ Required packages for media collection:');
  requiredPackages.forEach((pkg, index) => {
    console.log(`   ${index + 1}. ${pkg}`);
  });
  
  // Test 2: Import Testing
  console.log('\n🔧 IMPORT TESTING');
  console.log('=' .repeat(50));
  
  const importResults: { package: string; status: string; error?: string }[] = [];
  
  // Test metascraper core
  try {
    const metascraper = await import('metascraper');
    importResults.push({
      package: 'metascraper',
      status: 'SUCCESS',
    });
    console.log('✅ metascraper: Import successful');
    console.log(`   Type: ${typeof metascraper.default}`);
    console.log(`   Is function: ${typeof metascraper.default === 'function'}`);
  } catch (error) {
    importResults.push({
      package: 'metascraper',
      status: 'FAILED',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log('❌ metascraper: Import failed');
    console.log(`   Error: ${error}`);
  }
  
  // Test metascraper-logo
  try {
    const metascraperLogo = await import('metascraper-logo');
    importResults.push({
      package: 'metascraper-logo',
      status: 'SUCCESS',
    });
    console.log('✅ metascraper-logo: Import successful');
    console.log(`   Type: ${typeof metascraperLogo.default}`);
    console.log(`   Is function: ${typeof metascraperLogo.default === 'function'}`);
  } catch (error) {
    importResults.push({
      package: 'metascraper-logo',
      status: 'FAILED',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log('❌ metascraper-logo: Import failed');
    console.log(`   Error: ${error}`);
  }
  
  // Test metascraper-logo-favicon
  try {
    const metascraperLogoFavicon = await import('metascraper-logo-favicon');
    importResults.push({
      package: 'metascraper-logo-favicon',
      status: 'SUCCESS',
    });
    console.log('✅ metascraper-logo-favicon: Import successful');
    console.log(`   Type: ${typeof metascraperLogoFavicon.default}`);
    console.log(`   Is function: ${typeof metascraperLogoFavicon.default === 'function'}`);
  } catch (error) {
    importResults.push({
      package: 'metascraper-logo-favicon',
      status: 'FAILED',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log('❌ metascraper-logo-favicon: Import failed');
    console.log(`   Error: ${error}`);
  }
  
  // Test metascraper-image
  try {
    const metascraperImage = await import('metascraper-image');
    importResults.push({
      package: 'metascraper-image',
      status: 'SUCCESS',
    });
    console.log('✅ metascraper-image: Import successful');
    console.log(`   Type: ${typeof metascraperImage.default}`);
    console.log(`   Is function: ${typeof metascraperImage.default === 'function'}`);
  } catch (error) {
    importResults.push({
      package: 'metascraper-image',
      status: 'FAILED',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.log('❌ metascraper-image: Import failed');
    console.log(`   Error: ${error}`);
  }
  
  // Test 3: Metascraper Configuration Test
  console.log('\n🔧 METASCRAPER CONFIGURATION TEST');
  console.log('=' .repeat(50));
  
  try {
    const metascraper = (await import('metascraper')).default;
    const metascraperLogo = (await import('metascraper-logo')).default;
    const metascraperLogoFavicon = (await import('metascraper-logo-favicon')).default;
    const metascraperImage = (await import('metascraper-image')).default;
    
    // Test scraper configuration (same as MediaCollectionJob)
    const scraper = metascraper([
      metascraperLogo(),
      metascraperLogoFavicon(),
      metascraperImage()
    ]);
    
    console.log('✅ Metascraper configuration successful');
    console.log(`   Scraper type: ${typeof scraper}`);
    console.log(`   Is function: ${typeof scraper === 'function'}`);
    console.log('   Plugins configured: logo, logo-favicon, image');
    
  } catch (error) {
    console.log('❌ Metascraper configuration failed');
    console.log(`   Error: ${error}`);
  }
  
  // Test 4: Functional Test with Sample HTML
  console.log('\n🔧 FUNCTIONAL TEST WITH SAMPLE HTML');
  console.log('=' .repeat(50));
  
  try {
    const metascraper = (await import('metascraper')).default;
    const metascraperLogo = (await import('metascraper-logo')).default;
    const metascraperLogoFavicon = (await import('metascraper-logo-favicon')).default;
    const metascraperImage = (await import('metascraper-image')).default;
    
    const scraper = metascraper([
      metascraperLogo(),
      metascraperLogoFavicon(),
      metascraperImage()
    ]);
    
    // Sample HTML with meta tags
    const sampleHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Test Page</title>
        <meta property="og:image" content="https://example.com/og-image.jpg" />
        <meta property="og:logo" content="https://example.com/logo.png" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="shortcut icon" href="https://example.com/favicon.png" />
      </head>
      <body>
        <h1>Test Page</h1>
      </body>
      </html>
    `;
    
    const metadata = await scraper({ 
      html: sampleHtml, 
      url: 'https://example.com' 
    });
    
    console.log('✅ Functional test successful');
    console.log('   Extracted metadata:');
    console.log(`     Logo: ${metadata.logo || 'Not found'}`);
    console.log(`     Image: ${metadata.image || 'Not found'}`);
    console.log(`     Title: ${metadata.title || 'Not found'}`);
    
  } catch (error) {
    console.log('❌ Functional test failed');
    console.log(`   Error: ${error}`);
  }
  
  // Test 5: MediaCollectionJob Import Test
  console.log('\n🔧 MEDIA COLLECTION JOB IMPORT TEST');
  console.log('=' .repeat(50));
  
  try {
    // Test if MediaCollectionJob can be imported (this tests the actual implementation)
    const { MediaCollectionJob } = await import('../src/lib/jobs/media-collection-job');
    
    console.log('✅ MediaCollectionJob import successful');
    console.log(`   Type: ${typeof MediaCollectionJob}`);
    console.log(`   Is class: ${typeof MediaCollectionJob === 'function'}`);
    console.log('   MediaCollectionJob implementation can access metascraper dependencies');
    
  } catch (error) {
    console.log('❌ MediaCollectionJob import failed');
    console.log(`   Error: ${error}`);
    console.log('   This indicates an issue with the MediaCollectionJob implementation');
  }
  
  // Test 6: Version Compatibility Check
  console.log('\n🔧 VERSION COMPATIBILITY CHECK');
  console.log('=' .repeat(50));
  
  try {
    const fs = require('fs');
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    const versions = {
      'metascraper': packageJson.dependencies['metascraper'],
      'metascraper-logo': packageJson.dependencies['metascraper-logo'],
      'metascraper-logo-favicon': packageJson.dependencies['metascraper-logo-favicon'],
      'metascraper-image': packageJson.dependencies['metascraper-image']
    };
    
    console.log('✅ Package versions from package.json:');
    Object.entries(versions).forEach(([pkg, version]) => {
      console.log(`   ${pkg}: ${version}`);
    });
    
    // Check for version consistency
    const pluginVersions = [
      versions['metascraper-logo'],
      versions['metascraper-logo-favicon'], 
      versions['metascraper-image']
    ];
    
    const allSameVersion = pluginVersions.every(v => v === pluginVersions[0]);
    
    if (allSameVersion) {
      console.log('✅ All plugin versions are consistent');
    } else {
      console.log('⚠️ Plugin versions are inconsistent (may cause issues)');
    }
    
  } catch (error) {
    console.log('❌ Version check failed');
    console.log(`   Error: ${error}`);
  }
  
  // Summary
  console.log('\n📊 DEPENDENCY VERIFICATION SUMMARY');
  console.log('=' .repeat(50));
  
  const successfulImports = importResults.filter(r => r.status === 'SUCCESS').length;
  const totalPackages = importResults.length;
  const successRate = (successfulImports / totalPackages) * 100;
  
  console.log(`Import Success Rate: ${successRate}% (${successfulImports}/${totalPackages})`);
  
  importResults.forEach((result, index) => {
    const icon = result.status === 'SUCCESS' ? '✅' : '❌';
    console.log(`\n${index + 1}. ${icon} ${result.package}: ${result.status}`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  if (successRate === 100) {
    console.log('\n🎉 ALL METASCRAPER DEPENDENCIES VERIFIED SUCCESSFULLY!');
    console.log('✅ All required packages are installed and functional');
    console.log('✅ Imports work correctly');
    console.log('✅ Configuration is valid');
    console.log('✅ Functional test passed');
    console.log('✅ MediaCollectionJob can access dependencies');
    console.log('✅ Ready for media collection implementation');
  } else {
    console.log('\n⚠️ SOME DEPENDENCIES HAVE ISSUES');
    console.log('❌ Check failed imports above');
    console.log('❌ May need to reinstall packages');
  }
  
  console.log('\n📋 NEXT STEPS:');
  if (successRate === 100) {
    console.log('1. Dependencies are ready - no action needed');
    console.log('2. MediaCollectionJob should work correctly');
    console.log('3. Test media collection with real URLs');
  } else {
    console.log('1. Fix failed package imports');
    console.log('2. Reinstall problematic packages');
    console.log('3. Re-run this test to verify fixes');
  }
}

testMetascraperDependencies().catch(console.error);
