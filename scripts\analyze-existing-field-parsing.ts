import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Complete expected schema based on AI Dude implementation
const EXPECTED_AI_FIELDS = {
  // Core identification fields
  name: 'string',
  description: 'string', 
  short_description: 'string',
  detailed_description: 'string',
  
  // Company and categorization
  company: 'string',
  category_id: 'string',
  subcategory: 'string',
  
  // Core content fields
  features: 'array',
  pricing: 'object',
  pros_and_cons: 'object',
  
  // Social and external links
  social_links: 'object',
  
  // Additional content
  hashtags: 'array',
  tooltip: 'string',
  haiku: 'object',
  releases: 'array',
  
  // FAQ system
  faqs: 'array',
  
  // SEO fields
  meta_title: 'string',
  meta_description: 'string',
  meta_keywords: 'string',
  
  // System metadata
  generated_content: 'object'
};

const EXPECTED_NESTED_STRUCTURES = {
  pricing: ['type', 'plans', 'details'],
  pros_and_cons: ['pros', 'cons'],
  haiku: ['lines', 'theme'],
  social_links: ['twitter', 'linkedin', 'github', 'facebook', 'youtube'],
  generated_content: ['methodology', 'generated_at', 'model_used', 'quality_score', 'fields_generated']
};

async function analyzeExistingFieldParsing() {
  console.log('🔍 ANALYZING EXISTING FIELD PARSING');
  console.log('=' .repeat(70));
  
  // Get recent tools with AI-generated content
  console.log('\n📋 Step 1: Fetching recent AI-generated tools...');
  
  const { data: tools, error } = await supabaseAdmin
    .from('tools')
    .select('*')
    .not('generated_content', 'is', null)
    .order('created_at', { ascending: false })
    .limit(5);
    
  if (error) {
    console.error('❌ Failed to fetch tools:', error.message);
    return;
  }
  
  if (!tools || tools.length === 0) {
    console.log('❌ No tools with generated content found');
    return;
  }
  
  console.log(`✅ Found ${tools.length} tools with AI-generated content`);
  
  // Analyze each tool
  const analysisResults = [];
  
  for (let i = 0; i < tools.length; i++) {
    const tool = tools[i];
    console.log(`\n📋 Analyzing Tool ${i + 1}: ${tool.name} (${tool.id})`);
    console.log(`   Created: ${tool.created_at}`);
    console.log(`   Submission Source: ${tool.submission_source}`);
    console.log(`   AI Status: ${tool.ai_generation_status}`);
    
    const analysis = await analyzeToolFieldParsing(tool);
    analysisResults.push({
      tool_id: tool.id,
      tool_name: tool.name,
      ...analysis
    });
  }
  
  // Generate comprehensive report
  console.log('\n' + '=' .repeat(70));
  console.log('📊 COMPREHENSIVE FIELD PARSING ANALYSIS');
  console.log('=' .repeat(70));
  
  generateComprehensiveReport(analysisResults);
}

async function analyzeToolFieldParsing(tool: any) {
  const analysis = {
    total_expected_fields: Object.keys(EXPECTED_AI_FIELDS).length,
    present_fields: 0,
    missing_fields: [] as string[],
    incorrect_types: [] as string[],
    nested_structure_issues: [] as string[],
    successful_fields: [] as string[],
    generated_content_analysis: {} as any
  };
  
  // Check each expected field
  for (const [fieldName, expectedType] of Object.entries(EXPECTED_AI_FIELDS)) {
    const fieldValue = tool[fieldName];
    
    if (fieldValue === null || fieldValue === undefined) {
      analysis.missing_fields.push(fieldName);
    } else {
      const actualType = Array.isArray(fieldValue) ? 'array' : typeof fieldValue;
      
      if (expectedType === 'array' && !Array.isArray(fieldValue)) {
        analysis.incorrect_types.push(`${fieldName}: expected array, got ${actualType}`);
      } else if (expectedType === 'object' && typeof fieldValue !== 'object') {
        analysis.incorrect_types.push(`${fieldName}: expected object, got ${actualType}`);
      } else if (expectedType === 'string' && typeof fieldValue !== 'string') {
        analysis.incorrect_types.push(`${fieldName}: expected string, got ${actualType}`);
      } else {
        analysis.successful_fields.push(fieldName);
        analysis.present_fields++;
      }
    }
  }
  
  // Check nested structures
  for (const [fieldName, expectedSubFields] of Object.entries(EXPECTED_NESTED_STRUCTURES)) {
    const fieldValue = tool[fieldName];
    
    if (fieldValue && typeof fieldValue === 'object') {
      const missingSubFields = expectedSubFields.filter(subField => 
        !(subField in fieldValue) || fieldValue[subField] === null || fieldValue[subField] === undefined
      );
      
      if (missingSubFields.length > 0) {
        analysis.nested_structure_issues.push(`${fieldName}: missing ${missingSubFields.join(', ')}`);
      }
    }
  }
  
  // Analyze generated_content specifically
  if (tool.generated_content) {
    analysis.generated_content_analysis = analyzeGeneratedContent(tool.generated_content);
  }
  
  return analysis;
}

function analyzeGeneratedContent(generatedContent: any) {
  const analysis = {
    has_methodology: !!generatedContent.methodology,
    has_generated_at: !!generatedContent.generated_at,
    has_model_used: !!generatedContent.model_used,
    has_quality_score: typeof generatedContent.quality_score === 'number',
    has_fields_generated: Array.isArray(generatedContent.fields_generated),
    fields_generated_count: Array.isArray(generatedContent.fields_generated) ? generatedContent.fields_generated.length : 0,
    has_excluded_fields: Array.isArray(generatedContent.excluded_fields),
    schema_version: generatedContent.schema_version || 'unknown',
    validation_status: generatedContent.validation_status || 'unknown'
  };
  
  return analysis;
}

function generateComprehensiveReport(analysisResults: any[]) {
  if (analysisResults.length === 0) {
    console.log('❌ No analysis results to report');
    return;
  }
  
  // Calculate overall statistics
  const totalTools = analysisResults.length;
  const avgFieldsPresent = analysisResults.reduce((sum, result) => sum + result.present_fields, 0) / totalTools;
  const avgSuccessRate = (avgFieldsPresent / analysisResults[0].total_expected_fields) * 100;
  
  console.log(`\n📊 OVERALL STATISTICS (${totalTools} tools analyzed):`);
  console.log(`   Average fields present: ${avgFieldsPresent.toFixed(1)}/${analysisResults[0].total_expected_fields}`);
  console.log(`   Average success rate: ${avgSuccessRate.toFixed(1)}%`);
  
  // Field-by-field analysis
  const fieldStats: Record<string, { present: number; missing: number; incorrect: number }> = {};
  
  Object.keys(EXPECTED_AI_FIELDS).forEach(field => {
    fieldStats[field] = { present: 0, missing: 0, incorrect: 0 };
  });
  
  analysisResults.forEach(result => {
    result.successful_fields.forEach((field: string) => {
      if (fieldStats[field]) fieldStats[field].present++;
    });
    
    result.missing_fields.forEach((field: string) => {
      if (fieldStats[field]) fieldStats[field].missing++;
    });
    
    result.incorrect_types.forEach((typeIssue: string) => {
      const field = typeIssue.split(':')[0];
      if (fieldStats[field]) fieldStats[field].incorrect++;
    });
  });
  
  console.log('\n📋 FIELD-BY-FIELD SUCCESS RATES:');
  Object.entries(fieldStats).forEach(([field, stats]) => {
    const successRate = (stats.present / totalTools) * 100;
    const status = successRate >= 90 ? '✅' : successRate >= 70 ? '⚠️' : '❌';
    console.log(`   ${status} ${field}: ${successRate.toFixed(1)}% (${stats.present}/${totalTools})`);
    
    if (stats.missing > 0) {
      console.log(`      Missing in ${stats.missing} tools`);
    }
    if (stats.incorrect > 0) {
      console.log(`      Incorrect type in ${stats.incorrect} tools`);
    }
  });
  
  // Most problematic fields
  const problematicFields = Object.entries(fieldStats)
    .filter(([_, stats]) => (stats.present / totalTools) < 0.8)
    .sort(([_, a], [__, b]) => (a.present / totalTools) - (b.present / totalTools))
    .slice(0, 5);
    
  if (problematicFields.length > 0) {
    console.log('\n🚨 MOST PROBLEMATIC FIELDS (success rate < 80%):');
    problematicFields.forEach(([field, stats]) => {
      const successRate = (stats.present / totalTools) * 100;
      console.log(`   ❌ ${field}: ${successRate.toFixed(1)}% success rate`);
    });
  }
  
  // Generated content analysis
  const generatedContentStats = analysisResults.map(r => r.generated_content_analysis);
  const avgFieldsGenerated = generatedContentStats.reduce((sum, stats) => sum + (stats.fields_generated_count || 0), 0) / totalTools;
  
  console.log('\n📋 GENERATED CONTENT METADATA:');
  console.log(`   Average fields generated: ${avgFieldsGenerated.toFixed(1)}`);
  console.log(`   Tools with methodology: ${generatedContentStats.filter(s => s.has_methodology).length}/${totalTools}`);
  console.log(`   Tools with quality score: ${generatedContentStats.filter(s => s.has_quality_score).length}/${totalTools}`);
  console.log(`   Tools with fields list: ${generatedContentStats.filter(s => s.has_fields_generated).length}/${totalTools}`);
  
  // Recommendations
  console.log('\n🔧 RECOMMENDATIONS:');
  
  if (avgSuccessRate < 80) {
    console.log('   🚨 CRITICAL: Overall field parsing success rate is below 80%');
    console.log('   1. Review PromptManager.processAIDudeResponse() mapping logic');
    console.log('   2. Check AI prompt templates for missing field instructions');
    console.log('   3. Verify database schema supports all expected fields');
  } else if (avgSuccessRate < 95) {
    console.log('   ⚠️ WARNING: Some fields are consistently missing');
    console.log('   1. Focus on the problematic fields listed above');
    console.log('   2. Check if AI providers are generating these fields');
    console.log('   3. Verify field mapping in content generation pipeline');
  } else {
    console.log('   ✅ EXCELLENT: Field parsing is working well overall');
    console.log('   1. Monitor for any new field requirements');
    console.log('   2. Consider adding validation for critical fields');
  }
  
  // Database schema check
  console.log('\n📋 NEXT STEPS:');
  console.log('   1. Run database schema validation');
  console.log('   2. Test with fresh AI generation');
  console.log('   3. Compare OpenAI vs OpenRouter field generation');
  console.log('   4. Validate bulk processing vs manual submission differences');
}

// Database schema validation
async function validateDatabaseSchema() {
  console.log('\n🗃️ DATABASE SCHEMA VALIDATION');
  console.log('=' .repeat(50));
  
  try {
    // Get database columns using correct PostgreSQL system tables
    const { data: columns, error } = await supabaseAdmin
      .rpc('get_table_columns', { table_name: 'tools' });
      
    if (error) {
      // Fallback: try to get schema info from a sample tool
      const { data: sampleTool, error: sampleError } = await supabaseAdmin
        .from('tools')
        .select('*')
        .limit(1)
        .single();
        
      if (sampleError) {
        console.log('❌ Cannot validate database schema');
        return;
      }
      
      const dbFields = Object.keys(sampleTool);
      const expectedFields = Object.keys(EXPECTED_AI_FIELDS);
      
      console.log(`📋 Database fields found: ${dbFields.length}`);
      console.log(`📋 Expected AI fields: ${expectedFields.length}`);
      
      const missingFields = expectedFields.filter(field => !dbFields.includes(field));
      const extraFields = dbFields.filter(field => !expectedFields.includes(field) && !['id', 'slug', 'link', 'website', 'logo_url', 'screenshots', 'created_at', 'updated_at'].includes(field));
      
      if (missingFields.length > 0) {
        console.log(`❌ Missing fields in database: ${missingFields.join(', ')}`);
      }
      
      if (extraFields.length > 0) {
        console.log(`📋 Extra fields in database: ${extraFields.join(', ')}`);
      }
      
      console.log('✅ Database schema validation completed (limited)');
    }
  } catch (error) {
    console.log('⚠️ Database schema validation skipped:', error);
  }
}

async function runAnalysis() {
  try {
    await analyzeExistingFieldParsing();
    await validateDatabaseSchema();
    
    console.log('\n🎯 ANALYSIS COMPLETE');
    console.log('=' .repeat(70));
    console.log('✅ Review the field parsing analysis above');
    console.log('✅ Address any problematic fields identified');
    console.log('✅ Consider implementing missing field validation');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error);
  }
}

runAnalysis().catch(console.error);
