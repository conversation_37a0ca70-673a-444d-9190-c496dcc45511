import json
import csv
import os
import time
import re
from typing import List, Dict, Any, Set

# Checkpoint file for resuming the crawler
CHECKPOINT_FILE = "aitoptools_crawler_checkpoint.json"

def load_checkpoint():
    """Load checkpoint data if file exists"""
    data = {
        'tools_data': [],
        'processed_urls': set(),
        'failed_urls': {}
    }
    
    if os.path.exists(CHECKPOINT_FILE):
        try:
            with open(CHECKPOINT_FILE, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
                data['tools_data'] = checkpoint_data.get('tools_data', [])
                data['processed_urls'] = set(checkpoint_data.get('processed_urls', []))
                data['failed_urls'] = checkpoint_data.get('failed_urls', {})
            print(f"Loaded checkpoint with {len(data['tools_data'])} tools, {len(data['processed_urls'])} processed URLs, and {len(data['failed_urls'])} failed URLs")
        except Exception as e:
            print(f"Error loading checkpoint: {e}")
    
    return data

def save_checkpoint(tools_data: List[Dict[str, Any]], processed_urls: Set[str], failed_urls: Dict[str, Dict[str, Any]] = None):
    """Save checkpoint data for resume support"""
    if failed_urls is None:
        failed_urls = {}
        
    checkpoint_data = {
        'tools_data': tools_data,
        'processed_urls': list(processed_urls),
        'failed_urls': failed_urls
    }
    try:
        # First save to a temporary file
        temp_file = f"{CHECKPOINT_FILE}.tmp"
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_data, f)
        
        # Then rename it to the actual checkpoint file (safer atomic operation)
        if os.path.exists(CHECKPOINT_FILE):
            os.replace(temp_file, CHECKPOINT_FILE)
        else:
            os.rename(temp_file, CHECKPOINT_FILE)
            
        print(f"Saved checkpoint with {len(tools_data)} tools, {len(processed_urls)} processed URLs, and {len(failed_urls)} failed URLs")
        return True
    except Exception as e:
        print(f"Error saving checkpoint: {e}")
        return False

def save_to_csv(tools_data: List[Dict[str, Any]]):
    """Save the extracted tools data to a CSV file"""
    timestamp = int(time.time())
    output_file = f"aitoptools_data_{timestamp}.csv"
    
    # Define the field order to match extract_tool.py output
    fieldnames = [
        'category', 'subcategory', 'name', 'short_description', 'image_url',
        'website_url', 'pricing_type', 'pricing_amount', 'full_description',
        'use_cases_and_features', 'page_url'
    ]
    
    # Process the data for CSV format
    processed_data = []
    for tool in tools_data:
        processed_tool = dict(tool)  # Create a copy
        
        # Convert features list to pipe-separated string
        if 'use_cases_and_features' in processed_tool and isinstance(processed_tool['use_cases_and_features'], list):
            processed_tool['use_cases_and_features'] = "|".join([str(item) for item in processed_tool['use_cases_and_features']])
        
        processed_data.append(processed_tool)
    
    # Write to CSV
    with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames, extrasaction='ignore')
        writer.writeheader()
        writer.writerows(processed_data)
    
    print(f"Saved {len(processed_data)} AI tools to {output_file}")
    return output_file

def save_to_json(tools_data: List[Dict[str, Any]]):
    """Save the extracted tools data to a JSON file"""
    timestamp = int(time.time())
    output_file = f"aitoptools_data_{timestamp}.json"
    
    with open(output_file, "w", encoding="utf-8") as jsonfile:
        json.dump(tools_data, jsonfile, indent=2)
    
    print(f"Saved {len(tools_data)} AI tools to {output_file}")
    return output_file

def clean_tool_features(features_list):
    """Clean up the features list to remove navigation items and other irrelevant content"""
    if not features_list:
        return []
        
    cleaned_features = []
    for feature in features_list:
        if not isinstance(feature, str):
            continue
            
        # Clean up the feature text
        clean_feature = feature.strip()
        
        # Remove any numbering (1., 2., etc.) from the front
        clean_feature = re.sub(r'^\d+\.\s*', '', clean_feature)
        
        # Skip menu items, navigation links, etc.
        if (clean_feature and len(clean_feature) > 5 and len(clean_feature) < 200 and
            not any(x in clean_feature.lower() for x in ["submit tool", "contact", "about us", "privacy policy", "terms"])):
            cleaned_features.append(clean_feature)
    
    return cleaned_features if cleaned_features else []

def export_failed_urls(failed_urls: Dict[str, Dict[str, Any]]):
    """Export the list of failed URLs to a separate file for analysis"""
    if not failed_urls:
        return None
        
    timestamp = int(time.time())
    output_file = f"failed_urls_{timestamp}.json"
    
    with open(output_file, "w", encoding="utf-8") as jsonfile:
        json.dump(failed_urls, jsonfile, indent=2)
    
    print(f"Saved {len(failed_urls)} failed URLs to {output_file}")
    return output_file