import asyncio
import aiohttp
import re
import json
import time
import logging
import sys
import os
import traceback
from bs4 import BeautifulSoup
from typing import List, Dict, Any, Set, Tuple, Optional
from urllib.parse import urlparse, parse_qs

# Import the fixed extractor function
from fix_extractor import extract_tool_data

# Local imports
from categories import CATEGORY_MAP
from utils import load_checkpoint, save_checkpoint, save_to_json, save_to_csv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("enhanced_crawler.log", encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("enhanced_crawler")

class EnhancedCrawler:
    """
    Enhanced crawler with improved methods for handling the "load more" functionality
    """
    
    def __init__(self, max_concurrent_tools=5):
        self.max_concurrent_tools = max_concurrent_tools
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'X-Requested-With': 'XMLHttpRequest',  # Important for AJAX requests
            'Referer': 'https://aitoptools.com/'  # Adding a referer to make request more legitimate
        }
        
        # Load checkpoint data
        checkpoint_data = load_checkpoint()
        self.all_tools_data = checkpoint_data['tools_data']
        self.processed_urls = checkpoint_data['processed_urls']
        self.failed_urls = checkpoint_data.get('failed_urls', {})
        
        logger.info(f"Initialized enhanced crawler with {len(self.all_tools_data)} tools in checkpoint")
        logger.info(f"Using concurrency of {max_concurrent_tools}")
    
    def _extract_category_id(self, html, url):
        """
        Extract category ID with specific focus on term ID in body class and JavaScript patterns
        
        Prioritizes finding the ID in:
        1. The body class attribute (term-detector term-6824)
        2. JavaScript fetch calls using the ID (fetch('ajaxtoolcat/'+6824+)
        """
        logger.info(f"Attempting to extract category ID from URL: {url}")
        
        # Method 1: Look for term ID in body class (highest priority)
        body_match = re.search(r'class="[^"]*term-[a-z-]+ term-(\d+)', html)
        if body_match:
            category_id = body_match.group(1)
            logger.info(f"Found category ID in body class: {category_id}")
            return category_id
        
        # Method 2: Look for IDs in JavaScript fetch calls
        js_patterns = [
            r'fetch\([\'"][^\'"]*/\'\+(\d+)',  # fetch('ajaxtoolcat/'+6824+
            r'[\'"][^\'"]*/\'\+(\d+)',         # 'ajaxtoolcat/'+6824+
        ]
        
        for pattern in js_patterns:
            js_matches = re.findall(pattern, html)
            if js_matches:
                # Find the most common ID (likely the correct one)
                id_counts = {}
                for id_val in js_matches:
                    id_counts[id_val] = id_counts.get(id_val, 0) + 1
                
                most_common_id = max(id_counts.keys(), key=lambda k: id_counts[k])
                logger.info(f"Found category ID in JavaScript: {most_common_id}")
                return most_common_id
        
        # Method 3: Direct search for loadmorecategory function
        if 'function loadmorecategory()' in html:
            loadmore_match = re.search(r'function\s+loadmorecategory\s*\(\)\s*\{(.*?)\}', html, re.DOTALL)
            if loadmore_match:
                function_content = loadmore_match.group(1)
                # Look for 4-digit numbers that might be category IDs
                id_matches = re.findall(r'\b(\d{4})\b', function_content)
                if id_matches:
                    # Take the most frequent 4-digit number
                    id_counts = {}
                    for id_val in id_matches:
                        id_counts[id_val] = id_counts.get(id_val, 0) + 1
                    
                    most_common_id = max(id_counts.keys(), key=lambda k: id_counts[k])
                    logger.info(f"Found category ID in loadmorecategory function: {most_common_id}")
                    return most_common_id
        
        logger.warning("Could not find category ID with reliable methods")
        return None
    
    def _extract_tools_from_html(self, html):
        """Extract tool URLs from HTML content"""
        tool_urls = set()
        matches = re.findall(r'href=[\'"]https://aitoptools.com/tool/([^\'"]+)[\'"]', html)
        for match in matches:
            tool_urls.add(f"https://aitoptools.com/tool/{match}")
        
        return list(tool_urls)
        
    async def _try_different_ajax_methods(self, session, url, html, visible_tools_count):
        """Try different AJAX methods to load more tools"""
        # Combined tools from all methods
        all_tools = set()
        
        # First try to extract category ID
        category_id = self._extract_category_id(html, url)
        
        if category_id:
            logger.info(f"Found category ID: {category_id}")
            try:
                # Add initial visible tools
                initial_tools = self._extract_tools_from_html(html)
                for tool in initial_tools:
                    all_tools.add(tool)
                
                # Try direct AJAX URL method first (based on user's console output)
                tools_from_direct_ajax = await self._try_direct_ajax_url(session, url, category_id, visible_tools_count)
                
                if tools_from_direct_ajax:
                    for tool in tools_from_direct_ajax:
                        all_tools.add(tool)
                    
                # Still try standard WordPress AJAX as a fallback
                tools_from_ajax = await self._try_standard_wordpress_ajax(session, category_id, visible_tools_count)
                
                if tools_from_ajax:
                    for tool in tools_from_ajax:
                        all_tools.add(tool)
                    
            except Exception as e:
                logger.error(f"Error in AJAX methods: {e}")
        else:
            logger.warning("No category ID found, skipping AJAX methods")
        
        # Try direct pagination as another method
        try:
            tools_from_pagination = await self._try_direct_page_urls(session, url, visible_tools_count)
            if tools_from_pagination:
                for tool in tools_from_pagination:
                    all_tools.add(tool)
        except Exception as e:
            logger.error(f"Error in direct pagination method: {e}")
        
        logger.info(f"Combined all methods, found {len(all_tools)} tools total")
        return list(all_tools)
    
    async def _try_direct_ajax_url(self, session, base_url, category_id, initial_count):
        """
        Try the direct AJAX URL method that was discovered by the user
        Format: https://aitoptools.com/ai-tools/summarizer/ajaxtoolcat/6807/2/
        """
        logger.info("Trying direct AJAX URL method based on user's findings")
        
        # Parse the base URL to extract the category slug
        parsed_url = urlparse(base_url)
        path_parts = parsed_url.path.strip('/').split('/')
        
        # Make sure we have the right path structure
        if len(path_parts) >= 2 and path_parts[0] == 'ai-tools':
            category_slug = path_parts[1]
            
            tool_urls = set()
            max_pages = 100
            
            # Start from page 2 since page 1 is already loaded
            for page in range(2, max_pages + 1):
                # Use the exact URL format verified in the browser console
                if category_slug == "code-generator":
                    ajax_url = f"https://aitoptools.com/ai-tools/code-generator/ajaxtoolcat/6806/{page}/"
                elif category_slug == "summarizer":
                    ajax_url = f"https://aitoptools.com/ai-tools/summarizer/ajaxtoolcat/6807/{page}/"
                else:
                    ajax_url = f"https://aitoptools.com/ai-tools/{category_slug}/ajaxtoolcat/{category_id}/{page}/"
                
                logger.info(f"Trying AJAX URL: {ajax_url}")
                
                try:
                    async with session.get(ajax_url, headers=self.headers) as ajax_response:
                        if ajax_response.status != 200:
                            logger.error(f"AJAX request error {ajax_response.status}")
                            break
                        
                        ajax_html = await ajax_response.text()
                        
                        # Check for empty response or end message
                        if "No More Tools To Explore" in ajax_html or not ajax_html.strip() or ajax_html.strip() == "":
                            logger.info(f"Reached end of tools at page {page}")
                            break
                        
                        # Extract tool URLs from the AJAX response
                        new_tools = self._extract_tools_from_html(ajax_html)
                        new_count = 0
                        
                        for tool in new_tools:
                            if tool not in tool_urls:
                                tool_urls.add(tool)
                                new_count += 1
                        
                        logger.info(f"Found {new_count} new tools from page {page}, total: {len(tool_urls)}")
                        
                        # If no new tools found, we're done
                        if new_count == 0:
                            break
                        
                        # Short pause to be nice to the server
                        await asyncio.sleep(1)
                        
                except Exception as e:
                    logger.error(f"Error fetching AJAX page {page}: {e}")
                    break
            
            logger.info(f"Direct AJAX URL method found {len(tool_urls)} tools")
            return list(tool_urls)
        else:
            logger.warning(f"URL structure not suitable for direct AJAX method: {base_url}")
            return []
    
    async def _try_standard_wordpress_ajax(self, session, category_id, initial_count):
        """Try the standard WordPress AJAX method with loadmorecategory"""
        logger.info("Trying standard WordPress AJAX with loadmorecategory")
        
        tool_urls = set()
        max_pages = 1
        
        for page in range(1, max_pages + 1):
            ajax_url = "https://aitoptools.com/wp-admin/admin-ajax.php"
            
            # Try multiple variations of form data parameters
            form_data_variations = [
                {
                    'action': 'loadmorecategory',
                    'cat': category_id,
                    'page': str(page)
                },
                {
                    'action': 'load_more_category',
                    'cat': category_id,
                    'page': str(page)
                },
                {
                    'action': 'load_more',
                    'cat': category_id,
                    'page': str(page)
                },
                {
                    'action': 'jet_engine_ajax',
                    'handler': 'listing_load_more',
                    'page': str(page),
                    'query': json.dumps({"post_type": "ai-tools", "taxonomy": "ai-category", "term": category_id})
                }
            ]
            
            success = False
            
            # Try each variation
            for form_data in form_data_variations:
                logger.info(f"Trying AJAX with action: {form_data.get('action')} on page {page}")
                
                try:
                    async with session.post(ajax_url, data=form_data, headers=self.headers) as ajax_response:
                        if ajax_response.status != 200:
                            logger.error(f"AJAX request error {ajax_response.status}")
                            continue
                        
                        ajax_html = await ajax_response.text()
                        
                        # Check for empty response or end message
                        if "No More Tools To Explore" in ajax_html or not ajax_html.strip():
                            logger.info(f"Reached end of tools at page {page}")
                            success = True
                            break
                        
                        # Extract tool URLs from the AJAX response
                        new_tools = self._extract_tools_from_html(ajax_html)
                        new_count = 0
                        
                        for tool in new_tools:
                            if tool not in tool_urls:
                                tool_urls.add(tool)
                                new_count += 1
                        
                        logger.info(f"Found {new_count} new tools from page {page}, total: {len(tool_urls)}")
                        
                        # If we found new tools, this method works
                        if new_count > 0:
                            success = True
                            break
                        
                except Exception as e:
                    logger.error(f"Error with AJAX variation: {e}")
                    continue
            
            # If none of the variations worked or we've reached the end, stop
            if not success:
                break
            
            # Short pause to be nice to the server
            await asyncio.sleep(1)
        
        logger.info(f"Standard WordPress AJAX method found {len(tool_urls)} tools")
        return list(tool_urls)
    
    async def _try_direct_page_urls(self, session, base_url, initial_count):
        """Try using direct page URLs like /page/2/, /page/3/, etc."""
        logger.info("Trying direct page URLs method")
        
        tool_urls = set()
        max_pages = 1  # Try more pages
        
        # Strip trailing slash for URL joining
        if base_url.endswith('/'):
            base_url = base_url[:-1]
        
        for page in range(2, max_pages + 1):  # Start with page 2 (page 1 is already loaded)
            page_url = f"{base_url}/page/{page}/"
            logger.info(f"Trying direct page URL: {page_url}")
            
            try:
                async with session.get(page_url, headers=self.headers) as page_response:
                    if page_response.status != 200:
                        logger.info(f"Page {page} not found (status {page_response.status})")
                        break
                    
                    page_html = await page_response.text()
                    
                    # Check for empty page or no tools
                    soup = BeautifulSoup(page_html, 'html.parser')
                    tools_container = soup.select('.jet-listing-grid, .elementor-element-populated')
                    
                    if not tools_container or all('no-posts-found' in elem.get('class', []) for elem in tools_container):
                        logger.info(f"No tools found on page {page}")
                        break
                    
                    # Extract tool URLs from the page
                    new_tools = self._extract_tools_from_html(page_html)
                    new_count = 0
                    
                    for tool in new_tools:
                        if tool not in tool_urls:
                            tool_urls.add(tool)
                            new_count += 1
                    
                    logger.info(f"Found {new_count} new tools on page {page}")
                    
                    # If we're not getting any new tools, stop
                    if new_count == 0:
                        logger.info("No new tools found, stopping pagination")
                        break
            except Exception as e:
                logger.error(f"Error fetching page {page}: {e}")
                break
            
            # Short pause between page requests
            await asyncio.sleep(1)
        
        logger.info(f"Direct page URLs method found {len(tool_urls)} tools")
        return list(tool_urls)
    
    async def extract_tool_urls(self, session, category_url):
        """Extract tool URLs using multiple methods for robustness"""
        logger.info(f"Fetching category page: {category_url}")
        
        try:
            # Get the initial page HTML
            async with session.get(category_url, headers=self.headers, timeout=30) as response:
                if response.status != 200:
                    logger.error(f"HTTP error {response.status} for {category_url}")
                    return []
                
                html = await response.text()
                
                # First, extract all visible tool URLs
                tool_urls = set()
                matches = re.findall(r'href=[\'"]https://aitoptools.com/tool/([^\'"]+)[\'"]', html)
                for match in matches:
                    tool_urls.add(f"https://aitoptools.com/tool/{match}")
                
                visible_count = len(tool_urls)
                logger.info(f"Found {visible_count} tools on initial page load")
                
                # Try different AJAX methods to load more tools
                more_tools = await self._try_different_ajax_methods(session, category_url, html, visible_count)
                
                # Add any new tools we found
                for url in more_tools:
                    tool_urls.add(url)
                
                logger.info(f"Total tools found: {len(tool_urls)}")
                return list(tool_urls)
                
        except Exception as e:
            logger.error(f"Error extracting tools from {category_url}: {e}")
            return []
    
    async def process_subcategory(self, session, subcategory_url, subcategory_name, max_tools=0):
        """Process all tools in a subcategory"""
        logger.info(f"Processing subcategory: {subcategory_name} ({subcategory_url})")
        
        # Get tool URLs
        tool_urls = await self.extract_tool_urls(session, subcategory_url)
        
        if not tool_urls:
            logger.warning(f"No tool URLs found for {subcategory_name}")
            return 0
        
        # Filter unprocessed URLs
        unprocessed_urls = [url for url in tool_urls if url not in self.processed_urls]
        logger.info(f"Found {len(unprocessed_urls)} unprocessed tools out of {len(tool_urls)}")
        
        # Limit to max_tools if specified
        if max_tools > 0 and len(unprocessed_urls) > max_tools:
            unprocessed_urls = unprocessed_urls[:max_tools]
            logger.info(f"Processing only {max_tools} tools")
        
        processed_count = 0
        # Process tools in batches
        batch_size = min(self.max_concurrent_tools, len(unprocessed_urls))
        if batch_size == 0:
            return 0
            
        for i in range(0, len(unprocessed_urls), batch_size):
            batch = unprocessed_urls[i:i+batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(unprocessed_urls) + batch_size - 1)//batch_size}")
            
            # Process tools concurrently
            tasks = []
            for url in batch:
                tasks.append(extract_tool_data(url, subcategory_name))
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for j, result in enumerate(results):
                url = batch[j]
                
                if isinstance(result, Exception):
                    logger.error(f"Error processing {url}: {result}")
                    self.failed_urls[url] = {"error": str(result), "timestamp": int(time.time())}
                elif result is None:
                    logger.warning(f"No data extracted from {url}")
                    self.failed_urls[url] = {"error": "No data extracted", "timestamp": int(time.time())}
                else:
                    logger.info(f"Successfully processed: {result.get('name', 'Unnamed tool')}")
                    self.all_tools_data.append(result)
                    self.processed_urls.add(url)
                    processed_count += 1
            
            # Save checkpoint after each batch
            save_checkpoint(self.all_tools_data, self.processed_urls, self.failed_urls)
            
            # Short pause between batches
            if i + batch_size < len(unprocessed_urls):
                await asyncio.sleep(2)
        
        logger.info(f"Completed {subcategory_name}, processed {processed_count} tools")
        return processed_count
    
    async def crawl_all_categories(self, output_format="json", max_tools_per_subcategory=0, 
                                 specific_category=None, specific_subcategory=None):
        """Crawl all categories and subcategories"""
        logger.info("Starting full crawl of all categories and subcategories")
        
        # Create a session for all requests
        async with aiohttp.ClientSession() as session:
            # Determine which categories to process
            categories_to_process = {}
            if specific_category:
                if specific_category in CATEGORY_MAP:
                    categories_to_process[specific_category] = CATEGORY_MAP[specific_category]
                else:
                    logger.error(f"Category not found: {specific_category}")
                    return None
            else:
                categories_to_process = CATEGORY_MAP
            
            total_processed = 0
            
            # Process each category
            for category_name, category_data in categories_to_process.items():
                logger.info(f"Processing main category: {category_name}")
                
                # Determine which subcategories to process
                if specific_subcategory:
                    subcategories = [sc for sc in category_data["subcategories"] 
                                   if sc["name"] == specific_subcategory]
                    if not subcategories:
                        logger.warning(f"Subcategory {specific_subcategory} not found in {category_name}")
                        continue
                else:
                    subcategories = category_data["subcategories"]
                
                # Process each subcategory
                for subcategory in subcategories:
                    try:
                        processed = await self.process_subcategory(
                            session,
                            subcategory["url"],
                            subcategory["name"],
                            max_tools_per_subcategory
                        )
                        total_processed += processed
                    except Exception as e:
                        logger.error(f"Error processing subcategory {subcategory['name']}: {e}")
                        logger.error(traceback.format_exc())
                        # Continue with next subcategory
                        continue
            
            logger.info(f"Full crawl complete! Processed {total_processed} tools")
            logger.info(f"Total tools in database: {len(self.all_tools_data)}")
            
            # Save all the extracted data
            if output_format.lower() == "csv":
                return save_to_csv(self.all_tools_data)
            else:
                return save_to_json(self.all_tools_data)


async def main():
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Enhanced AI tools crawler with improved AJAX methods")
    parser.add_argument("--format", choices=["csv", "json"], default="json", help="Output format (csv or json)")
    parser.add_argument("--max-tools", type=int, default=0, help="Maximum number of tools per subcategory (0 for all)")
    parser.add_argument("--concurrency", type=int, default=5, help="Maximum concurrent tool extractions")
    parser.add_argument("--category", help="Crawl only a specific main category")
    parser.add_argument("--subcategory", help="Crawl only a specific subcategory (requires --category)")
    args = parser.parse_args()
    
    # Display startup message
    print("\n===== AITopTools Enhanced AJAX Crawler =====")
    print(f"Output format: {args.format}")
    print(f"Max tools per subcategory: {args.max_tools if args.max_tools > 0 else 'All'}")
    print(f"Concurrency: {args.concurrency}")
    if args.category:
        print(f"Filtering to category: {args.category}")
        if args.subcategory:
            print(f"Filtering to subcategory: {args.subcategory}")
    print("===================================\n")
    
    start_time = time.time()
    
    # Initialize the crawler
    crawler = EnhancedCrawler(max_concurrent_tools=args.concurrency)
    
    # Crawl all categories and save the data
    output_file = await crawler.crawl_all_categories(
        output_format=args.format, 
        max_tools_per_subcategory=args.max_tools,
        specific_category=args.category,
        specific_subcategory=args.subcategory
    )
    
    end_time = time.time()
    total_time = end_time - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print("\n===== Extraction Summary =====")
    print(f"Total time: {int(hours)}h {int(minutes)}m {int(seconds)}s")
    print(f"Total tools extracted: {len(crawler.all_tools_data)}")
    print(f"Data saved to: {output_file}")
    
    if crawler.failed_urls:
        print(f"Failed URLs: {len(crawler.failed_urls)}")
    else:
        print("No failed URLs!")
    
    print("===================================\n")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nCrawling interrupted by user. Partial results are saved in the checkpoint file.")
    except Exception as e:
        print(f"\nCrawling terminated due to an error: {e}")
        print("Please check logs for details.")