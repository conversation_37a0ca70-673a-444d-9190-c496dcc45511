import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function debugCurrentFailingTool() {
  const toolId = '576b72e0-2442-4e3b-921b-085a62f919ff';
  
  console.log('🔍 DEBUGGING CURRENT FAILING TOOL');
  console.log('=' .repeat(50));
  console.log(`Tool ID: ${toolId}`);
  
  // 1. Check if tool exists with admin client
  console.log('\n📋 Step 1: Check if tool exists with admin client');
  try {
    const { data: tools, error } = await supabaseAdmin
      .from('tools')
      .select('*')
      .eq('id', toolId);
      
    if (error) {
      console.log('❌ Admin client error:', error.message);
    } else if (tools && tools.length > 0) {
      console.log('✅ Admin client found tool:', {
        id: tools[0].id,
        name: tools[0].name,
        website: tools[0].website,
        submission_source: tools[0].submission_source,
        submission_type: tools[0].submission_type,
        created_at: tools[0].created_at
      });
    } else {
      console.log('❌ Admin client: Tool not found');
    }
  } catch (error) {
    console.log('💥 Admin client exception:', error);
  }
  
  // 2. Test the exact query from the pipeline
  console.log('\n📋 Step 2: Test exact pipeline query');
  try {
    const { data: tools, error: queryError } = await supabaseAdmin
      .from('tools')
      .select('submission_source, submission_type, name, website, id')
      .eq('id', toolId.trim());
      
    if (queryError) {
      console.log('❌ Pipeline query error:', queryError.message);
      console.log('Error details:', {
        code: queryError.code,
        message: queryError.message,
        details: queryError.details,
        hint: queryError.hint
      });
    } else if (tools && tools.length > 0) {
      console.log('✅ Pipeline query found tool:', tools[0]);
    } else {
      console.log('❌ Pipeline query: Tool not found');
    }
  } catch (error) {
    console.log('💥 Pipeline query exception:', error);
  }
  
  // 3. Check if there are any timing issues
  console.log('\n📋 Step 3: Check timing with delays');
  for (let delay of [0, 500, 1000, 2000]) {
    console.log(`\n  Testing with ${delay}ms delay...`);
    
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    try {
      const { data: tools, error } = await supabaseAdmin
        .from('tools')
        .select('submission_source, name')
        .eq('id', toolId);
        
      if (error) {
        console.log(`    ❌ Error with ${delay}ms delay:`, error.message);
      } else if (tools && tools.length > 0) {
        console.log(`    ✅ Found with ${delay}ms delay:`, tools[0].name, tools[0].submission_source);
      } else {
        console.log(`    ❌ Not found with ${delay}ms delay`);
      }
    } catch (error) {
      console.log(`    💥 Exception with ${delay}ms delay:`, error);
    }
  }
  
  // 4. Check database connection health
  console.log('\n📋 Step 4: Check database connection health');
  try {
    const { data, error } = await supabaseAdmin
      .from('tools')
      .select('count')
      .limit(1);
      
    if (error) {
      console.log('❌ Database connection issue:', error.message);
    } else {
      console.log('✅ Database connection healthy');
    }
  } catch (error) {
    console.log('💥 Database connection exception:', error);
  }
  
  // 5. Check if supabaseAdmin is properly initialized
  console.log('\n📋 Step 5: Check supabaseAdmin initialization');
  console.log('supabaseAdmin exists:', !!supabaseAdmin);
  console.log('Environment variables:');
  console.log('  NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.log('  SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  
  console.log('\n' + '=' .repeat(50));
  console.log('🎯 DEBUGGING COMPLETE');
}

debugCurrentFailingTool().catch(console.error);
