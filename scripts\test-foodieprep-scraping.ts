#!/usr/bin/env tsx

/**
 * Test FoodiePrep Scraping
 *
 * This script tests the scraping of www.foodieprep.ai with the fixed database storage
 * to verify that the duplicate file issue is resolved.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST before importing any modules
dotenv.config({ path: '.env.local' });

// Set required environment variables for the test
process.env.NODE_ENV = 'development';

// Now import the modules after environment is set
import { contentProcessor } from '../src/lib/scraping/content-processor';
import { EnhancedScrapeRequest } from '../src/lib/scraping/types';

async function testFoodiePrepScraping() {
  console.log('🧪 Testing FoodiePrep Scraping with Fixed Database Storage...\n');

  const request: EnhancedScrapeRequest = {
    url: 'https://www.foodieprep.ai/',
    options: {
      timeout: 70000,
      outputFormat: 'markdown',
      enableJSRendering: false,
      blockResources: true,
      deviceType: 'desktop'
    },
    costOptimization: true,
    mediaCollection: true,
    multiPageConfig: {
      enabled: false,
      mode: 'conditional',
      maxPagesPerTool: 2,
      creditThreshold: 50,
      pageTypes: {
        pricing: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false },
        faq: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false },
        features: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false },
        about: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false }
      },
      fallbackStrategy: { searchInMainPage: true, useNavigation: false, useSitemap: false }
    }
  };

  try {
    console.log('🚀 Starting enhanced scraping...');
    console.log(`📍 URL: ${request.url}`);
    console.log(`⚙️ Cost optimization: ${request.costOptimization}`);
    console.log(`📸 Media collection: ${request.mediaCollection}`);
    console.log('');

    const startTime = Date.now();
    const result = await contentProcessor.processEnhancedScrape(request);
    const endTime = Date.now();

    console.log('📊 Scraping Results:');
    console.log(`   ✅ Success: ${result.success}`);
    console.log(`   📄 Content length: ${result.content?.length || 0} characters`);
    console.log(`   💰 Credits used: ${result.costAnalysis?.creditsUsed || 0}`);
    console.log(`   🎯 Optimization strategy: ${result.costAnalysis?.optimizationStrategy || 'Unknown'}`);
    console.log(`   ⏱️ Processing time: ${endTime - startTime}ms`);
    console.log(`   🔗 URL: ${result.url}`);
    console.log(`   📅 Timestamp: ${result.timestamp}`);

    if (result.contentAnalysis) {
      console.log(`   📈 Content analysis:`);
      console.log(`      - Scenario: ${result.contentAnalysis.scenario}`);
      console.log(`      - Confidence: ${result.contentAnalysis.confidence}%`);
      console.log(`      - Has substantial content: ${result.contentAnalysis.hasSubstantialContent}`);
      console.log(`      - Has structure: ${result.contentAnalysis.hasStructure}`);
    }

    if (result.mediaAssets) {
      console.log(`   🖼️ Media assets:`);
      console.log(`      - Favicon: ${result.mediaAssets.favicon ? 'Available' : 'None'}`);
      console.log(`      - OG images: ${result.mediaAssets.ogImages?.length || 0}`);
      console.log(`      - Screenshot: ${result.mediaAssets.screenshot ? 'Available' : 'None'}`);
    }

    if (result.error) {
      console.log(`   ❌ Error: ${result.error}`);
    }

    console.log('');

    // Check for file creation
    const { promises: fs } = await import('fs');
    const { join } = await import('path');
    
    try {
      const scrapedDir = join(process.cwd(), 'data', 'scraped-content', 'www.foodieprep.ai');
      const files = await fs.readdir(scrapedDir);
      const mdFiles = files.filter(f => f.endsWith('.md'));
      
      console.log('📁 File System Results:');
      console.log(`   📂 Directory: ${scrapedDir}`);
      console.log(`   📄 .md files found: ${mdFiles.length}`);
      
      if (mdFiles.length > 0) {
        console.log('   📋 Files:');
        mdFiles.forEach((file, index) => {
          console.log(`      ${index + 1}. ${file}`);
        });
        
        if (mdFiles.length > 1) {
          console.log('   ⚠️ WARNING: Multiple files detected - duplicate issue may still exist');
        } else {
          console.log('   ✅ Single file created - duplicate issue resolved');
        }
      }
    } catch (fileError) {
      console.log('📁 File System Results: Directory not found or empty');
    }

    console.log('');
    console.log('🎯 Test Summary:');
    
    if (result.success) {
      console.log('✅ Scraping completed successfully');
      console.log('✅ Database storage debugging information logged');
      console.log('✅ Content processing pipeline functional');
      
      if (result.costAnalysis?.creditsUsed && result.costAnalysis.creditsUsed <= 1) {
        console.log('✅ Cost optimization working (1 credit used)');
      } else {
        console.log(`⚠️ Cost optimization may need review (${result.costAnalysis?.creditsUsed || 0} credits used)`);
      }
    } else {
      console.log('❌ Scraping failed');
      console.log('❌ Check the debug logs for database storage issues');
    }

  } catch (error) {
    console.error('💥 Test failed with exception:', error);
    console.error('');
    console.error('🔍 Debug information:');
    console.error('   Error type:', error instanceof Error ? error.constructor.name : typeof error);
    console.error('   Error message:', error instanceof Error ? error.message : String(error));
    if (error instanceof Error && error.stack) {
      console.error('   Stack trace:', error.stack);
    }
  }
}

// Run the test
testFoodiePrepScraping().catch(console.error);
