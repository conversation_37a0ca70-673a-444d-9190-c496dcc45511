'use client';

import React, { useState, useEffect } from 'react';
import { 
  Setting<PERSON>, 
  Key, 
  Zap, 
  CheckCircle, 
  AlertCircle, 
  Save,
  TestTube,
  Eye,
  EyeOff
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface AIProvider {
  id: string;
  name: string;
  status: 'active' | 'inactive' | 'error';
  model: string;
  apiKey: string;
  endpoint?: string;
  maxTokens: number;
  temperature: number;
  isDefault: boolean;
}

export default function AIConfigPage() {
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [testingProvider, setTestingProvider] = useState<string | null>(null);
  const [showApiKeys, setShowApiKeys] = useState<Record<string, boolean>>({});
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Load AI provider configuration
  useEffect(() => {
    const loadProviders = async () => {
      try {
        setIsLoading(true);

        // Call real configuration API
        const response = await fetch('/api/admin/config?section=ai-providers', {
          headers: {
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to load AI provider configuration');
        }

        const data = await response.json();

        if (data.section === 'ai-providers' && data.data) {
          const config = data.data;

          // Transform configuration data to provider format
          const transformedProviders: AIProvider[] = [
            {
              id: 'openai',
              name: 'OpenAI',
              status: config.openai?.enabled ? 'active' : 'inactive',
              model: config.openai?.model || 'gpt-4o-2024-11-20',
              apiKey: '***************************', // Masked for security
              maxTokens: Number(config.openai?.maxTokens) || 4000,
              temperature: Number(config.openai?.temperature) || 0.7,
              isDefault: config.modelSelection?.fallbackOrder?.[0] === 'openai'
            },
            {
              id: 'openrouter',
              name: 'OpenRouter',
              status: config.openrouter?.enabled ? 'active' : 'inactive',
              model: config.openrouter?.model || 'google/gemini-2.5-pro-preview',
              apiKey: '***************************', // Masked for security
              endpoint: 'https://openrouter.ai/api/v1',
              maxTokens: Number(config.openrouter?.maxTokens) || 8000,
              temperature: Number(config.openrouter?.temperature) || 0.7,
              isDefault: config.modelSelection?.fallbackOrder?.[0] === 'openrouter'
            }
          ];

          setProviders(transformedProviders);
        } else {
          throw new Error(data.error || 'Failed to load configuration');
        }

      } catch (err) {
        console.error('Error loading providers:', err);

        // Enhanced error handling with specific messages
        let errorMessage = 'Failed to load AI provider configuration';
        if (err instanceof Error) {
          if (err.message.includes('401') || err.message.includes('Unauthorized')) {
            errorMessage = 'Authentication failed. Please check your admin credentials.';
          } else if (err.message.includes('404')) {
            errorMessage = 'Configuration service is not available. Please contact support.';
          } else if (err.message.includes('500')) {
            errorMessage = 'Server error occurred. Please try again later.';
          } else if (err.message.includes('Failed to fetch')) {
            errorMessage = 'Network error. Please check your connection and try again.';
          }
        }

        setError(errorMessage);

        // Fallback to default providers if API fails
        setProviders([
          {
            id: 'openai',
            name: 'OpenAI',
            status: 'inactive',
            model: 'gpt-4o-2024-11-20',
            apiKey: '',
            maxTokens: 4000,
            temperature: 0.7,
            isDefault: true
          },
          {
            id: 'openrouter',
            name: 'OpenRouter',
            status: 'inactive',
            model: 'google/gemini-2.5-pro-preview',
            apiKey: '',
            endpoint: 'https://openrouter.ai/api/v1',
            maxTokens: 8000,
            temperature: 0.7,
            isDefault: false
          }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    loadProviders();
  }, []);

  const handleSaveConfig = async () => {
    setIsSaving(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Transform providers back to configuration format
      const configData = {
        openai: {
          enabled: providers.find(p => p.id === 'openai')?.status === 'active',
          model: providers.find(p => p.id === 'openai')?.model || 'gpt-4o-2024-11-20',
          maxTokens: providers.find(p => p.id === 'openai')?.maxTokens || 4000,
          temperature: providers.find(p => p.id === 'openai')?.temperature || 0.7,
          timeout: 60000,
          priority: providers.find(p => p.id === 'openai')?.isDefault ? 1 : 2
        },
        openrouter: {
          enabled: providers.find(p => p.id === 'openrouter')?.status === 'active',
          model: providers.find(p => p.id === 'openrouter')?.model || 'google/gemini-2.5-pro-preview',
          maxTokens: providers.find(p => p.id === 'openrouter')?.maxTokens || 8000,
          temperature: providers.find(p => p.id === 'openrouter')?.temperature || 0.7,
          implicitCaching: true,
          timeout: 120000,
          priority: providers.find(p => p.id === 'openrouter')?.isDefault ? 1 : 2
        },
        modelSelection: {
          strategy: 'auto',
          fallbackOrder: providers
            .filter(p => p.status === 'active')
            .sort((a, b) => (a.isDefault ? -1 : 1))
            .map(p => p.id),
          costThreshold: 0.01,
          qualityThreshold: 0.8
        }
      };

      // Call real configuration API
      const response = await fetch('/api/admin/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          section: 'ai-providers',
          action: 'update',
          data: configData
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setSuccessMessage('AI provider configuration saved successfully');

        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(null), 3000);
      } else {
        throw new Error(result.error || result.message || 'Failed to save configuration');
      }

    } catch (err) {
      setError('Failed to save configuration: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestProvider = async (providerId: string) => {
    setTestingProvider(providerId);
    setError(null);

    try {
      // Call real provider test API
      const response = await fetch(`/api/admin/config/test-providers?provider=${providerId}&detailed=true`, {
        method: 'GET',
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error(`Test failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success && result.result?.success) {
        // Update provider status to active on successful test
        setProviders(prev => prev.map(p =>
          p.id === providerId
            ? { ...p, status: 'active' as const }
            : p
        ));

        // Show success message with test details
        const responseTime = result.result.responseTime || 0;
        setSuccessMessage(`${providerId.toUpperCase()} test successful! Response time: ${responseTime}ms`);
        setTimeout(() => setSuccessMessage(null), 5000);
      } else {
        throw new Error(result.result?.error || result.error || 'Provider test failed');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to test ${providerId.toUpperCase()} provider: ${errorMessage}`);

      // Update provider status to error on failed test
      setProviders(prev => prev.map(p =>
        p.id === providerId
          ? { ...p, status: 'error' as const }
          : p
      ));
    } finally {
      setTestingProvider(null);
    }
  };

  const toggleApiKeyVisibility = (providerId: string) => {
    setShowApiKeys(prev => ({
      ...prev,
      [providerId]: !prev[providerId]
    }));
  };

  const updateProvider = (providerId: string, field: string, value: any) => {
    setProviders(prev => prev.map(p => 
      p.id === providerId 
        ? { ...p, [field]: value }
        : p
    ));
  };

  const setAsDefault = async (providerId: string) => {
    try {
      // Update local state immediately for responsive UI
      setProviders(prev => prev.map(p => ({
        ...p,
        isDefault: p.id === providerId
      })));

      // Show immediate feedback
      setSuccessMessage(`${providerId.toUpperCase()} set as default provider`);

      // Auto-save the configuration
      await handleSaveConfig();

    } catch (err) {
      // Revert the change if save failed
      setProviders(prev => prev.map(p => ({
        ...p,
        isDefault: false // Reset all to false, let user try again
      })));

      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to set default provider: ${errorMessage}`);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const maskApiKey = (apiKey: string) => {
    if (!apiKey) return 'Not configured';
    return apiKey.substring(0, 8) + '*'.repeat(20);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-400 mt-4">Loading AI configuration...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">AI Configuration</h1>
          <p className="text-gray-400">Manage AI providers and content generation settings</p>
        </div>
        
        <button
          onClick={handleSaveConfig}
          disabled={isSaving}
          className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
        >
          {isSaving ? (
            <>
              <LoadingSpinner size="sm" />
              <span>Saving...</span>
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              <span>Save Configuration</span>
            </>
          )}
        </button>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="bg-red-900 border border-red-700 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <p className="text-red-200">{error}</p>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-900 border border-green-700 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-400" />
            <p className="text-green-200">{successMessage}</p>
          </div>
        </div>
      )}

      {/* AI Providers */}
      <div className="space-y-4">
        {providers.map((provider) => (
          <div key={provider.id} className="bg-zinc-800 border border-black rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Settings className="w-6 h-6 text-blue-400" />
                <div>
                  <h3 className="text-lg font-semibold text-white">{provider.name}</h3>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(provider.status)}
                    <span className="text-sm text-gray-400 capitalize">{provider.status}</span>
                    {provider.isDefault && (
                      <span className="bg-blue-700 text-blue-200 text-xs px-2 py-1 rounded">
                        Default
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleTestProvider(provider.id)}
                  disabled={testingProvider === provider.id}
                  className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
                >
                  {testingProvider === provider.id ? (
                    <>
                      <LoadingSpinner size="sm" />
                      <span>Testing...</span>
                    </>
                  ) : (
                    <>
                      <TestTube className="w-4 h-4" />
                      <span>Test</span>
                    </>
                  )}
                </button>
                
                {!provider.isDefault && (
                  <button
                    onClick={() => setAsDefault(provider.id)}
                    className="bg-blue-700 hover:bg-blue-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
                  >
                    Set Default
                  </button>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Model */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Model
                </label>
                <input
                  type="text"
                  value={provider.model}
                  onChange={(e) => updateProvider(provider.id, 'model', e.target.value)}
                  className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                />
              </div>

              {/* API Key */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  API Key
                </label>
                <div className="relative">
                  <input
                    type={showApiKeys[provider.id] ? "text" : "password"}
                    value={provider.apiKey || ''}
                    onChange={(e) => updateProvider(provider.id, 'apiKey', e.target.value)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 pr-10 text-white focus:outline-none focus:border-blue-500"
                    placeholder="Enter API key..."
                  />
                  <button
                    type="button"
                    onClick={() => toggleApiKeyVisibility(provider.id)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                  >
                    {showApiKeys[provider.id] ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* Endpoint (for custom providers) */}
              {provider.endpoint && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Endpoint
                  </label>
                  <input
                    type="text"
                    value={provider.endpoint}
                    onChange={(e) => updateProvider(provider.id, 'endpoint', e.target.value)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                  />
                </div>
              )}

              {/* Max Tokens */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Max Tokens
                </label>
                <input
                  type="number"
                  value={isNaN(provider.maxTokens) ? '' : provider.maxTokens}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    updateProvider(provider.id, 'maxTokens', isNaN(value) ? 4000 : value);
                  }}
                  className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                  min="1000"
                  max="32000"
                  placeholder="4000"
                />
              </div>

              {/* Temperature */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Temperature ({isNaN(provider.temperature) ? '0.7' : provider.temperature.toFixed(1)})
                </label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={isNaN(provider.temperature) ? 0.7 : provider.temperature}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    updateProvider(provider.id, 'temperature', isNaN(value) ? 0.7 : value);
                  }}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>Conservative</span>
                  <span>Creative</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Help Section */}
      <div className="bg-blue-900 border border-blue-700 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-200 mb-3">Configuration Help</h3>
        <div className="space-y-2 text-blue-200 text-sm">
          <p>• <strong>Default Provider:</strong> Used for all content generation unless specified otherwise</p>
          <p>• <strong>Temperature:</strong> Controls creativity (0 = conservative, 2 = very creative)</p>
          <p>• <strong>Max Tokens:</strong> Maximum length of generated content</p>
          <p>• <strong>Test:</strong> Verifies API connectivity and authentication</p>
        </div>
      </div>
    </div>
  );
}
