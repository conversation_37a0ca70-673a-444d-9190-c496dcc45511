/**
 * Bulk Processing Engine
 * Orchestrates bulk processing operations with batch management and progress tracking
 */

import { createClient } from '@supabase/supabase-js';
import { BulkProcessingJob, InternalBulkProcessingJob, BulkToolData, JobStatus } from '@/lib/types';
import { JobPriority, JobType } from '@/lib/jobs/types';
import { getJobManager } from '@/lib/jobs';
import { getSynchronizationManager } from './sync-manager';
import { getTransactionManager } from './transaction-manager';
import { getErrorRecoveryManager, RecoveryContext, RecoveryResult } from './error-recovery';
import { getJobCleanupService } from './job-cleanup-service';
import { getToolCreationManager } from './tool-creation-manager';

// Bulk processing interfaces
export interface BulkProcessingOptions {
  batchSize: number;
  delayBetweenBatches: number;
  retryAttempts: number;
  aiProvider: 'openai' | 'openrouter';
  skipExisting: boolean;
  scrapeOnly?: boolean;
  generateContent?: boolean;
  resumeGeneration?: boolean;
  autoPublish?: boolean;
  priority?: 'low' | 'normal' | 'high';
  maxProcessingTime?: number; // Maximum time in milliseconds for job processing
}

// Database record interface for bulk processing jobs
export interface BulkProcessingJobDbRecord {
  id: string;
  job_type: 'text_file' | 'json_file' | 'manual_entry' | 'csv_import';
  status: JobStatus;
  total_items: number;
  processed_items: number;
  successful_items: number;
  failed_items: number;
  source_data: Record<string, unknown>;
  processing_options: Record<string, unknown>;
  progress_log: Array<Record<string, unknown>>;
  created_by: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  started_at?: string;
  submitted_by?: string;
  version?: number;
}

// Partial data interface for error recovery
export interface PartialJobData {
  processedBatches?: number;
  totalBatches?: number;
  lastCompletedBatch?: number;
  processedItems?: number;
  successfulItems?: number;
  failedBatch?: number;
  batchData?: BulkToolData[];
  toolId?: string;
  generatedContent?: Record<string, unknown>;
  mediaAssets?: Record<string, unknown>;
}

export interface BulkJobResult {
  jobId: string;
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  results: {
    successful: Array<{
      toolId: string;
      url: string;
      generatedAt: string;
    }>;
    failed: Array<{
      url: string;
      error: string;
      timestamp: string;
    }>;
  };
  processingTime: number;
  costEstimate?: number;
}

export interface BatchResult {
  batchIndex: number;
  totalItems: number;
  successful: number;
  failed: number;
  results: Array<{
    url: string;
    success: boolean;
    toolId?: string;
    error?: string;
    processingTime: number;
  }>;
}

/**
 * Bulk Processing Engine
 * Manages bulk operations with intelligent batching and error handling
 */
export class BulkProcessingEngine {
  private supabase;
  private jobManager;
  private syncManager;
  private transactionManager;
  private errorRecoveryManager;
  private cleanupService;
  private toolCreationManager;
  private activeJobs = new Map<string, InternalBulkProcessingJob>();
  private jobTimeouts = new Map<string, NodeJS.Timeout>();
  private jobResources = new Map<string, Set<string>>();
  private memoryMonitor: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private jobVersions = new Map<string, number>(); // Track job versions for optimistic locking

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    this.jobManager = getJobManager();
    this.syncManager = getSynchronizationManager();
    this.transactionManager = getTransactionManager();
    this.errorRecoveryManager = getErrorRecoveryManager();
    this.cleanupService = getJobCleanupService();
    this.toolCreationManager = getToolCreationManager();

    // Start memory monitoring and cleanup
    this.startMemoryMonitoring();
    this.startPeriodicCleanup();

    // Start automated job cleanup
    this.cleanupService.startAutomatedCleanup(30); // Every 30 minutes
    this.cleanupService.startCleanupMonitoring(15); // Every 15 minutes
  }

  /**
   * Create and start a bulk processing job
   */
  async createBulkJob(
    data: BulkToolData[],
    options: BulkProcessingOptions,
    metadata: {
      jobType: 'text_file' | 'json_file' | 'manual_entry';
      filename?: string;
      submittedBy?: string;
    }
  ): Promise<InternalBulkProcessingJob> {
    try {
      // Generate job ID
      const jobId = this.generateJobId();

      // Create bulk processing job record (internal representation)
      const bulkJob: InternalBulkProcessingJob = {
        id: jobId,
        jobType: metadata.jobType,
        status: 'pending',
        totalItems: data.length,
        processedItems: 0,
        successfulItems: 0,
        failedItems: 0,
        sourceData: {
          filename: metadata.filename,
          urls: data.map(item => item.url),
          tools: data.map(item => ({
            url: item.url,
            name: item.providedData.name || undefined,
            category: item.providedData.category || undefined,
            description: item.providedData.description || undefined,
          })),
        },
        processingOptions: {
          batchSize: options.batchSize,
          delayBetweenBatches: options.delayBetweenBatches,
          retryAttempts: options.retryAttempts,
          aiProvider: options.aiProvider,
          skipExisting: options.skipExisting,
        },
        results: {
          successful: [],
          failed: [],
        },
        progressLog: [],
        createdBy: metadata.submittedBy || 'system',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Store in database (convert to database schema)
      await this.storeBulkJobInDatabase(this.convertToDbSchema(bulkJob));

      // Add to active jobs and initialize resources
      this.activeJobs.set(jobId, bulkJob);
      this.jobResources.set(jobId, new Set());
      this.jobVersions.set(jobId, 1); // Initialize version tracking

      // Set up job timeout
      this.setupJobTimeout(jobId, options.maxProcessingTime || 3600000); // 1 hour default

      // Start processing with proper error handling
      this.processBulkJob(jobId, data).catch(error => {
        console.error(`Bulk job ${jobId} failed:`, error);
        this.failBulkJobWithCleanup(jobId, error.message);
      });

      console.log(`✅ Bulk job ${jobId} created with ${data.length} items`);
      return bulkJob;
    } catch (error) {
      console.error('Failed to create bulk job:', error);
      throw error;
    }
  }

  /**
   * Process a bulk job with intelligent batching
   */
  private async processBulkJob(jobId: string, originalData?: BulkToolData[]): Promise<void> {
    const bulkJob = this.activeJobs.get(jobId);
    if (!bulkJob) {
      throw new Error(`Bulk job ${jobId} not found`);
    }

    try {
      // Update status to processing
      await this.updateBulkJobStatus(jobId, 'processing');

      // Create batches - reconstruct BulkToolData from sourceData if originalData not provided
      const toolsData = originalData || this.reconstructBulkToolData(bulkJob.sourceData);
      const batches = this.createBatches(
        toolsData,
        bulkJob.processingOptions
      );

      console.log(`🔄 Processing ${batches.length} batches for job ${jobId}`);

      // Process batches sequentially
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        // Check if job was stopped or paused
        const currentJob = this.activeJobs.get(jobId);
        if (!currentJob || currentJob.status === 'cancelled' || currentJob.status === 'paused') {
          console.log(`⏸️ Bulk job ${jobId} was ${currentJob?.status || 'stopped'}`);
          break;
        }

        const batch = batches[batchIndex];
        console.log(`📦 Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} items)`);

        // Process batch
        const batchResult = await this.processBatch(jobId, batchIndex, batch, bulkJob.processingOptions);

        // Update bulk job progress
        await this.updateBulkJobProgress(jobId, batchResult);

        // Add delay between batches (except for last batch)
        if (batchIndex < batches.length - 1 && bulkJob.processingOptions.delayBetweenBatches > 0) {
          await new Promise(resolve =>
            setTimeout(resolve, bulkJob.processingOptions.delayBetweenBatches)
          );
        }
      }

      // Complete the job
      await this.completeBulkJob(jobId);
      console.log(`✅ Bulk job ${jobId} completed successfully`);

    } catch (error) {
      console.error(`❌ Bulk job ${jobId} failed:`, error);

      // Attempt job-level recovery
      const recoveryResult = await this.attemptJobRecovery(
        jobId,
        error as Error,
        { processedBatches: 0, totalBatches: 0 },
        'bulk_processing'
      );

      if (recoveryResult.recovered) {
        console.log(`✅ Job ${jobId} recovered successfully`);
        return;
      }

      await this.failBulkJobWithCleanup(jobId, (error as Error).message);
      throw error;
    }
  }

  /**
   * Create batches from tool data
   */
  private createBatches(tools: BulkToolData[], options: BulkProcessingOptions): BulkToolData[][] {
    const batches: BulkToolData[][] = [];
    const batchSize = Math.max(1, Math.min(options.batchSize, 20)); // Ensure reasonable batch size

    for (let i = 0; i < tools.length; i += batchSize) {
      batches.push(tools.slice(i, i + batchSize));
    }

    return batches;
  }

  /**
   * Process a single batch
   */
  private async processBatch(
    jobId: string,
    batchIndex: number,
    batch: BulkToolData[],
    options?: BulkProcessingOptions
  ): Promise<BatchResult> {
    const results: BatchResult['results'] = [];
    let successful = 0;
    let failed = 0;

    // Step 1: Ensure all tools exist in the database first
    console.log(`🔧 Ensuring ${batch.length} tools exist in database...`);
    const urlToToolIdMap = await this.toolCreationManager.ensureToolsExist(batch);

    // Step 2: Verify all tools exist and are properly committed to database
    console.log(`🔍 Verifying all tools are properly committed to database...`);
    await this.verifyAllToolsExist(urlToToolIdMap);
    console.log(`✅ All tools verified and committed, safe to start job processing...`);

    // Process items in batch with controlled concurrency
    const maxConcurrent = 2; // Limit concurrent processing to avoid overwhelming APIs
    const chunks = this.chunkArray(batch, maxConcurrent);

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (tool) => {
        const startTime = Date.now();

        try {
          // Get the tool ID for this URL
          const toolId = urlToToolIdMap.get(tool.url);
          if (!toolId) {
            throw new Error(`Failed to create or find tool record for ${tool.url}`);
          }

          // Enhanced tool validation before creating job
          await this.validateToolForJobCreation(toolId, tool.url);

          // Add database consistency delay to prevent race conditions
          // This ensures the tool record is fully committed before job creation
          await new Promise(resolve => setTimeout(resolve, 500)); // Increased to 500ms for better consistency

          // Create individual job for this tool with proper tool ID
          const individualJob = await this.jobManager.createJob(
            JobType.TOOL_PROCESSING,
            {
              toolId: toolId, // Use actual tool ID, not URL
              url: tool.url,
              providedData: tool.providedData,
              needsGeneration: tool.needsGeneration,
              bulkJobId: jobId,
              batchIndex,
              processingOptions: options, // Pass processing options to individual job
            },
            {
              priority: this.mapPriorityToJobPriority(options?.priority || 'normal'),
            }
          );

          // Link tool to job for tracking
          await this.toolCreationManager.linkToolToJob(toolId, individualJob.id);

          // Wait for job completion (with timeout)
          const result = await this.waitForJobCompletion(individualJob.id, 300000); // 5 minute timeout

          if (result.success) {
            successful++;
            // Update tool status to completed
            await this.toolCreationManager.updateToolStatus(toolId, 'completed');
            return {
              url: tool.url,
              success: true,
              toolId: result.toolId || toolId,
              processingTime: Date.now() - startTime,
            };
          } else {
            failed++;
            // Update tool status to failed
            await this.toolCreationManager.updateToolStatus(toolId, 'failed', result.error);
            return {
              url: tool.url,
              success: false,
              error: result.error || 'Unknown error',
              processingTime: Date.now() - startTime,
            };
          }
        } catch (error) {
          failed++;
          // Get the tool ID for error reporting
          const toolId = urlToToolIdMap.get(tool.url);
          if (toolId) {
            await this.toolCreationManager.updateToolStatus(toolId, 'failed', (error as Error).message);
          }
          return {
            url: tool.url,
            success: false,
            error: (error as Error).message,
            processingTime: Date.now() - startTime,
          };
        }
      });

      const chunkResults = await Promise.allSettled(chunkPromises);
      results.push(...chunkResults.map(r => 
        r.status === 'fulfilled' ? r.value : {
          url: 'unknown',
          success: false,
          error: 'Promise rejected',
          processingTime: 0,
        }
      ));
    }

    return {
      batchIndex,
      totalItems: batch.length,
      successful,
      failed,
      results,
    };
  }

  /**
   * Wait for individual job completion
   */
  private async waitForJobCompletion(jobId: string, timeoutMs: number): Promise<{
    success: boolean;
    toolId?: string;
    error?: string;
  }> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      try {
        const job = await this.jobManager.getJob(jobId);

        if (!job) {
          return {
            success: false,
            error: 'Job not found',
          };
        }

        if (job.status === 'completed') {
          return {
            success: true,
            toolId: job.result?.toolId,
          };
        }

        if (job.status === 'failed') {
          return {
            success: false,
            error: job.error || 'Job failed',
          };
        }
        
        // Wait before checking again
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        return {
          success: false,
          error: (error as Error).message,
        };
      }
    }
    
    return {
      success: false,
      error: 'Job timeout',
    };
  }

  /**
   * Utility function to chunk array
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Map priority string to JobPriority enum
   */
  private mapPriorityToJobPriority(priority: string): JobPriority {
    switch (priority) {
      case 'low': return JobPriority.LOW;
      case 'high': return JobPriority.HIGH;
      default: return JobPriority.NORMAL;
    }
  }

  /**
   * Generate unique job ID
   */
  private generateJobId(): string {
    // Generate a proper UUID v4 format for database compatibility
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Reconstruct BulkToolData from sourceData for processing
   */
  private reconstructBulkToolData(sourceData: InternalBulkProcessingJob['sourceData']): BulkToolData[] {
    return (sourceData.urls || []).map((url: string) => ({
      url,
      providedData: {
        name: null,
        category: null,
        description: null,
        pricing: null,
        features: null,
      },
      needsGeneration: {
        name: true,
        description: true,
        features: true,
        pricing: true,
        prosAndCons: true,
        haiku: true,
        hashtags: true,
      },
    }));
  }

  /**
   * Store bulk job in database
   */
  private async storeBulkJobInDatabase(job: BulkProcessingJob): Promise<void> {
    const jobData = {
      id: job.id,
      job_type: job.job_type,
      status: job.status,
      total_items: job.total_items,
      processed_items: job.processed_items,
      successful_items: job.successful_items,
      failed_items: job.failed_items,
      source_data: job.source_data,
      processing_options: job.processing_options,
      progress_log: job.progress_log,
      created_by: job.created_by,
      created_at: job.created_at,
      updated_at: job.updated_at,
    };

    const { error } = await this.supabase
      .from('bulk_processing_jobs')
      .insert(jobData);

    if (error) {
      console.error('Failed to store bulk job in database:', error);
      throw error;
    }
  }

  /**
   * Update bulk job status atomically
   */
  private async updateBulkJobStatus(jobId: string, status: JobStatus): Promise<void> {
    const expectedVersion = this.jobVersions.get(jobId);

    const result = await this.syncManager.retryOnVersionMismatch(async () => {
      return await this.syncManager.updateJobStatusAtomic(jobId, status, expectedVersion);
    });

    if (!result.success) {
      console.error(`Failed to update bulk job ${jobId} status:`, result.error);
      throw new Error(`Status update failed: ${result.error}`);
    }

    // Update local state
    const job = this.activeJobs.get(jobId);
    if (job) {
      job.status = status;
      job.updatedAt = result.updatedAt || new Date().toISOString();
    }

    // Update version tracking
    if (result.newVersion) {
      this.jobVersions.set(jobId, result.newVersion);
    }

    console.log(`✅ Updated job ${jobId} status: ${result.oldStatus} → ${result.newStatus} (v${result.newVersion})`);
  }

  /**
   * Update bulk job progress atomically
   */
  private async updateBulkJobProgress(jobId: string, batchResult: BatchResult): Promise<void> {
    return await this.syncManager.withLock(jobId, async () => {
      const job = this.activeJobs.get(jobId);
      if (!job) return;

      // Calculate new totals
      const newProcessedItems = job.processedItems + batchResult.totalItems;
      const newSuccessfulItems = job.successfulItems + batchResult.successful;
      const newFailedItems = job.failedItems + batchResult.failed;

      // Get fresh version from database to avoid stale version issues
      const currentVersion = await this.syncManager.getJobVersion(jobId);

      const result = await this.syncManager.retryOnVersionMismatch(async () => {
        return await this.syncManager.updateJobProgressAtomic(
          jobId,
          newProcessedItems,
          newSuccessfulItems,
          newFailedItems,
          currentVersion || undefined
        );
      });

      if (!result.success) {
        console.error(`Failed to update bulk job ${jobId} progress:`, result.error);
        // Don't throw error for version mismatch - just log and continue
        if (result.error === 'version_mismatch') {
          console.warn(`Version mismatch for job ${jobId} - continuing with local state update`);
        } else {
          throw new Error(`Progress update failed: ${result.error}`);
        }
      }

      // Update local state atomically
      job.processedItems = newProcessedItems;
      job.successfulItems = newSuccessfulItems;
      job.failedItems = newFailedItems;
      job.updatedAt = result.updatedAt || new Date().toISOString();

      // Update results arrays
      batchResult.results.forEach(result => {
        if (result.success && result.toolId) {
          job.results?.successful.push({
            toolId: result.toolId,
            url: result.url,
            generatedAt: new Date().toISOString(),
          });
        } else {
          job.results?.failed.push({
            url: result.url,
            error: result.error || 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      });

      // Update version tracking with fresh version
      if (result.newVersion) {
        this.jobVersions.set(jobId, result.newVersion);
      } else if (currentVersion) {
        // Update with current version if no new version returned
        this.jobVersions.set(jobId, currentVersion);
      }

      console.log(`✅ Updated job ${jobId} progress: ${newProcessedItems}/${job.totalItems} (v${result.newVersion || currentVersion})`);
    });
  }

  /**
   * Complete bulk job atomically
   */
  private async completeBulkJob(jobId: string): Promise<void> {
    // Get fresh version from database to avoid stale version issues
    const currentVersion = await this.syncManager.getJobVersion(jobId);

    const result = await this.syncManager.retryOnVersionMismatch(async () => {
      return await this.syncManager.completeJobAtomic(jobId, currentVersion || undefined);
    });

    if (!result.success) {
      console.error(`Failed to complete bulk job ${jobId}:`, result.error);
      // Don't throw error for version mismatch - just log and continue
      if (result.error === 'version_mismatch') {
        console.warn(`Version mismatch during job completion for ${jobId} - marking as completed locally`);
      } else {
        throw new Error(`Job completion failed: ${result.error}`);
      }
    }

    // Update local state
    const job = this.activeJobs.get(jobId);
    if (job) {
      job.status = 'completed';
      job.completedAt = result.updatedAt || new Date().toISOString();
      job.updatedAt = result.updatedAt || new Date().toISOString();
    }

    // Update version tracking with fresh version
    if (result.newVersion) {
      this.jobVersions.set(jobId, result.newVersion);
    } else if (currentVersion) {
      // Update with current version if no new version returned
      this.jobVersions.set(jobId, currentVersion);
    }

    console.log(`✅ Completed job ${jobId} (v${result.newVersion || currentVersion})`);

    // Perform comprehensive cleanup
    await this.cleanupJob(jobId);
  }

  /**
   * Fail bulk job
   */
  private async failBulkJob(jobId: string, errorMessage: string): Promise<void> {
    const job = this.activeJobs.get(jobId);
    if (!job) return;

    job.status = 'failed';
    // Add error to progress log instead of a separate error field
    job.progressLog.push({
      timestamp: new Date().toISOString(),
      message: errorMessage,
      level: 'error'
    });
    job.updatedAt = new Date().toISOString();

    const { error } = await this.supabase
      .from('bulk_processing_jobs')
      .update({
        status: 'failed',
        updated_at: job.updatedAt,
      })
      .eq('id', jobId);

    if (error) {
      console.error(`Failed to update bulk job ${jobId} failure:`, error);
    }

    // Remove from active jobs
    this.activeJobs.delete(jobId);
  }

  /**
   * Get bulk job status
   */
  async getBulkJob(jobId: string): Promise<InternalBulkProcessingJob | null> {
    // Check active jobs first
    const activeJob = this.activeJobs.get(jobId);
    if (activeJob) {
      return activeJob;
    }

    // Query database
    const { data, error } = await this.supabase
      .from('bulk_processing_jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (error || !data) {
      return null;
    }

    return this.mapDatabaseToBulkJob(data);
  }

  /**
   * Get all bulk jobs with filtering
   */
  async getBulkJobs(filter: {
    status?: JobStatus;
    limit?: number;
    offset?: number;
  } = {}): Promise<InternalBulkProcessingJob[]> {
    let query = this.supabase
      .from('bulk_processing_jobs')
      .select('*')
      .order('created_at', { ascending: false });

    if (filter.status) {
      query = query.eq('status', filter.status);
    }

    if (filter.limit) {
      query = query.limit(filter.limit);
    }

    if (filter.offset) {
      query = query.range(filter.offset, (filter.offset + (filter.limit || 10)) - 1);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Failed to fetch bulk jobs:', error);
      return [];
    }

    return (data || []).map(this.mapDatabaseToBulkJob);
  }

  /**
   * Pause bulk job
   */
  async pauseBulkJob(jobId: string): Promise<void> {
    await this.updateBulkJobStatus(jobId, 'paused');
    console.log(`⏸️ Bulk job ${jobId} paused`);
  }

  /**
   * Resume bulk job
   */
  async resumeBulkJob(jobId: string): Promise<void> {
    const job = await this.getBulkJob(jobId);
    if (!job || job.status !== 'paused') {
      throw new Error(`Cannot resume job ${jobId}: job not found or not paused`);
    }

    // Add back to active jobs and resume processing
    this.activeJobs.set(jobId, job);
    await this.updateBulkJobStatus(jobId, 'processing');

    // Resume processing from where it left off
    this.processBulkJob(jobId).catch(error => {
      console.error(`Resumed bulk job ${jobId} failed:`, error);
      this.failBulkJob(jobId, error.message);
    });

    console.log(`▶️ Bulk job ${jobId} resumed`);
  }

  /**
   * Cancel bulk job
   */
  async cancelBulkJob(jobId: string): Promise<void> {
    await this.updateBulkJobStatus(jobId, 'cancelled');
    await this.cleanupJob(jobId);
    console.log(`⏹️ Bulk job ${jobId} cancelled`);
  }

  /**
   * Map database record to InternalBulkProcessingJob
   */
  private mapDatabaseToBulkJob(data: BulkProcessingJobDbRecord): InternalBulkProcessingJob {
    return {
      id: data.id,
      jobType: data.job_type,
      status: data.status,
      totalItems: data.total_items,
      processedItems: data.processed_items,
      successfulItems: data.successful_items,
      failedItems: data.failed_items,
      sourceData: data.source_data,
      processingOptions: data.processing_options,
      results: {
        successful: [],
        failed: []
      },
      progressLog: data.progress_log || [],
      createdBy: data.created_by || data.submitted_by || 'system',
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      startedAt: data.started_at,
      completedAt: data.completed_at,
    };
  }

  /**
   * Convert internal representation to database schema
   */
  private convertToDbSchema(job: InternalBulkProcessingJob): BulkProcessingJob {
    return {
      id: job.id,
      job_type: job.jobType,
      status: job.status,
      total_items: job.totalItems,
      processed_items: job.processedItems,
      successful_items: job.successfulItems,
      failed_items: job.failedItems,
      source_data: job.sourceData,
      processing_options: job.processingOptions,
      progress_log: job.progressLog,
      created_by: job.createdBy,
      created_at: job.createdAt,
      updated_at: job.updatedAt,
      started_at: job.startedAt,
      completed_at: job.completedAt,
    };
  }

  // ===== ERROR RECOVERY METHODS =====

  /**
   * Attempt to recover from job-level failure
   */
  private async attemptJobRecovery(
    jobId: string,
    error: Error,
    partialData: PartialJobData,
    failurePoint: string
  ): Promise<RecoveryResult> {
    const recoveryContext: RecoveryContext = {
      jobId,
      operationType: 'tool_creation',
      partialData,
      failurePoint,
      error: error.message,
      timestamp: new Date(),
      retryCount: 0,
      maxRetries: 3
    };

    return await this.errorRecoveryManager.recoverFromFailure(recoveryContext);
  }

  /**
   * Verify all tools exist and are properly committed to database
   */
  private async verifyAllToolsExist(urlToToolIdMap: Map<string, string>): Promise<void> {
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const toolIds = Array.from(urlToToolIdMap.values());
    console.log(`🔍 Verifying ${toolIds.length} tools exist in database...`);

    // Query all tools at once
    const { data: tools, error } = await supabase
      .from('tools')
      .select('id, name, submission_source')
      .in('id', toolIds);

    if (error) {
      throw new Error(`Failed to verify tools exist: ${error.message}`);
    }

    if (!tools || tools.length !== toolIds.length) {
      const foundIds = new Set(tools?.map(t => t.id) || []);
      const missingIds = toolIds.filter(id => !foundIds.has(id));
      throw new Error(`Tools not found in database: ${missingIds.join(', ')}`);
    }

    // Verify all tools have correct submission_source
    const incorrectTools = tools.filter(t => t.submission_source !== 'bulk_processing');
    if (incorrectTools.length > 0) {
      console.warn(`⚠️ ${incorrectTools.length} tools have incorrect submission_source, fixing...`);

      // Fix submission_source for all incorrect tools
      const { error: updateError } = await supabase
        .from('tools')
        .update({
          submission_source: 'bulk_processing',
          submission_type: 'admin',
          updated_at: new Date().toISOString()
        })
        .in('id', incorrectTools.map(t => t.id));

      if (updateError) {
        console.error(`❌ Failed to fix submission_source: ${updateError.message}`);
      } else {
        console.log(`✅ Fixed submission_source for ${incorrectTools.length} tools`);
      }
    }

    console.log(`✅ All ${tools.length} tools verified and properly configured`);
  }

  /**
   * Enhanced tool validation before job creation with comprehensive checks
   */
  private async validateToolForJobCreation(toolId: string, url: string): Promise<void> {
    try {
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );

      console.log(`🔍 Validating tool ${toolId} for job creation...`);

      // 1. Verify tool exists and has correct submission source
      const { data: tool, error: toolError } = await supabase
        .from('tools')
        .select('id, name, website, submission_source, submission_type, ai_generation_status')
        .eq('id', toolId)
        .single();

      if (toolError) {
        throw new Error(`Tool validation failed - database error: ${toolError.message}`);
      }

      if (!tool) {
        throw new Error(`Tool validation failed - tool ${toolId} not found in database`);
      }

      // 2. Verify submission source is correct for bulk processing
      if (tool.submission_source !== 'bulk_processing') {
        console.warn(`🔧 Tool ${toolId} has incorrect submission_source: ${tool.submission_source}, fixing...`);

        const { error: updateError } = await supabase
          .from('tools')
          .update({
            submission_source: 'bulk_processing',
            submission_type: 'admin',
            updated_at: new Date().toISOString()
          })
          .eq('id', toolId);

        if (updateError) {
          throw new Error(`Failed to fix submission_source for tool ${toolId}: ${updateError.message}`);
        }

        console.log(`✅ Fixed submission_source for tool ${toolId}`);
      }

      // 3. Check for any active jobs for this tool
      const { data: activeJobs, error: jobError } = await supabase
        .from('ai_generation_jobs')
        .select('id, job_type, status')
        .eq('tool_id', toolId)
        .in('status', ['pending', 'processing']);

      if (jobError) {
        console.warn(`⚠️ Could not check active jobs for tool ${toolId}: ${jobError.message}`);
      } else if (activeJobs && activeJobs.length > 0) {
        console.warn(`⚠️ Tool ${toolId} has ${activeJobs.length} active job(s):`);
        activeJobs.forEach(job => {
          console.warn(`  Job ${job.id}: ${job.job_type} - ${job.status}`);
        });
        console.warn(`Proceeding with job creation, but this may indicate a race condition`);
      }

      // 4. Validate URL matches
      if (tool.website !== url) {
        console.warn(`⚠️ URL mismatch for tool ${toolId}: DB has ${tool.website}, processing ${url}`);
      }

      console.log(`✅ Tool ${toolId} validation passed - ready for job creation`);
    } catch (error) {
      console.error(`❌ Tool validation failed for ${toolId}:`, error);
      throw error;
    }
  }

  /**
   * Verify tool has correct submission_source before starting content generation
   * @deprecated Use validateToolForJobCreation instead
   */
  private async verifyToolSubmissionSource(toolId: string): Promise<void> {
    try {
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );

      const { data: tool, error } = await supabase
        .from('tools')
        .select('submission_source, name')
        .eq('id', toolId)
        .single();

      if (error) {
        console.warn(`⚠️ Could not verify submission_source for tool ${toolId}: ${error.message}`);
        return;
      }

      if (tool.submission_source !== 'bulk_processing') {
        console.warn(`🔧 Tool ${toolId} (${tool.name}) has submission_source "${tool.submission_source}", fixing to "bulk_processing"`);

        // Fix the submission_source
        const { error: updateError } = await supabase
          .from('tools')
          .update({
            submission_source: 'bulk_processing',
            submission_type: 'admin',
            updated_at: new Date().toISOString()
          })
          .eq('id', toolId);

        if (updateError) {
          console.error(`❌ Failed to fix submission_source for tool ${toolId}: ${updateError.message}`);
        } else {
          console.log(`✅ Fixed submission_source for tool ${toolId}`);
        }
      } else {
        console.log(`✅ Tool ${toolId} has correct submission_source: bulk_processing`);
      }
    } catch (error) {
      console.warn(`⚠️ Error verifying submission_source for tool ${toolId}:`, error);
    }
  }

  // Note: Some methods below are currently unused but kept for future error recovery features
  // TODO: Remove unused methods in future cleanup: attemptBatchRecovery, processBatchWithRecovery,
  // validateToolData, addJobResource, removeJobResource

  /**
   * Attempt to recover from batch-level failure
   */
  private async attemptBatchRecovery(
    jobId: string,
    batch: BulkToolData[],
    error: Error,
    partialData: PartialJobData,
    batchIndex: number
  ): Promise<RecoveryResult> {
    const recoveryContext: RecoveryContext = {
      jobId,
      operationType: 'tool_creation',
      partialData: { ...partialData, failedBatch: batchIndex, batchData: batch },
      failurePoint: `batch_${batchIndex}`,
      error: error.message,
      timestamp: new Date(),
      retryCount: 0,
      maxRetries: 2
    };

    return await this.errorRecoveryManager.recoverFromFailure(recoveryContext);
  }

  /**
   * Process batch with error recovery
   */
  private async processBatchWithRecovery(
    jobId: string,
    batch: BulkToolData[],
    options: BulkProcessingOptions,
    transactionId: string
  ): Promise<BatchResult> {
    return await this.errorRecoveryManager.retryWithBackoff(async () => {
      // Add transaction operation for batch processing
      this.transactionManager.addOperation(transactionId, {
        type: 'rpc',
        rpcName: 'process_batch',
        rpcParams: { jobId, batch: batch.map(b => b.url) }
      });

      // Process the batch normally
      return await this.processBatch(jobId, 0, batch, options);
    }, {
      maxRetries: 3,
      baseDelayMs: 1000,
      retryableErrors: ['network_timeout', 'rate_limit_exceeded', 'api_timeout']
    });
  }

  /**
   * Validate tool data with comprehensive error reporting
   */
  private validateToolData(toolData: BulkToolData): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    sanitizedData?: BulkToolData;
  } {
    const schema = {
      required: ['url'],
      properties: {
        url: { type: 'string', format: 'url' },
        providedData: {
          properties: {
            name: { type: 'string', maxLength: 200, truncate: true },
            description: { type: 'string', maxLength: 2000, truncate: true },
            category: { type: 'string', maxLength: 100 }
          }
        }
      }
    };

    const validationResult = this.errorRecoveryManager.validateData(toolData, schema);

    return {
      isValid: validationResult.isValid,
      errors: validationResult.errors.map(e => e.error),
      warnings: validationResult.warnings.map(w => w.error),
      sanitizedData: validationResult.sanitizedData as BulkToolData
    };
  }

  // ===== MEMORY MANAGEMENT METHODS =====

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    this.memoryMonitor = setInterval(() => {
      this.checkMemoryUsage();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Start periodic cleanup
   */
  private startPeriodicCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.performPeriodicCleanup();
    }, 300000); // Cleanup every 5 minutes
  }

  /**
   * Check memory usage and trigger cleanup if needed
   */
  private checkMemoryUsage(): void {
    const memUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const heapTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024);

    console.log(`📊 Memory usage: ${heapUsedMB}MB / ${heapTotalMB}MB (${this.activeJobs.size} active jobs)`);

    // Trigger cleanup if memory usage is high (adjusted for 7GB limit)
    if (heapUsedMB > 5120 || this.activeJobs.size > 50) { // 5GB threshold for 7GB limit
      console.log('⚠️ High memory usage detected, triggering cleanup');
      this.performEmergencyCleanup();
    }
  }

  /**
   * Setup job timeout
   */
  private setupJobTimeout(jobId: string, timeoutMs: number): void {
    const timeout = setTimeout(() => {
      console.log(`⏰ Job ${jobId} timed out after ${timeoutMs}ms`);
      this.failBulkJobWithCleanup(jobId, 'Job timeout exceeded');
    }, timeoutMs);

    this.jobTimeouts.set(jobId, timeout);
  }

  /**
   * Clear job timeout
   */
  private clearJobTimeout(jobId: string): void {
    const timeout = this.jobTimeouts.get(jobId);
    if (timeout) {
      clearTimeout(timeout);
      this.jobTimeouts.delete(jobId);
    }
  }

  /**
   * Add resource to job tracking
   */
  private addJobResource(jobId: string, resourceId: string): void {
    const resources = this.jobResources.get(jobId);
    if (resources) {
      resources.add(resourceId);
    }
  }

  /**
   * Remove resource from job tracking
   */
  private removeJobResource(jobId: string, resourceId: string): void {
    const resources = this.jobResources.get(jobId);
    if (resources) {
      resources.delete(resourceId);
    }
  }

  /**
   * Cleanup job resources
   */
  private async cleanupJobResources(jobId: string): Promise<void> {
    const resources = this.jobResources.get(jobId);
    if (!resources) return;

    console.log(`🧹 Cleaning up ${resources.size} resources for job ${jobId}`);

    for (const resourceId of resources) {
      try {
        // Cleanup different types of resources
        if (resourceId.startsWith('temp_file_')) {
          // Cleanup temporary files (if any)
          console.log(`🗑️ Cleaning up temp file: ${resourceId}`);
        } else if (resourceId.startsWith('job_')) {
          // Cleanup child jobs
          await this.jobManager.deleteJob(resourceId);
        } else if (resourceId.startsWith('cache_')) {
          // Cleanup cache entries
          console.log(`🗑️ Cleaning up cache: ${resourceId}`);
        }
      } catch (error) {
        console.error(`Failed to cleanup resource ${resourceId}:`, error);
      }
    }

    this.jobResources.delete(jobId);
  }

  /**
   * Perform periodic cleanup
   */
  private async performPeriodicCleanup(): Promise<void> {
    console.log('🧹 Performing periodic cleanup...');

    const now = Date.now();
    const staleJobIds: string[] = [];

    // Find stale jobs (older than 2 hours)
    for (const [jobId, job] of this.activeJobs.entries()) {
      const jobAge = now - new Date(job.createdAt).getTime();
      const twoHours = 2 * 60 * 60 * 1000;

      if (jobAge > twoHours && (job.status === 'completed' || job.status === 'failed')) {
        staleJobIds.push(jobId);
      }
    }

    // Cleanup stale jobs
    for (const jobId of staleJobIds) {
      console.log(`🗑️ Cleaning up stale job: ${jobId}`);
      await this.cleanupJob(jobId);
    }

    console.log(`✅ Periodic cleanup completed (${staleJobIds.length} jobs cleaned)`);
  }

  /**
   * Perform emergency cleanup when memory is high
   */
  private async performEmergencyCleanup(): Promise<void> {
    console.log('🚨 Performing emergency cleanup...');

    const completedJobs: string[] = [];
    const failedJobs: string[] = [];

    // Find completed and failed jobs for immediate cleanup
    for (const [jobId, job] of this.activeJobs.entries()) {
      if (job.status === 'completed') {
        completedJobs.push(jobId);
      } else if (job.status === 'failed') {
        failedJobs.push(jobId);
      }
    }

    // Cleanup completed jobs first
    for (const jobId of completedJobs) {
      await this.cleanupJob(jobId);
    }

    // Cleanup failed jobs
    for (const jobId of failedJobs) {
      await this.cleanupJob(jobId);
    }

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    console.log(`🚨 Emergency cleanup completed (${completedJobs.length + failedJobs.length} jobs cleaned)`);
  }

  /**
   * Comprehensive job cleanup
   */
  private async cleanupJob(jobId: string): Promise<void> {
    try {
      // Clear timeout
      this.clearJobTimeout(jobId);

      // Cleanup resources
      await this.cleanupJobResources(jobId);

      // Remove from active jobs and version tracking
      this.activeJobs.delete(jobId);
      this.jobVersions.delete(jobId);

      console.log(`✅ Job ${jobId} cleaned up successfully`);
    } catch (error) {
      console.error(`Failed to cleanup job ${jobId}:`, error);
    }
  }

  /**
   * Fail bulk job with comprehensive cleanup
   */
  private async failBulkJobWithCleanup(jobId: string, errorMessage: string): Promise<void> {
    try {
      // First, update the job status in database
      await this.failBulkJob(jobId, errorMessage);

      // Then perform comprehensive cleanup
      await this.cleanupJob(jobId);
    } catch (error) {
      console.error(`Failed to fail job with cleanup ${jobId}:`, error);
      // Ensure cleanup happens even if database update fails
      await this.cleanupJob(jobId);
    }
  }

  /**
   * Shutdown cleanup - call this when shutting down the engine
   */
  public async shutdown(): Promise<void> {
    console.log('🛑 Shutting down bulk processing engine...');

    // Clear intervals
    if (this.memoryMonitor) {
      clearInterval(this.memoryMonitor);
      this.memoryMonitor = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Clear all timeouts
    for (const timeout of this.jobTimeouts.values()) {
      clearTimeout(timeout);
    }
    this.jobTimeouts.clear();

    // Cleanup all active jobs
    const activeJobIds = Array.from(this.activeJobs.keys());
    for (const jobId of activeJobIds) {
      await this.cleanupJob(jobId);
    }

    // Clear version tracking
    this.jobVersions.clear();

    // Shutdown sync manager
    this.syncManager.shutdown();

    // Shutdown cleanup service
    this.cleanupService.shutdown();

    console.log('✅ Bulk processing engine shutdown complete');
  }
}

// Singleton instance
let bulkProcessingEngine: BulkProcessingEngine | null = null;

export function getBulkProcessingEngine(): BulkProcessingEngine {
  if (!bulkProcessingEngine) {
    bulkProcessingEngine = new BulkProcessingEngine();
  }
  return bulkProcessingEngine;
}
