#!/usr/bin/env tsx

/**
 * Test UUID Generation Fix Only
 * 
 * This script tests just the UUID generation logic without requiring
 * full environment setup or database connections.
 */

function isValidUUID(str: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

/**
 * Generate unique job ID as UUID (copied from enhanced-queue.ts)
 */
function generateId(): string {
  // Generate a proper UUID v4 format for database compatibility
  // Using the same logic as bulk-engine.ts to ensure consistency
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Old generateId method (for comparison)
 */
function oldGenerateId(): string {
  return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

async function testUUIDGeneration() {
  console.log('🔍 Testing UUID Generation Fix...');
  console.log('=' .repeat(60));

  // Test the new UUID generation
  console.log('1️⃣ Testing new UUID generation method...');
  
  const newIds = [];
  for (let i = 0; i < 10; i++) {
    const id = generateId();
    newIds.push(id);
    console.log(`   Generated ID ${i + 1}: ${id}`);
    console.log(`   Is Valid UUID: ${isValidUUID(id) ? '✅ YES' : '❌ NO'}`);
    
    if (!isValidUUID(id)) {
      console.log('❌ New UUID generation is not working correctly');
      return false;
    }
  }

  // Check for uniqueness
  const uniqueIds = new Set(newIds);
  if (uniqueIds.size !== newIds.length) {
    console.log('❌ Generated UUIDs are not unique');
    return false;
  }

  console.log('✅ All generated IDs are valid UUIDs and unique');

  // Test the old method for comparison
  console.log('\n2️⃣ Testing old string-based generation method (for comparison)...');
  
  const oldIds = [];
  for (let i = 0; i < 5; i++) {
    const id = oldGenerateId();
    oldIds.push(id);
    console.log(`   Old ID ${i + 1}: ${id}`);
    console.log(`   Is Valid UUID: ${isValidUUID(id) ? '✅ YES' : '❌ NO (expected)'}`);
  }

  // Verify old method produces non-UUIDs
  const oldUuids = oldIds.filter(id => isValidUUID(id));
  if (oldUuids.length > 0) {
    console.log('⚠️  Old method unexpectedly produced valid UUIDs');
  } else {
    console.log('✅ Old method correctly produces non-UUID strings (as expected)');
  }

  // Test UUID format compliance
  console.log('\n3️⃣ Testing UUID v4 format compliance...');
  
  const testId = generateId();
  console.log(`   Test UUID: ${testId}`);
  
  // Check specific UUID v4 characteristics
  const parts = testId.split('-');
  if (parts.length !== 5) {
    console.log('❌ UUID does not have 5 parts separated by hyphens');
    return false;
  }

  if (parts[0].length !== 8 || parts[1].length !== 4 || parts[2].length !== 4 || 
      parts[3].length !== 4 || parts[4].length !== 12) {
    console.log('❌ UUID parts do not have correct lengths');
    return false;
  }

  // Check version (should be 4)
  if (testId[14] !== '4') {
    console.log('❌ UUID is not version 4');
    return false;
  }

  // Check variant (should be 8, 9, a, or b)
  const variant = testId[19].toLowerCase();
  if (!['8', '9', 'a', 'b'].includes(variant)) {
    console.log('❌ UUID variant is not correct');
    return false;
  }

  console.log('✅ UUID v4 format compliance verified');

  // Performance test
  console.log('\n4️⃣ Testing generation performance...');
  
  const startTime = Date.now();
  const performanceIds = [];
  
  for (let i = 0; i < 1000; i++) {
    performanceIds.push(generateId());
  }
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  console.log(`   Generated 1000 UUIDs in ${duration}ms`);
  console.log(`   Average: ${(duration / 1000).toFixed(3)}ms per UUID`);
  
  // Check uniqueness in large batch
  const uniquePerformanceIds = new Set(performanceIds);
  if (uniquePerformanceIds.size !== performanceIds.length) {
    console.log('❌ Collision detected in performance test');
    return false;
  }
  
  console.log('✅ No collisions in 1000 generated UUIDs');

  return true;
}

async function main() {
  console.log('🚀 Testing UUID Generation Fix...');
  console.log('=' .repeat(60));

  const success = await testUUIDGeneration();

  console.log('\n📊 Test Results:');
  console.log('=' .repeat(60));
  
  if (success) {
    console.log('🎉 UUID Generation Fix: ✅ PASSED');
    console.log('');
    console.log('✅ What was verified:');
    console.log('   • New generateId() method produces valid UUID v4 format');
    console.log('   • Generated UUIDs are unique across multiple generations');
    console.log('   • UUID format compliance (version 4, correct variant)');
    console.log('   • Performance is acceptable (sub-millisecond generation)');
    console.log('   • Old method correctly identified as non-UUID (for comparison)');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('   • The enhanced job queue will now generate proper UUIDs');
    console.log('   • Database insertion should work without UUID syntax errors');
    console.log('   • Test the full bulk processing workflow');
  } else {
    console.log('❌ UUID Generation Fix: FAILED');
    console.log('');
    console.log('🔧 Issues found:');
    console.log('   • Check the generateId() method implementation');
    console.log('   • Verify UUID format compliance');
    console.log('   • Ensure uniqueness is maintained');
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

export { testUUIDGeneration, generateId, isValidUUID };
