-- Check Database State for Stuck Jobs
-- This script shows the current state of all jobs to diagnose display issues

-- 1. Check the specific bulk processing job
SELECT 
  'BULK JOB DETAILS' as section,
  id,
  status,
  total_items,
  processed_items,
  successful_items,
  failed_items,
  created_at,
  updated_at,
  started_at,
  completed_at,
  version
FROM bulk_processing_jobs 
WHERE id = 'dbc98f0d-9823-4bfe-90a2-97411ebb989f';

-- 2. Check all bulk processing jobs
SELECT 
  'ALL BULK JOBS' as section,
  id,
  status,
  total_items,
  processed_items,
  successful_items,
  failed_items,
  created_at,
  started_at,
  completed_at
FROM bulk_processing_jobs 
ORDER BY created_at DESC
LIMIT 5;

-- 3. Check AI generation jobs for the problematic tool
SELECT 
  'AI JOBS FOR TOOL' as section,
  id,
  tool_id,
  job_type,
  status,
  progress,
  created_at,
  started_at,
  completed_at
FROM ai_generation_jobs 
WHERE tool_id = '8e0116a9-0745-4f5e-90fc-bfb288b42dbb'
ORDER BY created_at DESC;

-- 4. Check for any stuck jobs
SELECT 
  'STUCK JOBS CHECK' as section,
  'bulk_processing_jobs' as table_name,
  COUNT(*) as count
FROM bulk_processing_jobs 
WHERE status IN ('processing', 'pending')
  AND created_at < NOW() - INTERVAL '1 hour'

UNION ALL

SELECT 
  'STUCK JOBS CHECK' as section,
  'ai_generation_jobs' as table_name,
  COUNT(*) as count
FROM ai_generation_jobs 
WHERE status IN ('processing', 'retrying', 'pending')
  AND created_at < NOW() - INTERVAL '1 hour';

-- 5. Show what the frontend should see
SELECT 
  'FRONTEND DATA' as section,
  id,
  status,
  total_items,
  processed_items,
  successful_items,
  failed_items,
  CASE 
    WHEN created_at IS NULL THEN 'NULL created_at'
    ELSE created_at::text
  END as created_at_check,
  CASE 
    WHEN started_at IS NULL THEN 'NULL started_at'
    ELSE started_at::text
  END as started_at_check,
  CASE 
    WHEN completed_at IS NULL THEN 'NULL completed_at'
    ELSE completed_at::text
  END as completed_at_check
FROM bulk_processing_jobs 
WHERE id = 'dbc98f0d-9823-4bfe-90a2-97411ebb989f';
