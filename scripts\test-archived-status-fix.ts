#!/usr/bin/env tsx

/**
 * Test Script: Verify 'archived' status fix for bulk_processing_jobs
 * 
 * This script tests that the constraint violation error is resolved
 * by attempting to create and archive a test job.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testArchivedStatusFix() {
  console.log('🧪 Testing archived status fix for bulk_processing_jobs');
  console.log('=' .repeat(60));
  
  let testJobId: string | null = null;
  
  try {
    // Step 1: Create a test bulk processing job
    console.log('1️⃣ Creating test bulk processing job...');
    
    const { data: createResult, error: createError } = await supabase
      .from('bulk_processing_jobs')
      .insert({
        job_type: 'manual_entry',
        status: 'completed',
        total_items: 1,
        processed_items: 1,
        successful_items: 1,
        failed_items: 0,
        source_data: { test: true },
        processing_options: {},
        created_by: 'test-system'
      })
      .select('id')
      .single();
    
    if (createError) {
      console.error('❌ Failed to create test job:', createError.message);
      return false;
    }
    
    testJobId = createResult.id;
    console.log(`✅ Test job created with ID: ${testJobId}`);
    
    // Step 2: Test updating status to 'archived' (this was failing before)
    console.log('2️⃣ Testing status update to "archived"...');
    
    const { error: updateError } = await supabase
      .from('bulk_processing_jobs')
      .update({ 
        status: 'archived',
        updated_at: new Date().toISOString()
      })
      .eq('id', testJobId);
    
    if (updateError) {
      console.error('❌ Failed to update status to archived:', updateError.message);
      return false;
    }
    
    console.log('✅ Successfully updated status to "archived"');
    
    // Step 3: Verify the status was updated
    console.log('3️⃣ Verifying status update...');
    
    const { data: verifyResult, error: verifyError } = await supabase
      .from('bulk_processing_jobs')
      .select('status')
      .eq('id', testJobId)
      .single();
    
    if (verifyError) {
      console.error('❌ Failed to verify status:', verifyError.message);
      return false;
    }
    
    if (verifyResult.status !== 'archived') {
      console.error(`❌ Status verification failed. Expected: archived, Got: ${verifyResult.status}`);
      return false;
    }
    
    console.log('✅ Status verification successful');
    
    // Step 4: Test constraint allows all expected statuses
    console.log('4️⃣ Testing all allowed status values...');
    
    const allowedStatuses = ['pending', 'processing', 'completed', 'failed', 'cancelled', 'paused', 'archived'];
    
    for (const status of allowedStatuses) {
      const { error: statusError } = await supabase
        .from('bulk_processing_jobs')
        .update({ status })
        .eq('id', testJobId);
      
      if (statusError) {
        console.error(`❌ Status "${status}" rejected:`, statusError.message);
        return false;
      }
    }
    
    console.log('✅ All status values accepted by constraint');
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
    
  } finally {
    // Cleanup: Delete test job
    if (testJobId) {
      console.log('🧹 Cleaning up test job...');
      const { error: deleteError } = await supabase
        .from('bulk_processing_jobs')
        .delete()
        .eq('id', testJobId);
      
      if (deleteError) {
        console.warn('⚠️ Failed to cleanup test job:', deleteError.message);
      } else {
        console.log('✅ Test job cleaned up');
      }
    }
  }
}

async function main() {
  const success = await testArchivedStatusFix();
  
  console.log('');
  console.log('=' .repeat(60));
  
  if (success) {
    console.log('🎉 All tests passed!');
    console.log('✅ Archived status constraint fix is working correctly');
    console.log('✅ Job cleanup service should no longer encounter constraint violations');
  } else {
    console.log('❌ Tests failed!');
    console.log('❌ There may still be issues with the constraint fix');
  }
  
  process.exit(success ? 0 : 1);
}

main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
