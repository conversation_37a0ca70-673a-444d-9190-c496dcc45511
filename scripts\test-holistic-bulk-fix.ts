#!/usr/bin/env tsx

/**
 * Test Holistic Bulk Processing Fix
 * 
 * This script tests the complete holistic fix for both:
 * 1. Foreign key constraint violation (tool creation before job creation)
 * 2. Version mismatch errors (proper version tracking)
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function testToolCreationManager() {
  console.log('🔧 Testing Tool Creation Manager...');
  console.log('-'.repeat(50));

  try {
    // Import the tool creation manager
    const { getToolCreationManager } = await import('../src/lib/bulk-processing/tool-creation-manager');
    const toolManager = getToolCreationManager();

    // Test data
    const testToolData = {
      url: 'https://example-test-tool.com/',
      providedData: {
        name: 'Test Tool',
        description: 'A test tool for validation',
        category: 'Testing'
      },
      needsGeneration: {
        name: false,
        description: false,
        features: true,
        pricing: true,
        prosAndCons: true,
        haiku: true,
        hashtags: true
      }
    };

    console.log('1️⃣ Testing tool creation...');
    const toolId = await toolManager.ensureToolExists(testToolData);
    console.log(`✅ Tool created/found: ${toolId}`);

    // Verify tool exists in database
    const { data: tool, error } = await supabase
      .from('tools')
      .select('id, name, website, content_status')
      .eq('id', toolId)
      .single();

    if (error) {
      console.log('❌ Failed to verify tool in database:', error.message);
      return false;
    }

    console.log('✅ Tool verified in database:');
    console.log(`   ID: ${tool.id}`);
    console.log(`   Name: ${tool.name}`);
    console.log(`   Website: ${tool.website}`);
    console.log(`   Status: ${tool.content_status}`);

    // Test job linking
    console.log('2️⃣ Testing job linking...');
    const testJobId = generateUUID();
    await toolManager.linkToolToJob(toolId, testJobId);
    console.log(`✅ Tool linked to job: ${testJobId}`);

    // Test status update
    console.log('3️⃣ Testing status updates...');
    await toolManager.updateToolStatus(toolId, 'completed');
    console.log('✅ Tool status updated to completed');

    // Cleanup
    await supabase.from('tools').delete().eq('id', toolId);
    console.log('✅ Test tool cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Tool Creation Manager test failed:', error);
    return false;
  }
}

async function testEnhancedJobQueue() {
  console.log('\n🎯 Testing Enhanced Job Queue with Tool ID...');
  console.log('-'.repeat(50));

  try {
    // Create a test tool first
    const testToolId = generateUUID();
    const { error: toolError } = await supabase
      .from('tools')
      .insert({
        id: testToolId,
        name: 'Test Job Tool',
        slug: 'test-job-tool',
        link: '/tools/test-job-tool',
        website: 'https://test-job-tool.com/',
        description: 'Test tool for job queue validation',
        content_status: 'draft',
        submission_type: 'admin',
        submission_source: 'test'
      });

    if (toolError) {
      console.log('❌ Failed to create test tool:', toolError.message);
      return false;
    }

    console.log(`✅ Created test tool: ${testToolId}`);

    // Test job creation with proper tool ID
    console.log('1️⃣ Testing job creation with tool ID...');
    const testJobId = generateUUID();
    const { error: jobError } = await supabase
      .from('ai_generation_jobs')
      .insert({
        id: testJobId,
        tool_id: testToolId, // Use actual tool ID
        job_type: 'tool_processing',
        status: 'pending',
        progress: 0,
        attempts: 0,
        max_attempts: 3,
        priority: 1,
        job_data: {
          url: 'https://test-job-tool.com/',
          bulkJobId: 'test-bulk-job'
        }
      });

    if (jobError) {
      console.log('❌ Job creation failed:', jobError.message);
      return false;
    }

    console.log(`✅ Job created successfully: ${testJobId}`);

    // Verify job in database
    const { data: job } = await supabase
      .from('ai_generation_jobs')
      .select('id, tool_id, job_type, status')
      .eq('id', testJobId)
      .single();

    if (job) {
      console.log('✅ Job verified in database:');
      console.log(`   ID: ${job.id}`);
      console.log(`   Tool ID: ${job.tool_id}`);
      console.log(`   Type: ${job.job_type}`);
      console.log(`   Status: ${job.status}`);
    }

    // Cleanup
    await supabase.from('ai_generation_jobs').delete().eq('id', testJobId);
    await supabase.from('tools').delete().eq('id', testToolId);
    console.log('✅ Test data cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Enhanced Job Queue test failed:', error);
    return false;
  }
}

async function testVersionTracking() {
  console.log('\n📊 Testing Version Tracking...');
  console.log('-'.repeat(50));

  try {
    // Create a test bulk job
    const testBulkJobId = generateUUID();
    const { error: bulkError } = await supabase
      .from('bulk_processing_jobs')
      .insert({
        id: testBulkJobId,
        job_type: 'manual_entry',
        status: 'pending',
        total_items: 1,
        processed_items: 0,
        successful_items: 0,
        failed_items: 0,
        version: 1,
        source_data: [{ url: 'https://version-test.com/' }],
        processing_options: { test: true },
        created_by: 'version-test'
      });

    if (bulkError) {
      console.log('❌ Failed to create test bulk job:', bulkError.message);
      return false;
    }

    console.log(`✅ Created test bulk job: ${testBulkJobId}`);

    // Test version retrieval
    console.log('1️⃣ Testing version retrieval...');
    const { data: versionData } = await supabase
      .from('bulk_processing_jobs')
      .select('version')
      .eq('id', testBulkJobId)
      .single();

    if (versionData) {
      console.log(`✅ Current version: ${versionData.version}`);
    }

    // Test atomic status update
    console.log('2️⃣ Testing atomic status update...');
    const { data: statusResult, error: statusError } = await supabase.rpc('update_bulk_job_status_atomic', {
      p_job_id: testBulkJobId,
      p_new_status: 'processing',
      p_expected_version: versionData?.version || 1
    });

    if (statusError) {
      console.log('❌ Status update failed:', statusError.message);
      return false;
    }

    if (statusResult && statusResult.success) {
      console.log('✅ Status update successful');
      console.log(`   New version: ${statusResult.new_version}`);
    }

    // Test atomic progress update
    console.log('3️⃣ Testing atomic progress update...');
    const { data: progressResult, error: progressError } = await supabase.rpc('update_bulk_job_progress_atomic', {
      p_job_id: testBulkJobId,
      p_processed_items: 1,
      p_successful_items: 1,
      p_failed_items: 0,
      p_expected_version: statusResult?.new_version || 2
    });

    if (progressError) {
      console.log('❌ Progress update failed:', progressError.message);
      return false;
    }

    if (progressResult && progressResult.success) {
      console.log('✅ Progress update successful');
      console.log(`   New version: ${progressResult.new_version}`);
    }

    // Cleanup
    await supabase.from('bulk_processing_jobs').delete().eq('id', testBulkJobId);
    console.log('✅ Test bulk job cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Version tracking test failed:', error);
    return false;
  }
}

async function testCompleteWorkflow() {
  console.log('\n🚀 Testing Complete Holistic Workflow...');
  console.log('-'.repeat(50));

  try {
    console.log('1️⃣ Testing tool creation → job creation workflow...');
    
    // Import tool creation manager
    const { getToolCreationManager } = await import('../src/lib/bulk-processing/tool-creation-manager');
    const toolManager = getToolCreationManager();

    // Create tool first
    const testToolData = {
      url: 'https://workflow-test.com/',
      providedData: {
        name: 'Workflow Test Tool'
      },
      needsGeneration: {
        name: false,
        description: true,
        features: true,
        pricing: true,
        prosAndCons: true,
        haiku: true,
        hashtags: true
      }
    };

    const toolId = await toolManager.ensureToolExists(testToolData);
    console.log(`✅ Tool created: ${toolId}`);

    // Create AI generation job with proper tool ID
    const jobId = generateUUID();
    const { error: jobError } = await supabase
      .from('ai_generation_jobs')
      .insert({
        id: jobId,
        tool_id: toolId, // Use actual tool ID
        job_type: 'tool_processing',
        status: 'pending',
        progress: 0,
        attempts: 0,
        max_attempts: 3,
        priority: 1,
        job_data: {
          url: testToolData.url,
          bulkJobId: 'workflow-test'
        }
      });

    if (jobError) {
      console.log('❌ Job creation failed:', jobError.message);
      return false;
    }

    console.log(`✅ Job created successfully: ${jobId}`);

    // Link tool to job
    await toolManager.linkToolToJob(toolId, jobId);
    console.log('✅ Tool linked to job');

    // Simulate job completion
    await supabase
      .from('ai_generation_jobs')
      .update({ status: 'completed', progress: 100 })
      .eq('id', jobId);

    // Update tool status
    await toolManager.updateToolStatus(toolId, 'completed');
    console.log('✅ Tool status updated to completed');

    // Cleanup
    await supabase.from('ai_generation_jobs').delete().eq('id', jobId);
    await supabase.from('tools').delete().eq('id', toolId);
    console.log('✅ Workflow test cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Complete workflow test failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Testing Holistic Bulk Processing Fix...');
  console.log('=' .repeat(70));

  const results = {
    toolCreationManager: false,
    enhancedJobQueue: false,
    versionTracking: false,
    completeWorkflow: false
  };

  // Run all tests
  results.toolCreationManager = await testToolCreationManager();
  results.enhancedJobQueue = await testEnhancedJobQueue();
  results.versionTracking = await testVersionTracking();
  results.completeWorkflow = await testCompleteWorkflow();

  // Summary
  console.log('\n📊 Holistic Fix Test Results:');
  console.log('=' .repeat(70));
  console.log(`Tool Creation Manager:         ${results.toolCreationManager ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Enhanced Job Queue:            ${results.enhancedJobQueue ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Version Tracking:              ${results.versionTracking ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Complete Workflow:             ${results.completeWorkflow ? '✅ PASSED' : '❌ FAILED'}`);

  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 All holistic fixes verified successfully!');
    console.log('   • Tool creation before job creation works');
    console.log('   • Foreign key constraints satisfied');
    console.log('   • Version tracking works correctly');
    console.log('   • Complete workflow functional end-to-end');
    console.log('');
    console.log('🚀 The bulk processing system is now fully operational!');
  } else {
    console.log('\n❌ Some issues remain:');
    if (!results.toolCreationManager) {
      console.log('   • Tool Creation Manager has issues');
    }
    if (!results.enhancedJobQueue) {
      console.log('   • Enhanced Job Queue still has problems');
    }
    if (!results.versionTracking) {
      console.log('   • Version tracking errors persist');
    }
    if (!results.completeWorkflow) {
      console.log('   • Complete workflow still broken');
    }
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

export { main as testHolisticBulkFix };
