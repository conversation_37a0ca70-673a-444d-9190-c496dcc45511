#!/usr/bin/env tsx

/**
 * Migration 011 Runner: Increase short_description field length
 * 
 * This script executes Migration 011 which increases the short_description
 * field from VARCHAR(150) to VARCHAR(300) to prevent constraint violations
 * during AI content generation.
 */

import { config } from 'dotenv';
import { readFileSync } from 'fs';
import { join } from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

// Also try .env as fallback
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration011() {
  console.log('🚀 Starting Migration 011: Increase short_description field length');
  console.log('📋 This migration will:');
  console.log('   • Increase short_description from VARCHAR(150) to VARCHAR(300)');
  console.log('   • Prevent constraint violations during AI content generation');
  console.log('   • Test the new field length with verification queries');
  console.log('');

  try {
    // Read the migration SQL file
    const migrationPath = join(process.cwd(), 'src', 'lib', 'database', 'migrations', '011_increase_short_description_length.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf-8');

    console.log('📄 Loaded migration file: 011_increase_short_description_length.sql');
    
    // Try automatic execution first
    console.log('⚡ Attempting automatic SQL execution...');
    
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
      
      if (error) {
        throw new Error(`RPC execution failed: ${error.message}`);
      }
      
      console.log('✅ Migration 011 executed automatically!');
      
    } catch (rpcError) {
      console.log('⚠️  Automatic execution failed, switching to manual mode...');
      console.log(`   Error: ${rpcError}`);
      console.log('');
      
      // Fallback to manual execution
      console.log('📋 MANUAL SQL EXECUTION REQUIRED');
      console.log('Please copy and paste the following SQL into your Supabase SQL Editor:');
      console.log('');
      console.log('🔗 Supabase SQL Editor: https://supabase.com/dashboard/project/gvcdqspryxrvxadfpwux/sql');
      console.log('');
      console.log('--- COPY AND PASTE THE FOLLOWING SQL ---');
      console.log(migrationSQL);
      console.log('--- END OF SQL ---');
      console.log('');
      
      // Wait for user confirmation
      console.log('After executing the SQL in Supabase, press Enter to continue...');
      await new Promise(resolve => {
        process.stdin.once('data', () => resolve(undefined));
      });
      
      console.log('✅ Manual execution completed!');
    }

    console.log('');

    // Verify the field length was updated
    console.log('🔍 Verifying field length update...');
    
    try {
      const { data: columnInfo, error: columnError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, character_maximum_length')
        .eq('table_name', 'tools')
        .eq('column_name', 'short_description');

      if (columnError) {
        console.warn('⚠️ Could not verify field length (migration may still be successful):', columnError.message);
      } else {
        console.log('✅ Field length verification completed');
        if (columnInfo && columnInfo.length > 0) {
          const field = columnInfo[0];
          console.log(`📋 Updated field: ${field.column_name} ${field.data_type}(${field.character_maximum_length})`);
          
          if (field.character_maximum_length >= 300) {
            console.log('✅ Field length successfully increased to support longer descriptions');
          } else {
            console.log('⚠️ Field length may not have been updated correctly');
          }
        } else {
          console.log('⚠️ Field information not found - please verify manually in Supabase');
        }
      }
    } catch (verifyError) {
      console.warn('⚠️ Field verification failed (migration may still be successful):', verifyError);
    }

    console.log('');
    console.log('🎉 Migration 011 completed successfully!');
    console.log('✅ AI content generation should no longer encounter short_description length violations');
    console.log('✅ Short descriptions up to 300 characters now supported');

  } catch (error) {
    console.error('❌ Migration 011 failed with error:', error);
    process.exit(1);
  }
}

// Execute the migration
runMigration011().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
