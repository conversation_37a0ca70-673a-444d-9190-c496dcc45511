#!/usr/bin/env python3
"""
Extract website_url values from JSON file and save to text file.
"""

import json
import sys
from pathlib import Path

def extract_website_urls(json_file_path, output_file_path):
    """
    Extract website_url values from JSON file and save to text file.
    
    Args:
        json_file_path (str): Path to the input JSON file
        output_file_path (str): Path to the output text file
    """
    try:
        # Read the JSON file
        print(f"Reading JSON file: {json_file_path}")
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract website URLs
        website_urls = []
        total_objects = len(data)
        skipped_count = 0
        
        print(f"Processing {total_objects} objects...")
        
        for i, obj in enumerate(data):
            if i % 1000 == 0:  # Progress indicator
                print(f"Processed {i}/{total_objects} objects...")
            
            # Check if website_url exists and is not empty/null
            if 'website_url' in obj and obj['website_url']:
                url = obj['website_url'].strip()
                if url:  # Additional check for non-empty after stripping
                    website_urls.append(url)
                else:
                    skipped_count += 1
            else:
                skipped_count += 1
        
        # Remove duplicates while preserving order
        unique_urls = []
        seen = set()
        for url in website_urls:
            if url not in seen:
                unique_urls.append(url)
                seen.add(url)
        
        # Write to output file
        print(f"Writing {len(unique_urls)} unique URLs to: {output_file_path}")
        with open(output_file_path, 'w', encoding='utf-8') as f:
            for url in unique_urls:
                f.write(url + '\n')
        
        print(f"Extraction complete!")
        print(f"Total objects processed: {total_objects}")
        print(f"Objects with valid website_url: {len(website_urls)}")
        print(f"Unique URLs extracted: {len(unique_urls)}")
        print(f"Objects skipped (missing/empty website_url): {skipped_count}")
        
        return len(unique_urls)
        
    except FileNotFoundError:
        print(f"Error: File '{json_file_path}' not found.")
        return 0
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format in '{json_file_path}': {e}")
        return 0
    except Exception as e:
        print(f"Error: {e}")
        return 0

def main():
    # File paths
    json_file = "crawl/data/aitoptools_data_1743074025.json"
    output_file = "extracted_website_urls.txt"
    
    # Check if input file exists
    if not Path(json_file).exists():
        print(f"Error: Input file '{json_file}' does not exist.")
        sys.exit(1)
    
    # Extract URLs
    url_count = extract_website_urls(json_file, output_file)
    
    if url_count > 0:
        print(f"\nSuccess! {url_count} unique website URLs have been extracted to '{output_file}'")
    else:
        print("\nNo URLs were extracted. Please check the input file and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
