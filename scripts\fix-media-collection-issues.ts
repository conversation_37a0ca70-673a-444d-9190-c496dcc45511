#!/usr/bin/env tsx

/**
 * Fix Media Collection Issues
 * 
 * This script addresses the media collection problems:
 * 1. Missing error_message column in tools table
 * 2. Orphaned jobs cleanup
 * 3. Pipeline dependency issues
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function fixMediaCollectionIssues() {
  console.log('🔧 Fixing Media Collection Issues');
  console.log('=' .repeat(60));
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  try {
    // Step 1: Run the database migration
    console.log('1️⃣ Running database migration to add error_message column...');
    
    const migrationPath = join(process.cwd(), 'supabase', 'migrations', '010_add_error_message_column.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf-8');
    
    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        if (error && !error.message.includes('already exists')) {
          console.error(`❌ Migration statement failed: ${error.message}`);
          console.error(`Statement: ${statement.substring(0, 100)}...`);
        }
      }
    }
    
    console.log('✅ Database migration completed');
    
    // Step 2: Check for orphaned jobs
    console.log('\n2️⃣ Checking for orphaned jobs...');
    
    const { data: orphanedJobs, error: orphanError } = await supabase
      .from('ai_generation_jobs')
      .select('id, tool_id, job_type, status, created_at, attempts')
      .in('status', ['processing', 'pending'])
      .lt('created_at', new Date(Date.now() - 30 * 60 * 1000).toISOString()); // Older than 30 minutes
    
    if (orphanError) {
      console.error('❌ Error checking orphaned jobs:', orphanError);
    } else {
      console.log(`📊 Found ${orphanedJobs?.length || 0} potentially orphaned jobs`);
      
      if (orphanedJobs && orphanedJobs.length > 0) {
        console.log('\n🧹 Cleaning up orphaned jobs...');
        
        // Reset orphaned jobs to failed status
        const { error: resetError } = await supabase
          .from('ai_generation_jobs')
          .update({
            status: 'failed',
            error_logs: { error: 'Job orphaned - reset by cleanup script', timestamp: new Date().toISOString() },
            updated_at: new Date().toISOString()
          })
          .in('id', orphanedJobs.map(job => job.id));
        
        if (resetError) {
          console.error('❌ Error resetting orphaned jobs:', resetError);
        } else {
          console.log(`✅ Reset ${orphanedJobs.length} orphaned jobs to failed status`);
        }
      }
    }
    
    // Step 3: Check media collection pipeline dependencies
    console.log('\n3️⃣ Checking media collection pipeline dependencies...');
    
    const { data: mediaJobs, error: mediaError } = await supabase
      .from('ai_generation_jobs')
      .select('id, tool_id, job_type, status, created_at')
      .eq('job_type', 'media_collection')
      .in('status', ['pending', 'processing']);
    
    if (mediaError) {
      console.error('❌ Error checking media collection jobs:', mediaError);
    } else {
      console.log(`📊 Found ${mediaJobs?.length || 0} active media collection jobs`);
      
      if (mediaJobs && mediaJobs.length > 0) {
        for (const job of mediaJobs) {
          // Check if web scraping is completed for this tool
          const { data: webScrapingJobs, error: wsError } = await supabase
            .from('ai_generation_jobs')
            .select('id, status')
            .eq('tool_id', job.tool_id)
            .eq('job_type', 'web_scraping');
          
          if (wsError) {
            console.warn(`⚠️ Could not check web scraping for tool ${job.tool_id}`);
            continue;
          }
          
          const hasCompletedScraping = webScrapingJobs?.some(wsJob => wsJob.status === 'completed');
          
          if (!hasCompletedScraping) {
            console.log(`🔄 Media job ${job.id} waiting for web scraping completion`);
          } else {
            console.log(`✅ Media job ${job.id} has completed web scraping prerequisite`);
          }
        }
      }
    }
    
    // Step 4: Check tools table schema
    console.log('\n4️⃣ Verifying tools table schema...');
    
    const { data: schemaCheck, error: schemaError } = await supabase
      .rpc('exec_sql', { 
        sql: `SELECT column_name, data_type, is_nullable 
              FROM information_schema.columns 
              WHERE table_name = 'tools' 
              AND column_name IN ('error_message', 'processing_notes', 'ai_generation_status')
              ORDER BY column_name` 
      });
    
    if (schemaError) {
      console.error('❌ Error checking schema:', schemaError);
    } else {
      console.log('✅ Tools table schema:');
      if (schemaCheck && Array.isArray(schemaCheck)) {
        schemaCheck.forEach((col: any) => {
          console.log(`   ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
        });
      }
    }
    
    // Step 5: Test error_message column update
    console.log('\n5️⃣ Testing error_message column update...');
    
    // Find a tool to test with
    const { data: testTool, error: testToolError } = await supabase
      .from('tools')
      .select('id, name')
      .limit(1)
      .single();
    
    if (testToolError || !testTool) {
      console.warn('⚠️ No tools found for testing');
    } else {
      // Test updating error_message
      const { error: updateError } = await supabase
        .from('tools')
        .update({ 
          error_message: 'Test error message - ' + new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', testTool.id);
      
      if (updateError) {
        console.error('❌ Error updating error_message:', updateError);
      } else {
        console.log(`✅ Successfully updated error_message for tool ${testTool.id}`);
        
        // Clear the test error message
        await supabase
          .from('tools')
          .update({ error_message: null })
          .eq('id', testTool.id);
      }
    }
    
    console.log('\n✅ Media collection issues fix completed!');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to fix media collection issues:', error);
    return false;
  }
}

async function main() {
  const success = await fixMediaCollectionIssues();
  
  console.log('\n' + '=' .repeat(60));
  
  if (success) {
    console.log('🎉 Media collection issues have been resolved!');
    console.log('');
    console.log('✅ Fixed issues:');
    console.log('   - Added missing error_message column to tools table');
    console.log('   - Cleaned up orphaned jobs');
    console.log('   - Verified pipeline dependencies');
    console.log('   - Tested schema updates');
    console.log('');
    console.log('💡 Media collection should now work correctly!');
  } else {
    console.log('❌ Failed to resolve media collection issues');
    console.log('');
    console.log('🔧 Manual steps required:');
    console.log('1. Check database connection and permissions');
    console.log('2. Run the migration manually in Supabase SQL editor');
    console.log('3. Restart the application');
  }
  
  process.exit(success ? 0 : 1);
}

main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
