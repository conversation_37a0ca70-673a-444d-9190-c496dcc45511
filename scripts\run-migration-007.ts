#!/usr/bin/env tsx

/**
 * Migration 007: Fix job_type constraint to include 'tool_processing'
 * 
 * This script executes migration 007 which updates the ai_generation_jobs
 * table constraint to include the 'tool_processing' job type used by
 * the bulk processing system.
 */

import { config } from 'dotenv';
import { readFileSync } from 'fs';
import { join } from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

// Also try .env as fallback
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration007() {
  console.log('🚀 Starting Migration 007: Fix job_type constraint...');
  console.log('=' .repeat(70));

  try {
    // Read the migration SQL file
    const migrationPath = join(process.cwd(), 'src/lib/database/migrations/007_fix_job_type_constraint.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');

    console.log('📄 Loaded migration file: 007_fix_job_type_constraint.sql');
    console.log('');

    // Show what this migration will do
    console.log('📋 This migration will:');
    console.log('   🔧 Drop existing job_type constraint on ai_generation_jobs table');
    console.log('   ✅ Add updated constraint including "tool_processing" job type');
    console.log('   🧪 Test the new constraint with a dry-run insert');
    console.log('   📝 Add documentation comment for the constraint');
    console.log('');
    console.log('🎯 Problem being solved:');
    console.log('   • Bulk processing creates individual jobs with type "tool_processing"');
    console.log('   • Current constraint only allows: scrape, generate, bulk, media_extraction');
    console.log('   • This causes constraint violation error 23514');
    console.log('');

    // Display the SQL for manual execution
    console.log('⚠️  MANUAL SQL EXECUTION REQUIRED');
    console.log('Please copy and paste the following SQL into your Supabase SQL Editor:');
    console.log('');
    console.log('--- COPY AND PASTE THE FOLLOWING SQL ---');
    console.log(migrationSQL);
    console.log('--- END OF SQL ---');
    console.log('');

    // Wait for user confirmation
    console.log('After executing the SQL in Supabase, press Enter to continue...');
    await new Promise(resolve => {
      process.stdin.once('data', () => resolve(undefined));
    });

    // Verify the migration was applied
    console.log('🔍 Verifying migration was applied...');
    
    // Check constraint definition using modern PostgreSQL approach
    const { data: constraints, error: constraintError } = await supabase.rpc('get_constraint_definition', {
      constraint_name: 'ai_generation_jobs_job_type_check'
    }).then(result => {
      // If the RPC doesn't exist, try a direct query
      if (result.error) {
        return supabase
          .from('pg_constraint')
          .select('conname, pg_get_constraintdef(oid) as constraint_definition')
          .eq('conname', 'ai_generation_jobs_job_type_check');
      }
      return result;
    }).catch(() => {
      // Fallback to basic constraint check
      return { data: null, error: { message: 'Could not check constraint definition' } };
    });

    if (constraintError) {
      console.log('⚠️  Could not verify constraint (this is normal):', constraintError.message);
    } else if (constraints && constraints.length > 0) {
      console.log('✅ Constraint verification:');
      constraints.forEach(constraint => {
        console.log(`   Name: ${constraint.conname}`);
        console.log(`   Definition: ${constraint.constraint_definition || constraint.consrc || 'N/A'}`);

        // Check if tool_processing is included
        const definition = constraint.constraint_definition || constraint.consrc || '';
        if (definition.includes('tool_processing')) {
          console.log('   ✅ "tool_processing" is included in constraint');
        } else {
          console.log('   ❌ "tool_processing" not found in constraint');
        }
      });
    }

    // Test job creation with tool_processing type
    console.log('\n🧪 Testing job creation with tool_processing type...');
    
    try {
      const testJobId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });

      const { error: insertError } = await supabase
        .from('ai_generation_jobs')
        .insert({
          id: testJobId,
          job_type: 'tool_processing',
          status: 'pending',
          progress: 0,
          attempts: 0,
          max_attempts: 3,
          priority: 1,
          tags: ['test', 'migration-007'],
          can_pause: true,
          can_resume: false,
          can_stop: true
        });

      if (insertError) {
        console.log('❌ Test job insertion failed:', insertError.message);
        if (insertError.message.includes('violates check constraint')) {
          console.log('   This indicates the migration was not applied correctly.');
        }
      } else {
        console.log('✅ Test job with "tool_processing" type created successfully');
        
        // Clean up test job
        await supabase
          .from('ai_generation_jobs')
          .delete()
          .eq('id', testJobId);
        
        console.log('✅ Test job cleaned up');
      }
    } catch (testError) {
      console.log('❌ Test job creation error:', testError);
    }

    console.log('');
    console.log('🎉 Migration 007 completed successfully!');
    console.log('');
    console.log('✅ What was accomplished:');
    console.log('   • Updated job_type constraint to include "tool_processing"');
    console.log('   • Bulk processing can now create individual tool jobs');
    console.log('   • Fixed constraint violation error 23514');
    console.log('   • Maintained backward compatibility with existing job types');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('   • Test bulk processing workflow end-to-end');
    console.log('   • Verify individual tool jobs are created successfully');
    console.log('   • Check for any remaining version mismatch errors');

  } catch (error) {
    console.error('❌ Migration 007 failed:', error);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('   1. Ensure you have executed the SQL in Supabase SQL Editor');
    console.log('   2. Check that the constraint was dropped and recreated successfully');
    console.log('   3. Verify "tool_processing" is included in the new constraint');
    console.log('   4. Check Supabase logs for detailed error information');
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  runMigration007().catch(console.error);
}

export { runMigration007 };
