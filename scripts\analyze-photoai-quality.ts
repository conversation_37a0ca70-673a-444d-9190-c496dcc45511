#!/usr/bin/env tsx

/**
 * Analyze PhotoAI.com Quality Score
 * 
 * This script analyzes the actual quality score calculation for PhotoAI.com
 * to understand why the cost optimizer is not using basic content.
 */

import { config } from 'dotenv';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
config({ path: '.env.local' });
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

function calculateBasicQualityScore(content: string): number {
  let score = 0;

  // Length score (0-30 points) - more lenient
  const lengthScore = Math.min(30, (content.length / 1000) * 30);
  score += lengthScore;

  // Structure score (0-40 points) - prioritize structure
  const headingCount = (content.match(/<h[1-6]|^#{1,6}\s/g) || []).length;
  let structureScore = Math.min(40, headingCount * 10);

  // Bonus for large content with alternative structure indicators
  if (content.length > 50000 && structureScore < 20) {
    const paragraphCount = content.split(/\n\s*\n/).length;
    const hasLists = /^[\*\-\+]\s|^\d+\.\s/m.test(content);
    const hasLinks = /<a\s+href|^\[.*\]\(/m.test(content);

    // Give structure credit for large content with paragraphs, lists, or links
    if (paragraphCount > 10) structureScore += 10;
    if (hasLists) structureScore += 5;
    if (hasLinks) structureScore += 5;

    structureScore = Math.min(40, structureScore);
  }
  score += structureScore;

  // Content diversity score (0-30 points)
  const uniqueWords = new Set(content.toLowerCase().split(/\s+/)).size;
  const diversityScore = Math.min(30, (uniqueWords / 50) * 30);
  score += diversityScore;

  // Large content bonus (0-10 points) - reward substantial content
  if (content.length > 100000) {
    score += 10; // Bonus for very large content
  } else if (content.length > 50000) {
    score += 5; // Bonus for large content
  }

  return Math.round(score);
}

function isGoodEnoughContent(content: string): boolean {
  // Handle null, undefined, or empty content
  if (!content || typeof content !== 'string') {
    return false;
  }

  // For very large content (>50k chars), volume indicates successful scraping
  if (content.length > 50000) {
    const wordCount = content.split(/\s+/).length;
    const hasBasicStructure = content.split(/\n\s*\n/).length >= 3; // Has paragraphs

    if (wordCount > 1000 && hasBasicStructure) {
      console.log(`   ✅ LARGE-CONTENT: ${content.length} chars with ${wordCount} words - sufficient for AI`);
      return true;
    }
  }

  // For medium content (10k-50k chars), use relaxed quality threshold
  if (content.length > 10000) {
    const qualityScore = calculateBasicQualityScore(content);
    const relaxedThreshold = 50; // Lower threshold for substantial content

    if (qualityScore >= relaxedThreshold) {
      console.log(`   ✅ MEDIUM-CONTENT: ${content.length} chars, quality ${qualityScore}/100 - sufficient for AI`);
      return true;
    }
  }

  // For smaller content, use strict criteria
  const lengthCheck = content.length > 300;
  const wordCountCheck = content.split(/\s+/).length > 50;
  const headingCheck = /<h[1-6]|^#{1,6}\s/m.test(content);
  const qualityScoreCheck = calculateBasicQualityScore(content) > 60;
  const structureCheck = hasSufficientStructure(content);

  console.log('   Content Quality Checks (Small Content):');
  console.log(`     Length > 300: ${lengthCheck} (${content.length} chars)`);
  console.log(`     Words > 50: ${wordCountCheck} (${content.split(/\s+/).length} words)`);
  console.log(`     Has headings: ${headingCheck}`);
  console.log(`     Quality score > 60: ${qualityScoreCheck} (${calculateBasicQualityScore(content)})`);
  console.log(`     Sufficient structure: ${structureCheck}`);

  return lengthCheck && wordCountCheck && headingCheck && qualityScoreCheck && structureCheck;
}

function hasSufficientStructure(content: string): boolean {
  // Ensure content has enough structure for quality AI generation
  const hasMultipleHeadings = (content.match(/<h[1-6]|^#{1,6}\s/g) || []).length >= 2;
  const hasParagraphs = content.split(/\n\s*\n/).length >= 3;
  const hasLists = /^[\*\-\+]\s|^\d+\.\s/m.test(content);
  const hasLinks = /<a\s+href|^\[.*\]\(/m.test(content);

  console.log('   Structure Analysis:');
  console.log(`     Multiple headings (>=2): ${hasMultipleHeadings} (${(content.match(/<h[1-6]|^#{1,6}\s/g) || []).length})`);
  console.log(`     Paragraphs (>=3): ${hasParagraphs} (${content.split(/\n\s*\n/).length})`);
  console.log(`     Has lists: ${hasLists}`);
  console.log(`     Has links: ${hasLinks}`);

  // Need at least 2 structural elements for quality AI processing
  const structuralElements = [hasMultipleHeadings, hasParagraphs, hasLists, hasLinks];
  const structureCount = structuralElements.filter(Boolean).length;
  
  console.log(`     Structural elements: ${structureCount}/4 (need >=2)`);
  
  return structureCount >= 2;
}

async function analyzePhotoAIQuality() {
  console.log('🔍 Analyzing PhotoAI.com Quality Score...');
  console.log('=' .repeat(70));

  // Try to load the scraped PhotoAI.com content
  const possiblePaths = [
    'data/scraped-content/photoai.com/photoai.com__2025-06-22T12-14-50-446Z.md',
    'data/scraped-content/photoai.com/photoai.com__2025-06-22T12-40-23-620Z.md'
  ];

  let content = '';
  let usedPath = '';

  for (const path of possiblePaths) {
    try {
      const fullPath = join(process.cwd(), path);
      content = readFileSync(fullPath, 'utf8');
      usedPath = path;
      break;
    } catch (error) {
      // File doesn't exist, try next
    }
  }

  if (!content) {
    console.log('❌ Could not find PhotoAI.com scraped content');
    console.log('   Tried paths:');
    possiblePaths.forEach(path => console.log(`     • ${path}`));
    return false;
  }

  console.log(`✅ Loaded PhotoAI.com content from: ${usedPath}`);
  console.log(`   Content length: ${content.length} characters`);

  // Analyze the content
  console.log('\n1️⃣ Basic Content Analysis...');
  
  const wordCount = content.split(/\s+/).length;
  const headingMatches = content.match(/<h[1-6]|^#{1,6}\s/g) || [];
  const headingCount = headingMatches.length;
  const uniqueWords = new Set(content.toLowerCase().split(/\s+/)).size;

  console.log(`   Total characters: ${content.length}`);
  console.log(`   Total words: ${wordCount}`);
  console.log(`   Unique words: ${uniqueWords}`);
  console.log(`   Headings found: ${headingCount}`);

  // Calculate quality score breakdown
  console.log('\n2️⃣ Quality Score Calculation...');
  
  const lengthScore = Math.min(30, (content.length / 1000) * 30);
  const structureScore = Math.min(40, headingCount * 10);
  const diversityScore = Math.min(30, (uniqueWords / 50) * 30);
  const totalScore = Math.round(lengthScore + structureScore + diversityScore);

  console.log(`   Length score: ${lengthScore.toFixed(1)}/30 (${content.length}/1000 * 30)`);
  console.log(`   Structure score: ${structureScore.toFixed(1)}/40 (${headingCount} headings * 10)`);
  console.log(`   Diversity score: ${diversityScore.toFixed(1)}/30 (${uniqueWords}/50 * 30)`);
  console.log(`   Total quality score: ${totalScore}/100`);

  // Check if it passes the 60% threshold
  console.log('\n3️⃣ Quality Threshold Analysis...');
  
  const passesThreshold = totalScore > 60;
  console.log(`   Quality threshold: 60`);
  console.log(`   PhotoAI.com score: ${totalScore}`);
  console.log(`   Passes threshold: ${passesThreshold ? '✅ YES' : '❌ NO'}`);

  if (!passesThreshold) {
    console.log(`   Gap: ${60 - totalScore} points needed`);
  }

  // Check overall "good enough" criteria
  console.log('\n4️⃣ Cost Optimizer "Good Enough" Check...');
  
  const isGoodEnough = isGoodEnoughContent(content);
  console.log(`   Overall result: ${isGoodEnough ? '✅ GOOD ENOUGH' : '❌ NOT GOOD ENOUGH'}`);

  // Analyze why it might be failing
  console.log('\n5️⃣ Root Cause Analysis...');
  
  if (!passesThreshold) {
    console.log('   🎯 PRIMARY ISSUE: Quality score below 60% threshold');
    
    if (structureScore < 20) {
      console.log(`   📊 Structure issue: Only ${headingCount} headings found`);
      console.log('      • Need more headings for higher structure score');
      console.log('      • Each heading adds 10 points (max 40)');
    }
    
    if (diversityScore < 20) {
      console.log(`   📊 Diversity issue: Only ${uniqueWords} unique words`);
      console.log('      • Need more diverse vocabulary');
      console.log('      • Target: 50+ unique words for full 30 points');
    }
  }

  // Recommendations
  console.log('\n6️⃣ Recommendations...');
  
  if (!passesThreshold) {
    console.log('   Option 1: Lower quality threshold from 60 to 50');
    console.log(`     • PhotoAI.com would score ${totalScore}/50 = ${((totalScore/50)*100).toFixed(1)}%`);
    console.log('     • Would pass threshold and use basic content');
    console.log('     • Save 4 credits per job (80% cost reduction)');
    console.log('');
    console.log('   Option 2: Adjust quality scoring for large content');
    console.log('     • Give bonus points for content > 100k characters');
    console.log('     • Recognize that large content is inherently valuable');
    console.log('     • PhotoAI.com has 125k chars - clearly sufficient for AI');
    console.log('');
    console.log('   Option 3: Override threshold for very large content');
    console.log('     • If content > 50k chars, consider it automatically sufficient');
    console.log('     • Large content volume indicates successful scraping');
    console.log('     • Quality issues less important with abundant content');
  } else {
    console.log('   ✅ PhotoAI.com should be using basic content');
    console.log('   🔍 Check for other issues in cost optimization logic');
  }

  return { totalScore, passesThreshold, isGoodEnough, content };
}

// Run the analysis
if (require.main === module) {
  analyzePhotoAIQuality()
    .then(result => {
      if (result) {
        console.log('\n📊 Analysis Summary:');
        console.log('=' .repeat(70));
        console.log(`Quality Score: ${result.totalScore}/100`);
        console.log(`Passes 60% Threshold: ${result.passesThreshold ? 'YES' : 'NO'}`);
        console.log(`Cost Optimizer "Good Enough": ${result.isGoodEnough ? 'YES' : 'NO'}`);
        console.log(`Content Length: ${result.content.length} characters`);
        
        if (!result.passesThreshold) {
          console.log('\n🎯 SOLUTION: Adjust quality threshold or scoring for large content');
        }
      }
    })
    .catch(error => {
      console.error('\n💥 Analysis failed:', error);
      process.exit(1);
    });
}

export { analyzePhotoAIQuality };
