-- =====================================================
-- CLEANUP DUPLICATE BULK PROCESSING FUNCTIONS
-- =====================================================
-- Execute this SQL in your Supabase SQL Editor to remove
-- the old TEXT parameter versions of bulk processing functions
-- =====================================================

-- Drop old TEXT parameter versions of functions
DROP FUNCTION IF EXISTS update_bulk_job_status_atomic(p_job_id TEXT, p_new_status TEXT, p_expected_version INTEGER);
DROP FUNCTION IF EXISTS update_bulk_job_progress_atomic(p_job_id TEXT, p_processed_items INTEGER, p_successful_items INTEGER, p_failed_items INTEGER, p_expected_version INTEGER);
DROP FUNCTION IF EXISTS append_bulk_job_log_atomic(p_job_id TEXT, p_log_entry JSONB, p_expected_version INTEGER);
DROP FUNCTION IF EXISTS complete_bulk_job_atomic(p_job_id TEXT, p_expected_version INTEGER);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Check remaining functions to ensure only UUID versions exist
SELECT
    specific_name,
    parameter_name,
    data_type,
    ordinal_position
FROM information_schema.parameters
WHERE specific_schema = 'public'
AND specific_name LIKE '%bulk%'
AND parameter_name = 'p_job_id'
ORDER BY specific_name, ordinal_position;

-- Test the status update function with a UUID (should return job_not_found)
SELECT update_bulk_job_status_atomic('00000000-0000-0000-0000-000000000000'::uuid, 'processing');

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
-- If all queries executed successfully, only the UUID versions
-- of the bulk processing functions remain!
-- =====================================================
