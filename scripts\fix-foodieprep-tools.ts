#!/usr/bin/env tsx

/**
 * Fix FoodiePrep Tools
 * 
 * This script finds all FoodiePrep tools and ensures they have the correct
 * submission_source for bulk processing bypass to work.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function fixFoodiePrepTools() {
  console.log('🔧 Fixing FoodiePrep Tools...\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. Find all FoodiePrep tools
    console.log('1. 🔍 Finding all FoodiePrep tools...');
    const { data: tools, error } = await supabase
      .from('tools')
      .select('*')
      .or('name.ilike.%foodieprep%,website.ilike.%foodieprep%')
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch tools: ${error.message}`);
    }

    if (!tools || tools.length === 0) {
      console.log('❌ No FoodiePrep tools found');
      return;
    }

    console.log(`✅ Found ${tools.length} FoodiePrep tool(s):\n`);

    // 2. Show current status
    tools.forEach((tool, index) => {
      console.log(`   Tool ${index + 1}: ${tool.name} (${tool.id})`);
      console.log(`      🌐 Website: ${tool.website}`);
      console.log(`      📊 Content Status: ${tool.content_status}`);
      console.log(`      🤖 AI Status: ${tool.ai_generation_status}`);
      console.log(`      📝 Submission Type: ${tool.submission_type}`);
      console.log(`      📋 Submission Source: ${tool.submission_source}`);
      console.log(`      📅 Created: ${tool.created_at}`);
      
      if (tool.submission_source !== 'bulk_processing') {
        console.log(`      ❌ NEEDS FIX: submission_source is "${tool.submission_source}"`);
      } else {
        console.log(`      ✅ Correct submission_source`);
      }
      console.log('');
    });

    // 3. Fix tools that need updating
    const toolsToFix = tools.filter(t => t.submission_source !== 'bulk_processing');
    
    if (toolsToFix.length > 0) {
      console.log(`2. 🔧 Fixing ${toolsToFix.length} tool(s)...\n`);
      
      for (const tool of toolsToFix) {
        console.log(`   Fixing: ${tool.name} (${tool.id})`);
        
        const { error: updateError } = await supabase
          .from('tools')
          .update({
            submission_source: 'bulk_processing',
            submission_type: 'admin',
            updated_at: new Date().toISOString()
          })
          .eq('id', tool.id);

        if (updateError) {
          console.log(`   ❌ Failed to update: ${updateError.message}`);
        } else {
          console.log(`   ✅ Updated successfully`);
        }
      }
    } else {
      console.log('2. ✅ All tools already have correct submission_source');
    }

    // 4. Check for duplicates and recommend cleanup
    if (tools.length > 1) {
      console.log(`\n3. ⚠️ Multiple FoodiePrep tools detected (${tools.length})`);
      console.log('   Consider keeping only the most recent one:');
      
      const sortedTools = [...tools].sort((a, b) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      
      console.log(`   📌 Keep: ${sortedTools[0].name} (${sortedTools[0].id}) - Created: ${sortedTools[0].created_at}`);
      
      if (sortedTools.length > 1) {
        console.log('   🗑️ Consider removing:');
        sortedTools.slice(1).forEach((tool, index) => {
          console.log(`      ${index + 1}. ${tool.name} (${tool.id}) - Created: ${tool.created_at}`);
        });
      }
    }

    // 5. Verify the fix worked
    console.log('\n4. ✅ Verification:');
    const { data: verifyTools, error: verifyError } = await supabase
      .from('tools')
      .select('id, name, submission_source')
      .or('name.ilike.%foodieprep%,website.ilike.%foodieprep%');

    if (verifyError) {
      console.log(`❌ Verification failed: ${verifyError.message}`);
    } else if (verifyTools) {
      const correctTools = verifyTools.filter(t => t.submission_source === 'bulk_processing');
      console.log(`   ✅ ${correctTools.length}/${verifyTools.length} tools have correct submission_source`);
      
      if (correctTools.length === verifyTools.length) {
        console.log('   🎯 All FoodiePrep tools are now ready for bulk processing bypass!');
      }
    }

    console.log('\n🚀 Next Steps:');
    console.log('   1. Process www.foodieprep.ai through bulk processing again');
    console.log('   2. Look for "🚀 Bulk processing detected" in the logs');
    console.log('   3. Verify the tool is published directly (content_status = "published")');
    console.log('   4. Check that content is parsed into individual database fields');

  } catch (error) {
    console.error('💥 Fix failed:', error);
  }
}

// Run the fix
fixFoodiePrepTools().catch(console.error);
