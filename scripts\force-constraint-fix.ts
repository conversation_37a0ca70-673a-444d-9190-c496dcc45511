#!/usr/bin/env tsx

/**
 * Force Constraint Fix
 * 
 * This script provides SQL to forcefully fix the constraint issue
 * by dropping ALL constraints and recreating them properly.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function forceConstraintFix() {
  console.log('🔧 Force Constraint Fix...');
  console.log('=' .repeat(60));

  console.log('🎯 Problem Analysis:');
  console.log('   • Constraint definition shows "tool_processing" is included');
  console.log('   • Other job types work perfectly');
  console.log('   • Only "tool_processing" fails with constraint violation');
  console.log('   • This suggests a caching issue or duplicate constraints');
  console.log('');

  console.log('💡 Solution:');
  console.log('   • Drop ALL job_type constraints (including any duplicates)');
  console.log('   • Recreate the constraint cleanly');
  console.log('   • Test immediately after recreation');
  console.log('');

  const forceFixSQL = `
-- =====================================================
-- FORCE CONSTRAINT FIX
-- =====================================================

-- Step 1: Drop ALL job_type related constraints (handles duplicates)
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    -- Find and drop all job_type constraints
    FOR constraint_record IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'ai_generation_jobs'::regclass 
        AND conname LIKE '%job_type%'
    LOOP
        EXECUTE 'ALTER TABLE ai_generation_jobs DROP CONSTRAINT IF EXISTS ' || constraint_record.conname;
        RAISE NOTICE 'Dropped constraint: %', constraint_record.conname;
    END LOOP;
END $$;

-- Step 2: Recreate the constraint with a new name to avoid any caching issues
ALTER TABLE ai_generation_jobs ADD CONSTRAINT ai_generation_jobs_job_type_check_v2 
CHECK (job_type IN ('scrape', 'generate', 'bulk', 'media_extraction', 'tool_processing'));

-- Step 3: Verify the constraint works
DO $$
BEGIN
    -- Test that the constraint allows tool_processing
    IF 'tool_processing'::text = ANY ((ARRAY['scrape'::character varying, 'generate'::character varying, 'bulk'::character varying, 'media_extraction'::character varying, 'tool_processing'::character varying])::text[]) THEN
        RAISE NOTICE 'Constraint test PASSED: tool_processing is allowed';
    ELSE
        RAISE NOTICE 'Constraint test FAILED: tool_processing is not allowed';
    END IF;
END $$;

-- Step 4: Test with actual insert (will rollback)
DO $$
DECLARE
    test_uuid UUID := gen_random_uuid();
BEGIN
    -- Try to insert a test record
    INSERT INTO ai_generation_jobs (id, job_type, status, progress, attempts, max_attempts, priority) 
    VALUES (test_uuid, 'tool_processing', 'pending', 0, 0, 3, 1);
    
    RAISE NOTICE 'SUCCESS: tool_processing job inserted successfully';
    
    -- Clean up the test record
    DELETE FROM ai_generation_jobs WHERE id = test_uuid;
    RAISE NOTICE 'Test record cleaned up';
    
EXCEPTION
    WHEN check_violation THEN
        RAISE NOTICE 'FAILED: Constraint still blocking tool_processing: %', SQLERRM;
    WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: Unexpected error: %', SQLERRM;
END $$;

-- =====================================================
-- VERIFICATION
-- =====================================================

-- Show the new constraint
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'ai_generation_jobs'::regclass 
AND conname LIKE '%job_type%';

-- =====================================================
-- COMPLETION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=== FORCE CONSTRAINT FIX COMPLETED ===';
    RAISE NOTICE 'If you see "SUCCESS: tool_processing job inserted successfully" above,';
    RAISE NOTICE 'then the constraint fix worked and bulk processing should now work.';
    RAISE NOTICE '';
    RAISE NOTICE 'If you see "FAILED: Constraint still blocking", there may be';
    RAISE NOTICE 'additional constraints or database-level issues to investigate.';
END $$;
`;

  console.log('⚠️  EXECUTE THE FOLLOWING SQL IN SUPABASE:');
  console.log('');
  console.log('--- COPY AND PASTE THE FOLLOWING SQL ---');
  console.log(forceFixSQL);
  console.log('--- END OF SQL ---');
  console.log('');

  // Wait for user confirmation
  console.log('After executing the SQL in Supabase, press Enter to test...');
  await new Promise(resolve => {
    process.stdin.once('data', () => resolve(undefined));
  });

  // Test the fix
  console.log('🧪 Testing the constraint fix...');
  
  const testJobId = generateUUID();
  
  const { error: testError } = await supabase
    .from('ai_generation_jobs')
    .insert({
      id: testJobId,
      job_type: 'tool_processing',
      status: 'pending',
      progress: 0,
      attempts: 0,
      max_attempts: 3,
      priority: 1
    });

  if (testError) {
    console.log('❌ Constraint fix failed:', testError.message);
    console.log('');
    console.log('🔧 Additional troubleshooting needed:');
    console.log('   1. Check if there are any other constraints on the table');
    console.log('   2. Verify the SQL executed without errors');
    console.log('   3. Check Supabase logs for any additional error details');
    console.log('   4. Consider restarting the Supabase connection pool');
  } else {
    console.log('✅ Constraint fix successful!');
    console.log(`   Test job created with ID: ${testJobId}`);
    
    // Verify the job
    const { data: retrievedJob } = await supabase
      .from('ai_generation_jobs')
      .select('id, job_type, status')
      .eq('id', testJobId)
      .single();

    if (retrievedJob) {
      console.log('✅ Job verified:');
      console.log(`   ID: ${retrievedJob.id}`);
      console.log(`   Type: ${retrievedJob.job_type}`);
      console.log(`   Status: ${retrievedJob.status}`);
    }

    // Clean up
    await supabase.from('ai_generation_jobs').delete().eq('id', testJobId);
    console.log('✅ Test job cleaned up');
    
    console.log('');
    console.log('🎉 CONSTRAINT FIX SUCCESSFUL!');
    console.log('   The bulk processing system should now work correctly.');
    console.log('   Run "npm run test:bulk-fixes" to verify all fixes.');
  }
}

// Run the force fix
if (require.main === module) {
  forceConstraintFix().catch(console.error);
}

export { forceConstraintFix };
