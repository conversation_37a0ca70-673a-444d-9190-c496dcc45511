import { Job, <PERSON>Handler, ContentGenerationJobData, JobType, JobPriority } from '../types';
import { ContentGenerationPipeline } from '../../content-generation/pipeline';
import { ContentGenerationRequest } from '../../types';
import { CommonErrorHandlers, ErrorHandlingUtils } from '../../error-handling';
import { getJobManager } from '../index';

export class ContentGenerationHandler implements JobHandler {
  private pipeline: ContentGenerationPipeline;

  constructor() {
    this.pipeline = new ContentGenerationPipeline();
  }

  async handle(job: Job): Promise<{
    success: boolean;
    toolId: string;
    status: string;
    workflowState: string;
    content?: unknown;
    validation?: unknown;
    qualityScore?: unknown;
    editorialReview?: unknown;
    metadata?: unknown;
  }> {
    const data = job.data as ContentGenerationJobData;

    if (!process.env.CONTENT_GENERATION_ENABLED || process.env.CONTENT_GENERATION_ENABLED !== 'true') {
      throw new Error('Content generation is disabled');
    }

    // Use enhanced error handling for the entire job processing
    return await CommonErrorHandlers.jobProcessing.handleWithRestart(
      async () => {
        return await this.executeContentGeneration(job, data);
      },
      job.id,
      ErrorHandlingUtils.createErrorContext({
        operation: 'content_generation_job',
        toolId: data.toolId,
        metadata: {
          url: data.url,
          jobId: job.id,
          priority: data.priority,
          complexity: data.complexity
        }
      })
    );
  }

  /**
   * Execute content generation with enhanced error handling
   */
  private async executeContentGeneration(job: Job, data: ContentGenerationJobData): Promise<{
    success: boolean;
    toolId: string;
    status: string;
    workflowState: string;
    content?: unknown;
    validation?: unknown;
    qualityScore?: unknown;
    editorialReview?: unknown;
    metadata?: unknown;
  }> {
    try {
      // Prepare content for AI generation
      const scrapedContent = this.prepareScrapedContent(data);

      // Build content generation request
      const request: ContentGenerationRequest = {
        toolId: data.toolId || job.id, // Use job ID if toolId not provided
        scrapedContent,
        toolUrl: data.url,
        complexity: data.complexity || 'medium',
        priority: data.priority || 'quality',
        contentQuality: data.contentQuality || 70,
        scrapingCost: data.scrapingCost || 0,
        aiProvider: data.aiProvider // Pass through user's provider choice
      };

      // Pipeline options
      const pipelinePriority: 'high' | 'normal' | 'low' =
        data.priority === 'quality' ? 'high' :
        data.priority === 'speed' ? 'low' : 'normal';

      const options = {
        skipValidation: false,
        requireEditorialReview: data.requireEditorialReview || false,
        autoApprove: data.autoApprove || false,
        qualityThreshold: data.qualityThreshold || 80,
        priority: pipelinePriority
      };

      console.log(`Starting content generation pipeline for: ${data.url}`);
      console.log(`Tool ID: ${request.toolId}`);
      console.log(`Content size: ${scrapedContent.length} characters`);
      console.log(`Options:`, options);

      // Execute pipeline with enhanced error handling
      const result = await CommonErrorHandlers.aiProvider.handleWithFallback(
        async () => {
          return await this.pipeline.execute(request, options);
        },
        async () => {
          // Fallback: try with simplified options
          const fallbackOptions = {
            ...options,
            qualityThreshold: Math.max(options.qualityThreshold - 20, 60),
            skipValidation: true
          };
          console.log('Attempting content generation with fallback options');
          return await this.pipeline.execute(request, fallbackOptions);
        },
        ErrorHandlingUtils.createErrorContext({
          operation: 'content_generation_pipeline',
          toolId: request.toolId,
          metadata: {
            url: data.url,
            contentSize: scrapedContent.length,
            options
          }
        })
      );

      if (!result.success) {
        throw new Error(`Content generation pipeline failed: ${result.error}`);
      }

      console.log(`Pipeline completed successfully for tool: ${request.toolId}`);
      console.log(`Status: ${result.status}, Workflow: ${result.workflowState}`);
      console.log(`Quality Score: ${result.qualityScore?.overall || 'N/A'}`);

      // Trigger media collection job after successful content generation
      await this.triggerMediaCollection(request.toolId, data.url);

      return {
        success: true,
        toolId: result.toolId,
        status: result.status,
        workflowState: result.workflowState,
        content: result.generatedContent,
        validation: result.validation,
        qualityScore: result.qualityScore,
        editorialReview: result.editorialReview,
        metadata: result.metadata
      };
    } catch (error: unknown) {
      console.error('Content generation pipeline failed:', error);

      // Log error with enhanced context for monitoring
      console.error('Enhanced error context:', ErrorHandlingUtils.formatErrorForLogging(error, {
        operation: 'content_generation_job',
        toolId: data.toolId,
        metadata: {
          url: data.url,
          jobId: job.id
        }
      }));

      throw error;
    }
  }

  /**
   * Prepare scraped content for AI processing
   */
  private prepareScrapedContent(data: ContentGenerationJobData): string {
    const { url, scrapedData, pricingData, faqData } = data;

    if (!scrapedData) {
      throw new Error('No scraped data available for content generation');
    }

    // Handle different scraped data formats
    let mainContent = '';

    // Check if scrapedData is a string (direct content)
    if (typeof scrapedData === 'string') {
      mainContent = scrapedData;
    }
    // Check if scrapedData has data property (new structure from error log)
    else if (scrapedData.data) {
      // Handle nested data structure
      if (typeof scrapedData.data === 'string') {
        mainContent = scrapedData.data;
      } else if (scrapedData.data.content) {
        mainContent = scrapedData.data.content;
      } else if (scrapedData.data.text) {
        mainContent = scrapedData.data.text;
      } else {
        mainContent = JSON.stringify(scrapedData.data, null, 2);
      }
    }
    // Check if scrapedData has text property (PhotoAI.com case!)
    else if (scrapedData.text) {
      mainContent = scrapedData.text;
    }
    // Check if scrapedData has content property
    else if (scrapedData.content) {
      mainContent = scrapedData.content;
    }
    // Check if scrapedData has textContent property
    else if (scrapedData.textContent) {
      mainContent = scrapedData.textContent;
    }
    // Check if scrapedData has markdown property
    else if (scrapedData.markdown) {
      mainContent = scrapedData.markdown;
    }
    // Fallback: stringify the entire object
    else {
      console.warn('Unknown scraped data format, using JSON representation');
      console.warn('Available properties:', Object.keys(scrapedData));
      mainContent = JSON.stringify(scrapedData, null, 2);
    }

    // Build comprehensive content string for AI processing
    let content = `# AI Tool Analysis\n\n`;
    content += `**URL:** ${url}\n\n`;

    if (scrapedData.title) {
      content += `**Title:** ${scrapedData.title}\n\n`;
    }

    if (scrapedData.description) {
      content += `**Description:** ${scrapedData.description}\n\n`;
    }

    // Add the main content
    content += `**Main Content:**\n${mainContent}\n\n`;

    if (scrapedData.pricingText) {
      content += `**Pricing Information:**\n${scrapedData.pricingText}\n\n`;
    }

    if (scrapedData.faqText) {
      content += `**FAQ Content:**\n${scrapedData.faqText}\n\n`;
    }

    // Add structured pricing data if available
    if (pricingData) {
      content += `**Structured Pricing Data:**\n${JSON.stringify(pricingData, null, 2)}\n\n`;
    }

    // Add structured FAQ data if available
    if (faqData) {
      content += `**Structured FAQ Data:**\n${JSON.stringify(faqData, null, 2)}\n\n`;
    }

    // Add any additional scraped data
    if (scrapedData.features && scrapedData.features.length > 0) {
      content += `**Extracted Features:**\n${scrapedData.features.join('\n')}\n\n`;
    }

    if (scrapedData.socialLinks) {
      content += `**Social Links:**\n`;
      Object.entries(scrapedData.socialLinks).forEach(([platform, url]) => {
        if (url) content += `- ${platform}: ${url}\n`;
      });
      content += '\n';
    }

    return content;
  }

  /**
   * Trigger media collection job after successful content generation
   */
  private async triggerMediaCollection(toolId: string, url: string): Promise<void> {
    try {
      const jobManager = getJobManager();

      const job = await jobManager.createJob(
        JobType.MEDIA_COLLECTION,
        {
          toolId,
          url,
          options: {
            collectFavicon: true,
            collectOgImages: true,
            forceCollection: false // Don't override existing media
          }
        },
        {
          priority: JobPriority.NORMAL
        }
      );

      console.log(`📸 Auto-triggered media collection job ${job.id} for tool ${toolId} after content generation`);
    } catch (error) {
      console.error(`Failed to trigger media collection for tool ${toolId}:`, error);
      // Don't throw error - media collection failure shouldn't fail content generation
    }
  }

}
