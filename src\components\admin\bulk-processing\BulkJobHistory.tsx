'use client';

import { useState } from 'react';
import { BulkProcessingJob } from '@/lib/types';

interface BulkJobHistoryProps {
  jobs: BulkProcessingJob[];
  loading: boolean;
  onJobSelect: (jobId: string) => void;
}

/**
 * Bulk Job History Component
 * 
 * Displays historical bulk processing jobs with filtering,
 * sorting, and quick actions.
 */
export function BulkJobHistory({
  jobs,
  loading,
  onJobSelect,
}: BulkJobHistoryProps) {
  const [filter, setFilter] = useState<'all' | 'completed' | 'failed' | 'running'>('all');
  const [sortBy, setSortBy] = useState<'createdAt' | 'status' | 'progress'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const filteredJobs = jobs.filter(job => {
    if (filter === 'all') return true;
    return job.status === filter;
  });

  const sortedJobs = [...filteredJobs].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (sortBy) {
      case 'createdAt':
        aValue = new Date(a.created_at).getTime();
        bValue = new Date(b.created_at).getTime();
        break;
      case 'status':
        aValue = a.status;
        bValue = b.status;
        break;
      case 'progress':
        // Calculate progress from processed/total items since progress field doesn't exist in DB schema
        aValue = a.total_items > 0 ? (a.processed_items / a.total_items) * 100 : 0;
        bValue = b.total_items > 0 ? (b.processed_items / b.total_items) * 100 : 0;
        break;
      default:
        return 0;
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });



  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return 'bg-green-500/20 text-green-400';
      case 'failed': return 'bg-red-500/20 text-red-400';
      case 'running': return 'bg-blue-500/20 text-blue-400';
      case 'paused': return 'bg-yellow-500/20 text-yellow-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getStatusIcon = (status: string): string => {
    switch (status) {
      case 'completed': return '✅';
      case 'failed': return '❌';
      case 'running': return '🔄';
      case 'paused': return '⏸️';
      default: return '⏳';
    }
  };

  const formatDate = (dateString: string): string => {
    if (!dateString) return 'No date';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid date';

    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (startTime: string, endTime?: string): string => {
    if (!startTime) return 'No duration';

    const start = new Date(startTime).getTime();
    if (isNaN(start)) return 'Invalid start time';

    const end = endTime ? new Date(endTime).getTime() : Date.now();
    if (endTime && isNaN(end)) return 'Invalid end time';

    const duration = Math.round((end - start) / 1000);

    if (duration < 0) return 'Invalid duration';
    if (duration < 60) return `${duration}s`;

    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}m ${seconds}s`;
  };

  if (loading) {
    return (
      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        <div className="flex items-center justify-center space-x-3">
          <div className="animate-spin w-6 h-6 border-2 border-orange-500 border-t-transparent rounded-full"></div>
          <span className="text-white">Loading job history...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-zinc-800 border border-black rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-white">Job History</h2>
        <div className="text-sm text-gray-400">
          {jobs.length} total jobs
        </div>
      </div>

      {/* Filters */}
      <div className="flex space-x-1 mb-6">
        {(['all', 'completed', 'failed', 'running'] as const).map((filterOption) => (
          <button
            key={filterOption}
            onClick={() => setFilter(filterOption)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === filterOption
                ? 'bg-orange-500 text-white'
                : 'bg-zinc-700 text-gray-400 hover:bg-zinc-600'
            }`}
          >
            {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
            <span className="ml-1 text-xs">
              ({filterOption === 'all' ? jobs.length : jobs.filter(job => job.status === filterOption).length})
            </span>
          </button>
        ))}
      </div>

      {/* Jobs Table */}
      {sortedJobs.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-zinc-700">
                <th className="text-left p-3 text-gray-400 font-medium">
                  <button
                    onClick={() => handleSort('createdAt')}
                    className="flex items-center space-x-1 hover:text-white transition-colors"
                  >
                    <span>Created</span>
                    {sortBy === 'createdAt' && (
                      <span>{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </button>
                </th>
                <th className="text-left p-3 text-gray-400 font-medium">
                  <button
                    onClick={() => handleSort('status')}
                    className="flex items-center space-x-1 hover:text-white transition-colors"
                  >
                    <span>Status</span>
                    {sortBy === 'status' && (
                      <span>{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </button>
                </th>
                <th className="text-left p-3 text-gray-400 font-medium">
                  <button
                    onClick={() => handleSort('progress')}
                    className="flex items-center space-x-1 hover:text-white transition-colors"
                  >
                    <span>Progress</span>
                    {sortBy === 'progress' && (
                      <span>{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </button>
                </th>
                <th className="text-left p-3 text-gray-400 font-medium">Items</th>
                <th className="text-left p-3 text-gray-400 font-medium">Duration</th>
                <th className="text-left p-3 text-gray-400 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedJobs.map((job) => (
                <tr
                  key={job.id}
                  className="border-b border-zinc-700/50 hover:bg-zinc-700/30 transition-colors"
                >
                  <td className="p-3">
                    <div className="text-white text-sm">
                      {formatDate(job.created_at)}
                    </div>
                    <div className="text-gray-400 text-xs font-mono">
                      {job.id.slice(0, 8)}...
                    </div>
                  </td>
                  <td className="p-3">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(job.status)}`}>
                      <span className="mr-1">{getStatusIcon(job.status)}</span>
                      {job.status}
                    </span>
                  </td>
                  <td className="p-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-zinc-700 rounded-full h-2">
                        <div
                          className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${job.total_items > 0 ? Math.min(100, (job.processed_items / job.total_items) * 100) : 0}%` }}
                        />
                      </div>
                      <span className="text-white text-sm">
                        {Math.round(job.total_items > 0 ? Math.min(100, (job.processed_items / job.total_items) * 100) : 0)}%
                      </span>
                    </div>
                  </td>
                  <td className="p-3">
                    <div className="text-white text-sm">
                      {job.total_items || 0} total
                    </div>
                    <div className="text-gray-400 text-xs">
                      {job.successful_items || 0} success, {job.failed_items || 0} failed
                    </div>
                  </td>
                  <td className="p-3">
                    <div className="text-white text-sm">
                      {formatDuration(job.started_at || job.created_at, job.completed_at)}
                    </div>
                  </td>
                  <td className="p-3">
                    <button
                      onClick={() => onJobSelect(job.id)}
                      className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm font-medium transition-colors"
                    >
                      View Results
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center text-gray-400 py-8">
          {filter === 'all' ? 'No bulk processing jobs found' : `No ${filter} jobs found`}
        </div>
      )}
    </div>
  );
}
