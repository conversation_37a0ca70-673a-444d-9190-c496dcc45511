#!/usr/bin/env tsx

/**
 * Test Retry Functionality
 * 
 * This script tests the enhanced retry functionality for failed and stuck jobs.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function testRetryFunctionality() {
  console.log('🧪 Testing Enhanced Retry Functionality...\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. Find jobs that can be retried
    console.log('1. 🔍 Finding jobs that can be retried...');
    const { data: jobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .in('status', ['failed', 'retrying'])
      .order('updated_at', { ascending: false })
      .limit(10);

    if (jobsError) {
      throw new Error(`Failed to fetch jobs: ${jobsError.message}`);
    }

    if (!jobs || jobs.length === 0) {
      console.log('❌ No failed or retrying jobs found to test with');
      return;
    }

    console.log(`✅ Found ${jobs.length} job(s) that can be retried:\n`);

    // 2. Analyze each job
    const now = new Date();
    const retryableJobs = [];
    const stuckJobs = [];

    jobs.forEach((job, index) => {
      console.log(`   Job ${index + 1}: ${job.id}`);
      console.log(`      📋 Type: ${job.job_type}`);
      console.log(`      📊 Status: ${job.status}`);
      console.log(`      🔄 Attempts: ${job.attempts}/${job.max_attempts}`);
      console.log(`      📅 Updated: ${job.updated_at}`);
      
      if (job.status === 'failed') {
        console.log(`      ✅ Can retry: Failed job`);
        retryableJobs.push(job);
      } else if (job.status === 'retrying') {
        const updatedAt = new Date(job.updated_at);
        const minutesSinceUpdate = (now.getTime() - updatedAt.getTime()) / (1000 * 60);
        
        if (minutesSinceUpdate > 15) {
          console.log(`      ⚠️ STUCK: Retrying for ${Math.round(minutesSinceUpdate)} minutes`);
          stuckJobs.push(job);
        } else {
          console.log(`      🔄 Active retry: ${Math.round(minutesSinceUpdate)} minutes ago`);
        }
      }
      
      if (job.error_logs) {
        console.log(`      ❌ Last Error: ${JSON.stringify(job.error_logs).substring(0, 100)}...`);
      }
      
      console.log('');
    });

    // 3. Test retry API endpoint
    if (retryableJobs.length > 0 || stuckJobs.length > 0) {
      const testJob = retryableJobs[0] || stuckJobs[0];
      console.log(`2. 🧪 Testing retry API with job: ${testJob.id}`);
      
      try {
        const response = await fetch(`http://localhost:3000/api/automation/jobs/${testJob.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789',
          },
          body: JSON.stringify({ action: 'retry' }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ Retry API call successful:');
        console.log(`   📊 New Status: ${result.data.status}`);
        console.log(`   🔄 Attempts: ${result.data.attempts}`);
        console.log(`   📅 Updated: ${result.data.updatedAt}`);

        // 4. Verify the job was updated in database
        console.log('\n3. ✅ Verifying database update...');
        const { data: updatedJob, error: verifyError } = await supabase
          .from('ai_generation_jobs')
          .select('*')
          .eq('id', testJob.id)
          .single();

        if (verifyError) {
          throw new Error(`Failed to verify update: ${verifyError.message}`);
        }

        console.log(`   📊 Database Status: ${updatedJob.status}`);
        console.log(`   🔄 Database Attempts: ${updatedJob.attempts}`);
        console.log(`   📅 Database Updated: ${updatedJob.updated_at}`);

        if (updatedJob.status === 'pending') {
          console.log('✅ Job successfully reset to pending status');
        } else {
          console.log(`⚠️ Unexpected status: ${updatedJob.status}`);
        }

      } catch (apiError) {
        console.log(`❌ Retry API test failed: ${apiError}`);
      }
    }

    // 5. Summary
    console.log('\n🎯 Summary:');
    console.log(`   📊 Total jobs checked: ${jobs.length}`);
    console.log(`   ❌ Failed jobs (can retry): ${retryableJobs.length}`);
    console.log(`   ⚠️ Stuck retrying jobs: ${stuckJobs.length}`);
    console.log(`   ✅ Total retryable jobs: ${retryableJobs.length + stuckJobs.length}`);
    console.log('');
    console.log('🚀 Enhanced Retry Features:');
    console.log('   ✅ Retry button for failed jobs');
    console.log('   ✅ Force retry for stuck retrying jobs (>15 min)');
    console.log('   ✅ Visual indicators for stuck jobs');
    console.log('   ✅ Prominent retry buttons in job list');
    console.log('   ✅ Enhanced error handling and feedback');

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
testRetryFunctionality().catch(console.error);
