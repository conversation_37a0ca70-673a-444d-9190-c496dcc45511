#!/usr/bin/env tsx

/**
 * Test Bulk Processing Bypass
 * 
 * This script tests that bulk processing tools bypass manual review
 * and are published directly after AI content generation.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function testBulkProcessingBypass() {
  console.log('🧪 Testing Bulk Processing Bypass...\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. Find bulk processing tools
    console.log('1. 🔍 Finding bulk processing tools...');
    const { data: bulkTools, error: bulkError } = await supabase
      .from('tools')
      .select('*')
      .eq('submission_source', 'bulk_processing')
      .order('created_at', { ascending: false })
      .limit(10);

    if (bulkError) {
      throw new Error(`Failed to fetch bulk tools: ${bulkError.message}`);
    }

    if (!bulkTools || bulkTools.length === 0) {
      console.log('❌ No bulk processing tools found');
      return;
    }

    console.log(`✅ Found ${bulkTools.length} bulk processing tools\n`);

    // 2. Check their status
    console.log('2. 📊 Checking tool statuses...');
    
    let publishedCount = 0;
    let draftCount = 0;
    let withEditorialReview = 0;
    let withGeneratedContent = 0;

    for (const tool of bulkTools) {
      console.log(`📋 Tool: ${tool.name} (${tool.id})`);
      console.log(`   🌐 Website: ${tool.website}`);
      console.log(`   📊 Content Status: ${tool.content_status}`);
      console.log(`   🤖 AI Status: ${tool.ai_generation_status}`);
      console.log(`   📈 Quality Score: ${tool.content_quality_score || 'N/A'}`);
      console.log(`   📅 Published At: ${tool.published_at || 'N/A'}`);
      console.log(`   📋 Editorial Review ID: ${tool.editorial_review_id || 'None'}`);
      console.log(`   📝 Generated Content: ${tool.generated_content ? 'Yes' : 'No'}`);

      // Count statuses
      if (tool.content_status === 'published') publishedCount++;
      if (tool.content_status === 'draft') draftCount++;
      if (tool.editorial_review_id) withEditorialReview++;
      if (tool.generated_content) withGeneratedContent++;

      console.log('');
    }

    // 3. Find user submission tools for comparison
    console.log('3. 🔍 Finding user submission tools for comparison...');
    const { data: userTools, error: userError } = await supabase
      .from('tools')
      .select('*')
      .neq('submission_source', 'bulk_processing')
      .not('submission_source', 'is', null)
      .order('created_at', { ascending: false })
      .limit(5);

    if (userError) {
      console.warn(`⚠️ Could not fetch user tools: ${userError.message}`);
    } else if (userTools && userTools.length > 0) {
      console.log(`✅ Found ${userTools.length} user submission tools for comparison\n`);
      
      let userPublishedCount = 0;
      let userDraftCount = 0;
      let userWithEditorialReview = 0;

      for (const tool of userTools) {
        console.log(`📋 User Tool: ${tool.name} (${tool.submission_source})`);
        console.log(`   📊 Content Status: ${tool.content_status}`);
        console.log(`   📋 Editorial Review ID: ${tool.editorial_review_id || 'None'}`);

        if (tool.content_status === 'published') userPublishedCount++;
        if (tool.content_status === 'draft') userDraftCount++;
        if (tool.editorial_review_id) userWithEditorialReview++;

        console.log('');
      }

      console.log('📊 User Tools Summary:');
      console.log(`   📈 Published: ${userPublishedCount}/${userTools.length}`);
      console.log(`   📝 Draft: ${userDraftCount}/${userTools.length}`);
      console.log(`   📋 With Editorial Review: ${userWithEditorialReview}/${userTools.length}`);
      console.log('');
    }

    // 4. Summary and analysis
    console.log('🎯 Bulk Processing Analysis:');
    console.log(`   📈 Published: ${publishedCount}/${bulkTools.length} (${Math.round(publishedCount/bulkTools.length*100)}%)`);
    console.log(`   📝 Draft: ${draftCount}/${bulkTools.length} (${Math.round(draftCount/bulkTools.length*100)}%)`);
    console.log(`   📋 With Editorial Review: ${withEditorialReview}/${bulkTools.length} (${Math.round(withEditorialReview/bulkTools.length*100)}%)`);
    console.log(`   🤖 With Generated Content: ${withGeneratedContent}/${bulkTools.length} (${Math.round(withGeneratedContent/bulkTools.length*100)}%)`);
    console.log('');

    // 5. Validation
    console.log('✅ Validation Results:');
    
    if (withEditorialReview === 0) {
      console.log('✅ PASS: No bulk processing tools have editorial reviews (as expected)');
    } else {
      console.log(`❌ FAIL: ${withEditorialReview} bulk processing tools have editorial reviews (should be 0)`);
    }

    if (publishedCount > draftCount) {
      console.log('✅ PASS: More bulk tools are published than in draft (bypass working)');
    } else {
      console.log(`⚠️ WARNING: More bulk tools are in draft (${draftCount}) than published (${publishedCount})`);
      console.log('   This might indicate the bypass is not working or tools are still processing');
    }

    const completedTools = bulkTools.filter(t => t.ai_generation_status === 'completed');
    const publishedCompletedTools = completedTools.filter(t => t.content_status === 'published');
    
    if (completedTools.length > 0) {
      const publishedPercentage = Math.round(publishedCompletedTools.length / completedTools.length * 100);
      console.log(`📊 Of ${completedTools.length} completed tools, ${publishedCompletedTools.length} are published (${publishedPercentage}%)`);
      
      if (publishedPercentage >= 90) {
        console.log('✅ PASS: Most completed bulk tools are published (bypass working well)');
      } else if (publishedPercentage >= 50) {
        console.log('⚠️ WARNING: Some completed bulk tools are not published');
      } else {
        console.log('❌ FAIL: Most completed bulk tools are not published (bypass may not be working)');
      }
    }

    console.log('');
    console.log('🎯 Expected Behavior for Bulk Processing:');
    console.log('   ✅ submission_source = "bulk_processing"');
    console.log('   ✅ content_status = "published" (after AI generation)');
    console.log('   ✅ ai_generation_status = "completed"');
    console.log('   ✅ editorial_review_id = null (no editorial review created)');
    console.log('   ✅ published_at = timestamp (when published)');
    console.log('   ✅ generated_content = AI Dude output');

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
testBulkProcessingBypass().catch(console.error);
