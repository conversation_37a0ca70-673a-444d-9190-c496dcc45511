/**
 * Media Extractor for Scrape.do Integration
 * Handles OG image extraction, favicon collection, and screenshot capture
 * Priority: Favicon > OG Images > Screenshot fallback
 */

import {
  MediaAsset,
  ImageCollection,
  FaviconResult,
  ScreenshotResult,
  ScrapeOptions
} from './types';
import { scrapeDoClient } from './scrape-do-client';
import { ogImageHandler } from './og-image-handler';
import { faviconCollector } from './favicon-collector';
import { SCRAPING_CONFIG } from '../config/scraping-config';

export class MediaExtractor {

  /**
   * Main method to collect all media assets with priority system
   * Priority Logic:
   * 1. Always extract favicon (use placeholder if not available)
   * 2. Always extract OG images (og:image, twitter:image, facebook:image)
   * 3. Only capture screenshot if NO OG images are available (regardless of favicon)
   */
  async collectImagesWithPriority(url: string, content: string): Promise<ImageCollection> {
    const images: ImageCollection = {
      favicon: null,
      ogImages: [],
      screenshot: null
    };

    try {
      // Step 1: Always extract favicon (no additional cost)
      const faviconResult = await faviconCollector.collectFavicon(url, content);

      // Always provide favicon - use extracted URLs or placeholder
      if (faviconResult.faviconUrls.length > 0) {
        images.favicon = faviconResult.faviconUrls;
      } else {
        // Use placeholder favicon when none found
        const baseUrl = new URL(url).origin;
        images.favicon = [`${baseUrl}/favicon.ico`]; // Default placeholder
      }

      // Step 2: Always extract OG images from meta tags (no additional cost)
      images.ogImages = ogImageHandler.extractOGImages(content, url);

      // Step 3: Only capture screenshot if NO OG images are available
      // Screenshot is independent of favicon availability
      if (images.ogImages.length === 0) {
        images.screenshot = await this.captureViewportScreenshot(url);
      }

      return images;
    } catch (error) {
      console.error('Media collection failed:', error);

      // Even on error, ensure favicon placeholder is provided
      if (!images.favicon) {
        try {
          const baseUrl = new URL(url).origin;
          images.favicon = [`${baseUrl}/favicon.ico`];
        } catch {
          // If URL parsing fails, use a generic placeholder
          images.favicon = ['/favicon.ico'];
        }
      }

      return images;
    }
  }

  /**
   * Extract favicon URLs with priority order
   */
  async extractFaviconPriority(url: string, content: string): Promise<string[]> {
    const faviconSelectors = [
      'link[rel="icon"]',                    // Standard favicon
      'link[rel="shortcut icon"]',           // Legacy favicon
      'link[rel="apple-touch-icon"]',        // Apple touch icon
      'link[rel="apple-touch-icon-precomposed"]' // Apple precomposed
    ];

    const faviconUrls: string[] = [];

    // Extract from HTML content (no additional API cost)
    faviconSelectors.forEach(selector => {
      const regex = new RegExp(`<${selector}[^>]*href=["']([^"']+)["']`, 'gi');
      let match;
      while ((match = regex.exec(content)) !== null) {
        faviconUrls.push(this.resolveUrl(match[1], url));
      }
    });

    // Fallback to default favicon.ico
    if (faviconUrls.length === 0) {
      const baseUrl = new URL(url).origin;
      faviconUrls.push(`${baseUrl}/favicon.ico`);
    }

    return [...new Set(faviconUrls)]; // Remove duplicates
  }

  /**
   * Extract Open Graph images from scraped content
   */
  extractOGImages(content: string, baseUrl: string): MediaAsset[] {
    const ogImages: MediaAsset[] = [];

    // Extract OG images from scraped content
    const ogImageRegex = /<meta\s+property=["']og:image["']\s+content=["']([^"']+)["']/gi;
    const twitterImageRegex = /<meta\s+name=["']twitter:image["']\s+content=["']([^"']+)["']/gi;
    const facebookImageRegex = /<meta\s+property=["']fb:image["']\s+content=["']([^"']+)["']/gi;

    let match;
    
    // Extract og:image
    while ((match = ogImageRegex.exec(content)) !== null) {
      ogImages.push({
        type: 'og:image',
        url: this.resolveUrl(match[1], baseUrl),
        priority: 1
      });
    }

    // Extract twitter:image
    while ((match = twitterImageRegex.exec(content)) !== null) {
      ogImages.push({
        type: 'twitter:image',
        url: this.resolveUrl(match[1], baseUrl),
        priority: 2
      });
    }

    // Extract facebook image
    while ((match = facebookImageRegex.exec(content)) !== null) {
      ogImages.push({
        type: 'facebook:image',
        url: this.resolveUrl(match[1], baseUrl),
        priority: 3
      });
    }

    return ogImages.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Capture standard viewport screenshot (NOT fullpage)
   */
  async captureViewportScreenshot(url: string): Promise<ScreenshotResult> {
    try {
      const result = await scrapeDoClient.scrapePage(url, {
        enableJSRendering: true,     // Required for screenshots
        captureScreenshot: true,     // Standard viewport only (NOT fullScreenShot)
        fullPageScreenshot: false,   // Explicitly disable fullpage
        blockResources: true,        // Optimize cost and performance
        timeout: SCRAPING_CONFIG.defaultOptions.enhanced.timeout || 50000, // Use enhanced timeout for screenshots
        deviceType: 'desktop',       // Standard desktop viewport
        customWaitTime: 2000,        // Wait for page to stabilize
        returnJSON: true             // Required for screenshot capture
      });

      if (result.success) {
        // Extract screenshot from JSON response (new method)
        let screenshotData: string | undefined;

        // Try different screenshot fields in order of preference
        if (result.screenShots && result.screenShots.length > 0) {
          const screenshotObj = result.screenShots[0];
          if (screenshotObj && screenshotObj.image) {
            screenshotData = screenshotObj.image; // Extract image data from screenshot object
          }
        } else if (result.screenshot) {
          screenshotData = result.screenshot;
        }

        if (screenshotData) {
          return {
            success: true,
            screenshot: screenshotData.startsWith('data:') ? screenshotData : `data:image/png;base64,${screenshotData}`,
            metadata: {
              width: 1200,
              height: 800,
              fullPage: false,
              capturedAt: new Date().toISOString()
            },
            timestamp: new Date().toISOString()
          };
        }

        // Fallback: try to extract from content (legacy method)
        if (result.content) {
          const screenshotMatch = result.content.match(/data:image\/[^;]+;base64,([^"]+)/);
          if (screenshotMatch) {
            return {
              success: true,
              screenshot: screenshotMatch[0], // Full data URL
              metadata: {
                width: 1200,
                height: 800,
                fullPage: false,
                capturedAt: new Date().toISOString()
              },
              timestamp: new Date().toISOString()
            };
          }
        }
      }

      return {
        success: false,
        error: 'Screenshot not found in response',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Enhanced screenshot capture with specific options
   */
  async captureScreenshot(url: string, options: {
    fullPage?: boolean;
    elementSelector?: string;
    width?: number;
    height?: number;
    deviceType?: 'desktop' | 'mobile' | 'tablet';
    waitForSelector?: string;
    customWait?: number;
  } = {}): Promise<ScreenshotResult> {
    try {
      const scrapeOptions: ScrapeOptions = {
        enableJSRendering: true,     // Required for screenshots
        blockResources: true,        // Optimize performance
        timeout: SCRAPING_CONFIG.defaultOptions.enhanced.timeout || 50000, // Use enhanced timeout for screenshots
        deviceType: options.deviceType || 'desktop',
        customWaitTime: options.customWait || 2000,
        returnJSON: true             // Required for screenshot capture
      };

      // Configure screenshot type
      if (options.fullPage) {
        scrapeOptions.fullPageScreenshot = true;
      } else {
        scrapeOptions.captureScreenshot = true;
      }

      // Configure wait conditions
      if (options.waitForSelector) {
        scrapeOptions.waitForSelector = options.waitForSelector;
      }

      const result = await scrapeDoClient.scrapePage(url, scrapeOptions);

      if (result.success) {
        // Extract screenshot from JSON response (new method)
        let screenshotData: string | undefined;

        if (options.fullPage) {
          // For full page screenshots, try fullScreenshot field first, then screenShots array
          if (result.fullScreenshot) {
            screenshotData = result.fullScreenshot;
          } else if (result.screenShots && result.screenShots.length > 0) {
            const screenshotObj = result.screenShots.find(s => s.type === 'FullScreenShot') || result.screenShots[0];
            screenshotData = screenshotObj?.image;
          }
        } else {
          // For viewport screenshots, try screenShots array first, then screenshot field
          if (result.screenShots && result.screenShots.length > 0) {
            const screenshotObj = result.screenShots.find(s => s.type === 'ScreenShot') || result.screenShots[0];
            screenshotData = screenshotObj?.image;
          } else if (result.screenshot) {
            screenshotData = result.screenshot;
          }
        }

        if (screenshotData) {
          return {
            success: true,
            screenshot: screenshotData.startsWith('data:') ? screenshotData : `data:image/png;base64,${screenshotData}`,
            metadata: {
              width: options.width || 1200,
              height: options.height || 800,
              fullPage: options.fullPage || false,
              capturedAt: new Date().toISOString()
            },
            timestamp: new Date().toISOString()
          };
        }

        // Fallback: try to extract from content (legacy method)
        if (result.content) {
          const screenshotMatch = result.content.match(/data:image\/[^;]+;base64,([^"]+)/);
          if (screenshotMatch) {
            return {
              success: true,
              screenshot: screenshotMatch[0],
              metadata: {
                width: options.width || 1200,
                height: options.height || 800,
                fullPage: options.fullPage || false,
                capturedAt: new Date().toISOString()
              },
              timestamp: new Date().toISOString()
            };
          }
        }
      }

      return {
        success: false,
        error: 'Screenshot capture failed',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Extract all available images from content
   */
  extractAllImages(content: string, baseUrl: string): MediaAsset[] {
    const images: MediaAsset[] = [];
    
    // Extract regular img tags
    const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*(?:alt=["']([^"']*)["'])?[^>]*>/gi;
    let match;
    
    while ((match = imgRegex.exec(content)) !== null) {
      images.push({
        type: 'og:image', // Generic type for regular images
        url: this.resolveUrl(match[1], baseUrl),
        priority: 10, // Lower priority than OG images
        metadata: {
          // alt: match[2] || undefined
        }
      });
    }

    return images;
  }

  /**
   * Validate image URL accessibility
   */
  async validateImageUrl(imageUrl: string): Promise<boolean> {
    try {
      const response = await fetch(imageUrl, {
        method: 'HEAD'
      });

      const contentType = response.headers.get('content-type');
      const isImageType = contentType?.startsWith('image/') ?? false;

      return response.ok && isImageType;
    } catch (error) {
      // Log error for debugging in tests
      if (process.env.NODE_ENV === 'test') {
        console.error('validateImageUrl error:', error);
      }
      return false;
    }
  }

  /**
   * Get image metadata without downloading
   */
  async getImageMetadata(imageUrl: string): Promise<{ width?: number; height?: number; format?: string; size?: number } | null> {
    try {
      // Create AbortController for timeout compatibility
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(imageUrl, {
        method: 'HEAD',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) return null;

      const contentType = response.headers.get('content-type');
      const contentLength = response.headers.get('content-length');

      return {
        format: contentType?.split('/')[1],
        size: contentLength ? parseInt(contentLength) : undefined
      };
    } catch {
      return null;
    }
  }

  /**
   * Filter and prioritize images based on quality criteria
   */
  filterQualityImages(images: MediaAsset[]): MediaAsset[] {
    return images
      .filter(image => {
        // Filter out common low-quality image patterns
        const url = image.url.toLowerCase();
        return !url.includes('logo') && 
               !url.includes('icon') && 
               !url.includes('avatar') &&
               !url.includes('thumbnail') &&
               !url.endsWith('.svg'); // SVGs often don't work well as previews
      })
      .sort((a, b) => {
        // Sort by priority, then by estimated quality
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        
        // Prefer images with metadata indicating larger size
        const aSize = a.metadata?.size || 0;
        const bSize = b.metadata?.size || 0;
        return bSize - aSize;
      });
  }

  /**
   * Resolve relative URLs to absolute URLs
   */
  private resolveUrl(relativeUrl: string, baseUrl: string): string {
    try {
      return new URL(relativeUrl, baseUrl).href;
    } catch {
      return relativeUrl;
    }
  }

  /**
   * Extract favicon specifically with fallback strategies
   */
  async extractFavicon(url: string): Promise<FaviconResult> {
    try {
      // First try to scrape the page for favicon links
      const result = await scrapeDoClient.scrapePage(url, {
        enableJSRendering: false, // Basic scraping for favicon
        outputFormat: 'raw',      // Need raw HTML for parsing
        blockResources: true,     // Optimize performance
        timeout: SCRAPING_CONFIG.defaultOptions.basic.timeout || 15000
      });

      if (result.success) {
        const faviconUrls = await this.extractFaviconPriority(url, result.content);
        return {
          faviconUrls,
          primaryFavicon: faviconUrls[0] || null,
          extractedAt: new Date().toISOString()
        };
      }

      // Fallback to default favicon.ico
      const baseUrl = new URL(url).origin;
      const defaultFavicon = `${baseUrl}/favicon.ico`;

      return {
        faviconUrls: [defaultFavicon],
        primaryFavicon: defaultFavicon,
        extractedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Favicon extraction failed:', error);
      return {
        faviconUrls: [],
        primaryFavicon: null,
        extractedAt: new Date().toISOString(),
        error: (error as Error).message
      };
    }
  }
}

// Export singleton instance
export const mediaExtractor = new MediaExtractor();
