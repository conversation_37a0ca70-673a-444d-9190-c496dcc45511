import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTool() {
  const toolId = '128c4bc9-605b-4a32-a340-97a842003f7c';
  
  console.log('🔍 Checking tool existence and submission source...');
  console.log(`Tool ID: ${toolId}`);
  
  // Test the exact query from the pipeline
  const { data: tools, error } = await supabase
    .from('tools')
    .select('submission_source, submission_type, name, website')
    .eq('id', toolId);
    
  console.log('Query result:', { 
    tools: tools?.length || 0, 
    error: error?.message || 'none' 
  });
  
  if (error) {
    console.error('❌ Database error:', error);
    return;
  }
  
  if (!tools || tools.length === 0) {
    console.log('❌ Tool not found');
    return;
  }
  
  console.log('✅ Tool found:', tools[0]);
  console.log('Submission source:', tools[0].submission_source);
  console.log('Is bulk processing:', tools[0].submission_source === 'bulk_processing');
  
  // Check for any AI generation jobs for this tool
  const { data: jobs, error: jobError } = await supabase
    .from('ai_generation_jobs')
    .select('*')
    .eq('tool_id', toolId)
    .order('created_at', { ascending: false })
    .limit(5);
    
  if (jobError) {
    console.error('❌ Job query error:', jobError);
  } else {
    console.log(`📋 Found ${jobs?.length || 0} AI generation jobs for this tool`);
    if (jobs && jobs.length > 0) {
      jobs.forEach((job, i) => {
        console.log(`  Job ${i + 1}: ${job.job_type} - ${job.status} (${job.created_at})`);
      });
    }
  }
}

checkTool().catch(console.error);
