#!/usr/bin/env tsx

/**
 * Test Script: Verify admin authentication is working
 * 
 * This script tests the admin API authentication both locally and in production
 */

import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

async function testAdminAuthentication() {
  console.log('🔐 Testing Admin API Authentication');
  console.log('=' .repeat(60));
  
  const baseUrl = process.env.SITE_URL || 'https://dudeai.vercel.app';
  const adminApiKey = process.env.ADMIN_API_KEY;
  const publicAdminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY;
  
  console.log('Environment Configuration:');
  console.log(`   Base URL: ${baseUrl}`);
  console.log(`   ADMIN_API_KEY: ${adminApiKey ? `${adminApiKey.substring(0, 10)}...` : 'NOT_SET'}`);
  console.log(`   NEXT_PUBLIC_ADMIN_API_KEY: ${publicAdminApiKey ? `${publicAdminApiKey.substring(0, 10)}...` : 'NOT_SET'}`);
  console.log('');
  
  if (!adminApiKey) {
    console.error('❌ ADMIN_API_KEY is not set in environment variables');
    return false;
  }
  
  if (!publicAdminApiKey) {
    console.error('❌ NEXT_PUBLIC_ADMIN_API_KEY is not set in environment variables');
    return false;
  }
  
  if (adminApiKey !== publicAdminApiKey) {
    console.error('❌ ADMIN_API_KEY and NEXT_PUBLIC_ADMIN_API_KEY do not match');
    console.error(`   ADMIN_API_KEY: ${adminApiKey}`);
    console.error(`   NEXT_PUBLIC_ADMIN_API_KEY: ${publicAdminApiKey}`);
    return false;
  }
  
  console.log('✅ Environment variables are properly configured');
  console.log('');
  
  // Test 1: Debug endpoint
  console.log('1️⃣ Testing debug endpoint...');
  try {
    const debugResponse = await fetch(`${baseUrl}/api/admin/debug/env`, {
      headers: {
        'x-admin-api-key': adminApiKey
      }
    });
    
    if (debugResponse.ok) {
      const debugData = await debugResponse.json();
      console.log('✅ Debug endpoint accessible');
      console.log('   Environment data:', JSON.stringify(debugData.environment, null, 2));
    } else {
      console.error(`❌ Debug endpoint failed: ${debugResponse.status} ${debugResponse.statusText}`);
      const errorText = await debugResponse.text();
      console.error(`   Response: ${errorText}`);
    }
  } catch (error) {
    console.error('❌ Debug endpoint request failed:', error);
  }
  
  console.log('');
  
  // Test 2: Bulk processing GET endpoint
  console.log('2️⃣ Testing bulk processing GET endpoint...');
  try {
    const getResponse = await fetch(`${baseUrl}/api/admin/bulk-processing`, {
      method: 'GET',
      headers: {
        'x-admin-api-key': adminApiKey
      }
    });
    
    if (getResponse.ok) {
      const getData = await getResponse.json();
      console.log('✅ Bulk processing GET endpoint accessible');
      console.log(`   Found ${getData.data?.jobs?.length || 0} jobs`);
    } else {
      console.error(`❌ Bulk processing GET failed: ${getResponse.status} ${getResponse.statusText}`);
      const errorText = await getResponse.text();
      console.error(`   Response: ${errorText}`);
      return false;
    }
  } catch (error) {
    console.error('❌ Bulk processing GET request failed:', error);
    return false;
  }
  
  console.log('');
  
  // Test 3: Test without API key (should fail)
  console.log('3️⃣ Testing without API key (should fail)...');
  try {
    const noKeyResponse = await fetch(`${baseUrl}/api/admin/bulk-processing`, {
      method: 'GET'
      // No x-admin-api-key header
    });
    
    if (noKeyResponse.status === 401) {
      console.log('✅ Correctly rejected request without API key');
    } else {
      console.error(`❌ Should have returned 401, got: ${noKeyResponse.status}`);
      return false;
    }
  } catch (error) {
    console.error('❌ No-key test request failed:', error);
    return false;
  }
  
  console.log('');
  
  // Test 4: Test with wrong API key (should fail)
  console.log('4️⃣ Testing with wrong API key (should fail)...');
  try {
    const wrongKeyResponse = await fetch(`${baseUrl}/api/admin/bulk-processing`, {
      method: 'GET',
      headers: {
        'x-admin-api-key': 'wrong-key-123'
      }
    });
    
    if (wrongKeyResponse.status === 401) {
      console.log('✅ Correctly rejected request with wrong API key');
    } else {
      console.error(`❌ Should have returned 401, got: ${wrongKeyResponse.status}`);
      return false;
    }
  } catch (error) {
    console.error('❌ Wrong-key test request failed:', error);
    return false;
  }
  
  return true;
}

async function main() {
  const success = await testAdminAuthentication();
  
  console.log('');
  console.log('=' .repeat(60));
  
  if (success) {
    console.log('🎉 All authentication tests passed!');
    console.log('✅ Admin API authentication is working correctly');
    console.log('✅ Environment variables are properly configured');
    console.log('✅ API endpoints are accessible with correct credentials');
    console.log('✅ Security is working (unauthorized requests rejected)');
    console.log('');
    console.log('💡 The admin authentication system is functioning properly!');
  } else {
    console.log('❌ Authentication tests failed!');
    console.log('❌ Admin API authentication is not working correctly');
    console.log('');
    console.log('🔧 Troubleshooting steps:');
    console.log('1. Check that ADMIN_API_KEY and NEXT_PUBLIC_ADMIN_API_KEY are set');
    console.log('2. Ensure both keys have the same value');
    console.log('3. Verify the keys are set in Vercel environment variables');
    console.log('4. Restart your development server after making changes');
    console.log('5. Check the browser network tab for the actual request headers');
  }
  
  process.exit(success ? 0 : 1);
}

main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
