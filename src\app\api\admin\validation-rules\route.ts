import { NextRequest, NextResponse } from 'next/server';
import { validateApi<PERSON>ey } from '@/lib/auth';
import { supabase } from '@/lib/supabase';

interface ValidationRulesConfig {
  contentStandards: {
    minDescriptionLength: number;
    maxDescriptionLength: number;
    minDetailedDescriptionWords: number;
    maxDetailedDescriptionWords: number;
    minFeaturesCount: number;
    maxFeaturesCount: number;
    minProsCount: number;
    maxProsCount: number;
    minConsCount: number;
    maxConsCount: number;
    minFaqsCount: number;
    maxFaqsCount: number;
    minHashtagsCount: number;
    maxHashtagsCount: number;
    maxMetaTitleLength: number;
    minMetaDescriptionLength: number;
    maxMetaDescriptionLength: number;
    maxTooltipLength: number;
  };
  validationRules: Array<{
    id: string;
    field: string;
    type: 'required' | 'length' | 'array_length' | 'word_count' | 'enum' | 'format';
    params: {
      min?: number;
      max?: number;
      values?: string[];
      pattern?: string;
    };
    message: string;
    severity: 'error' | 'warning';
    enabled: boolean;
  }>;
  bannedWords: string[];
  requiredFields: string[];
}

const DEFAULT_VALIDATION_CONFIG: ValidationRulesConfig = {
  contentStandards: {
    minDescriptionLength: 50,
    maxDescriptionLength: 500,
    minDetailedDescriptionWords: 50,
    maxDetailedDescriptionWords: 300,
    minFeaturesCount: 3,
    maxFeaturesCount: 8,
    minProsCount: 3,
    maxProsCount: 10,
    minConsCount: 3,
    maxConsCount: 10,
    minFaqsCount: 3,
    maxFaqsCount: 8,
    minHashtagsCount: 5,
    maxHashtagsCount: 10,
    maxMetaTitleLength: 60,
    minMetaDescriptionLength: 150,
    maxMetaDescriptionLength: 160,
    maxTooltipLength: 100
  },
  validationRules: [
    {
      id: 'detailed_description_required',
      field: 'detailed_description',
      type: 'required',
      params: {},
      message: 'Detailed description is required',
      severity: 'error',
      enabled: true
    },
    {
      id: 'detailed_description_word_count',
      field: 'detailed_description',
      type: 'word_count',
      params: { min: 50, max: 300 },
      message: 'Detailed description must be between 50-300 words',
      severity: 'error',
      enabled: true
    },
    {
      id: 'features_array_length',
      field: 'features',
      type: 'array_length',
      params: { min: 3, max: 8 },
      message: 'Features must be an array with 3-8 items',
      severity: 'error',
      enabled: true
    },
    {
      id: 'pros_array_length',
      field: 'pros_and_cons.pros',
      type: 'array_length',
      params: { min: 3, max: 10 },
      message: 'Pros must be an array with 3-10 items',
      severity: 'error',
      enabled: true
    },
    {
      id: 'cons_array_length',
      field: 'pros_and_cons.cons',
      type: 'array_length',
      params: { min: 3, max: 10 },
      message: 'Cons must be an array with 3-10 items',
      severity: 'error',
      enabled: true
    },
    {
      id: 'meta_title_length',
      field: 'meta_title',
      type: 'length',
      params: { max: 60 },
      message: 'Meta title must not exceed 60 characters',
      severity: 'error',
      enabled: true
    },
    {
      id: 'meta_description_length',
      field: 'meta_description',
      type: 'length',
      params: { min: 150, max: 160 },
      message: 'Meta description must be between 150-160 characters',
      severity: 'error',
      enabled: true
    },
    {
      id: 'hashtags_array_length',
      field: 'hashtags',
      type: 'array_length',
      params: { min: 5, max: 10 },
      message: 'Hashtags count should be between 5-10',
      severity: 'warning',
      enabled: true
    }
  ],
  bannedWords: [
    'placeholder',
    'lorem ipsum',
    'test content',
    'coming soon',
    'under construction',
    'todo',
    'fixme'
  ],
  requiredFields: [
    'name',
    'description',
    'short_description',
    'detailed_description',
    'company',
    'category_id',
    'features',
    'pricing',
    'pros_and_cons',
    'hashtags',
    'meta_title',
    'meta_description'
  ]
};

/**
 * GET /api/admin/validation-rules
 * Get current validation rules configuration
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get validation configuration from database
    const { data: configData, error } = await supabase
      .from('system_configuration')
      .select('config_value')
      .eq('config_key', 'validation_rules_config')
      .single();

    let validationConfig: ValidationRulesConfig;

    if (error || !configData) {
      // If no configuration exists, create default configuration
      validationConfig = DEFAULT_VALIDATION_CONFIG;
      
      // Save default configuration to database
      const { error: createError } = await supabase
        .from('system_configuration')
        .upsert({
          config_key: 'validation_rules_config',
          config_value: validationConfig,
          config_type: 'ai_provider',
          is_active: true,
          is_sensitive: false,
          description: 'Content validation rules and standards configuration',
          updated_by: 'admin',
          version: 1,
          environment: 'development',
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'config_key'
        });

      if (createError) {
        console.error('Database error creating default validation configuration:', createError);
        // Don't throw here, just log the error and continue with defaults
      }
    } else {
      validationConfig = configData.config_value as ValidationRulesConfig;
    }

    return NextResponse.json({
      success: true,
      data: validationConfig,
      message: 'Validation configuration retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching validation configuration:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch validation configuration' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/validation-rules
 * Update validation rules configuration
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, config } = body;

    if (action === 'reset') {
      // Reset to default configuration
      const { error } = await supabase
        .from('system_configuration')
        .upsert({
          config_key: 'validation_rules_config',
          config_value: DEFAULT_VALIDATION_CONFIG,
          config_type: 'ai_provider',
          is_active: true,
          is_sensitive: false,
          description: 'Content validation rules and standards configuration',
          updated_by: 'admin',
          version: 1,
          environment: 'development',
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'config_key'
        });

      if (error) {
        console.error('Database error resetting validation configuration:', error);
        throw new Error(`Failed to reset validation configuration: ${error.message}`);
      }

      return NextResponse.json({
        success: true,
        data: DEFAULT_VALIDATION_CONFIG,
        message: 'Validation configuration reset to defaults'
      });
    }

    if (action === 'update') {
      if (!config) {
        return NextResponse.json(
          { success: false, error: 'Configuration data is required' },
          { status: 400 }
        );
      }

      // Validate configuration structure
      const validationErrors = validateConfigurationStructure(config);
      if (validationErrors.length > 0) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Invalid configuration structure',
            details: validationErrors
          },
          { status: 400 }
        );
      }

      // Update configuration in database
      const { error } = await supabase
        .from('system_configuration')
        .upsert({
          config_key: 'validation_rules_config',
          config_value: config,
          config_type: 'ai_provider',
          is_active: true,
          is_sensitive: false,
          description: 'Content validation rules and standards configuration',
          updated_by: 'admin',
          version: 1,
          environment: 'development',
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'config_key'
        });

      if (error) {
        console.error('Database error updating validation configuration:', error);
        throw new Error(`Failed to update validation configuration: ${error.message}`);
      }

      return NextResponse.json({
        success: true,
        data: config,
        message: 'Validation configuration updated successfully'
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error updating validation configuration:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update validation configuration' },
      { status: 500 }
    );
  }
}

/**
 * Validate configuration structure
 */
function validateConfigurationStructure(config: any): string[] {
  const errors: string[] = [];

  if (!config.contentStandards) {
    errors.push('contentStandards is required');
  } else {
    const standards = config.contentStandards;
    
    // Check required numeric fields
    const requiredNumericFields = [
      'minDescriptionLength', 'maxDescriptionLength',
      'minDetailedDescriptionWords', 'maxDetailedDescriptionWords',
      'minFeaturesCount', 'maxFeaturesCount',
      'minProsCount', 'maxProsCount',
      'minConsCount', 'maxConsCount'
    ];

    for (const field of requiredNumericFields) {
      if (typeof standards[field] !== 'number' || standards[field] < 0) {
        errors.push(`${field} must be a non-negative number`);
      }
    }

    // Check logical constraints
    if (standards.minDescriptionLength >= standards.maxDescriptionLength) {
      errors.push('minDescriptionLength must be less than maxDescriptionLength');
    }

    if (standards.minDetailedDescriptionWords >= standards.maxDetailedDescriptionWords) {
      errors.push('minDetailedDescriptionWords must be less than maxDetailedDescriptionWords');
    }

    if (standards.minFeaturesCount >= standards.maxFeaturesCount) {
      errors.push('minFeaturesCount must be less than maxFeaturesCount');
    }

    if (standards.minProsCount >= standards.maxProsCount) {
      errors.push('minProsCount must be less than maxProsCount');
    }

    if (standards.minConsCount >= standards.maxConsCount) {
      errors.push('minConsCount must be less than maxConsCount');
    }
  }

  if (!Array.isArray(config.validationRules)) {
    errors.push('validationRules must be an array');
  }

  if (!Array.isArray(config.bannedWords)) {
    errors.push('bannedWords must be an array');
  }

  if (!Array.isArray(config.requiredFields)) {
    errors.push('requiredFields must be an array');
  }

  return errors;
}
