export interface Job {
  id: string;
  type: JobType;
  data: any;
  status: JobStatus;
  priority: JobPriority;
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
  updatedAt: Date;
  scheduledFor?: Date;
  completedAt?: Date;
  error?: string;
  result?: any;
  // Enhanced fields for new system
  progress?: number; // 0-100
  progressDetails?: ProgressDetails;
  canPause?: boolean;
  canResume?: boolean;
  canStop?: boolean;
  toolId?: string;
  parentJobId?: string; // For sub-jobs
  tags?: string[];
  estimatedDuration?: number; // in milliseconds
  actualDuration?: number; // in milliseconds
}

export enum JobType {
  TOOL_SUBMISSION = 'tool_submission',
  CONTENT_GENERATION = 'content_generation',
  WEB_SCRAPING = 'web_scraping',
  EMAIL_NOTIFICATION = 'email_notification',
  TOOL_PROCESSING = 'tool_processing',
  SCREENSHOT_CAPTURE = 'screenshot_capture',
  FAVICON_EXTRACTION = 'favicon_extraction',
  BULK_PROCESSING = 'bulk_processing',
  MEDIA_COLLECTION = 'media_collection',
}

export enum JobStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  RETRYING = 'retrying',
  CANCELLED = 'cancelled',
  PAUSED = 'paused',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
}

export enum JobPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4,
}

export interface JobHandler {
  handle(job: Job): Promise<any>;
}

export interface JobQueue {
  add(type: JobType, data: any, options?: JobOptions): Promise<Job>;
  process(): Promise<void>;
  getJob(id: string): Promise<Job | null>;
  getJobs(status?: JobStatus): Promise<Job[]>;
  removeJob(id: string): Promise<boolean>;
  retryJob(id: string): Promise<Job>;
  // Enhanced methods for new system
  pauseJob(id: string): Promise<Job>;
  resumeJob(id: string): Promise<Job>;
  stopJob(id: string): Promise<Job>;
  updateProgress(id: string, progress: number, details?: ProgressDetails): Promise<void>;
  getJobHistory(limit?: number, offset?: number): Promise<Job[]>;
  getJobsByTool(toolId: string): Promise<Job[]>;
  getQueueStats(): Promise<QueueStats>;
}

export interface JobOptions {
  priority?: JobPriority;
  delay?: number;
  maxAttempts?: number;
  scheduledFor?: Date;
}

export interface ToolSubmissionJobData {
  url: string;
  name: string;
  description?: string;
  category?: string;
  submitterEmail: string;
  submitterName?: string;
}

export interface ContentGenerationJobData {
  url: string;
  scrapedData: any;
  pricingData?: any;
  faqData?: any;
  toolId?: string;
  complexity?: 'simple' | 'medium' | 'complex';
  priority?: 'speed' | 'quality' | 'cost';
  contentQuality?: number;
  scrapingCost?: number;
  requireEditorialReview?: boolean;
  autoApprove?: boolean;
  qualityThreshold?: number;
  aiProvider?: 'openai' | 'openrouter'; // User's provider choice
}

export interface WebScrapingJobData {
  url: string;
  toolId?: string; // Optional tool ID for associating scraping with specific tool
  options?: {
    timeout?: number;
    waitForSelector?: string;
    extractImages?: boolean;
    extractLinks?: boolean;
  };
}

export interface EmailNotificationJobData {
  to: string | string[];
  subject: string;
  template: string;
  data: any;
  priority?: 'low' | 'normal' | 'high';
}

export interface BulkProcessingJobData {
  urls: string[];
  options: {
    batchSize: number;
    delayBetweenBatches: number;
    retryAttempts: number;
    aiProvider: 'openai' | 'openrouter';
    skipExisting: boolean;
    scrapeOnly: boolean;
    generateContent: boolean;
    resumeGeneration: boolean;
    autoPublish: boolean;
    priority: 'low' | 'normal' | 'high';
  };
  metadata: {
    jobType: 'text_file' | 'json_file' | 'manual_entry';
    filename?: string;
    submittedBy?: string;
  };
}

export interface MediaCollectionJobData {
  toolId: string;
  url: string;
  options?: {
    collectFavicon?: boolean;
    collectOgImages?: boolean;
    forceCollection?: boolean;
  };
}

// Enhanced Job Processing Interfaces

export interface ProgressDetails {
  phase: string;
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
  estimatedTimeRemaining?: number;
  message?: string;
  metadata?: Record<string, unknown>;
}

export interface QueueStats {
  totalJobs: number;
  pendingJobs: number;
  processingJobs: number;
  completedJobs: number;
  failedJobs: number;
  pausedJobs: number;
  activeJobs: number; // Added: Count of currently active/running jobs
  averageProgress: number; // Added: Average progress percentage across active jobs
  averageProcessingTime: number;
  successRate: number;
  lastUpdated: Date;
}

export interface JobControlOptions {
  force?: boolean;
  reason?: string;
  metadata?: Record<string, unknown>;
}

export interface JobHistoryFilter {
  status?: JobStatus[];
  type?: JobType[];
  toolId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  limit?: number;
  offset?: number;
}

export interface WebSocketJobUpdate {
  jobId: string;
  type: 'status_change' | 'progress_update' | 'error' | 'completed';
  data: {
    status?: JobStatus;
    progress?: number;
    progressDetails?: ProgressDetails;
    error?: string;
    result?: any;
    timestamp: Date;
  };
}

export interface JobEventListener {
  onStatusChange?: (job: Job, oldStatus: JobStatus) => void;
  onProgressUpdate?: (job: Job, progress: number, details?: ProgressDetails) => void;
  onError?: (job: Job, error: Error) => void;
  onCompleted?: (job: Job, result: any) => void;
}
