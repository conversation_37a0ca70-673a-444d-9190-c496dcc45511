'use client';

import React from 'react';
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';

interface AdminBreadcrumbsProps {
  currentPath: string;
}

interface BreadcrumbItem {
  label: string;
  path: string;
  isActive: boolean;
}

// Mapping of paths to readable labels
const PATH_LABELS: Record<string, string> = {
  '/admin': 'Dashboard',
  '/admin/tools': 'Tool Management',
  '/admin/tools/new': 'Add New Tool',
  '/admin/tools/edit': 'Edit Tool',
  '/admin/bulk': 'Bulk Processing',
  '/admin/jobs': 'Job Monitoring',
  '/admin/performance': 'Performance Analytics',
  '/admin/editorial': 'Editorial Control',
  '/admin/categories': 'Category Management',
  '/admin/settings': 'System Settings',
  '/admin/content': 'Content Generation',
  '/admin/content/validation-rules': 'Validation Rules',
  '/admin/analytics': 'Analytics Dashboard'
};

export function AdminBreadcrumbs({ currentPath }: AdminBreadcrumbsProps) {
  // Generate breadcrumb items from current path
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = currentPath.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always start with Admin Dashboard
    breadcrumbs.push({
      label: 'Dashboard',
      path: '/admin',
      isActive: currentPath === '/admin'
    });

    // If we're not on the dashboard, add subsequent segments
    if (currentPath !== '/admin') {
      let currentSegmentPath = '';
      
      for (let i = 0; i < pathSegments.length; i++) {
        const segment = pathSegments[i];
        
        // Skip the 'admin' segment as it's already handled
        if (segment === 'admin') continue;
        
        currentSegmentPath += `/${segment}`;
        const fullPath = `/admin${currentSegmentPath}`;
        
        // Get label from mapping or format the segment
        const label = PATH_LABELS[fullPath] || formatSegmentLabel(segment);
        
        breadcrumbs.push({
          label,
          path: fullPath,
          isActive: fullPath === currentPath
        });
      }
    }

    return breadcrumbs;
  };

  // Format segment labels for paths not in the mapping
  const formatSegmentLabel = (segment: string): string => {
    // Handle dynamic segments (like IDs)
    if (segment.match(/^[0-9a-f-]{36}$/)) {
      return 'Details'; // UUID pattern
    }
    
    if (segment.match(/^\d+$/)) {
      return `Item ${segment}`; // Numeric ID
    }

    // Convert kebab-case to Title Case
    return segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const breadcrumbs = generateBreadcrumbs();

  // Don't show breadcrumbs if we're on the dashboard
  if (currentPath === '/admin') {
    return null;
  }

  return (
    <nav className="bg-zinc-800 border-b border-zinc-700 px-6 py-3">
      <div className="flex items-center space-x-2 text-sm">
        {/* Home icon */}
        <Link
          href="/admin"
          className="text-gray-400 hover:text-white transition-colors p-1 rounded"
        >
          <Home size={16} />
        </Link>

        {/* Breadcrumb items */}
        {breadcrumbs.map((item, index) => (
          <React.Fragment key={item.path}>
            {/* Separator */}
            {index > 0 && (
              <ChevronRight size={14} className="text-gray-500" />
            )}

            {/* Breadcrumb item */}
            {item.isActive ? (
              <span className="text-white font-medium">
                {item.label}
              </span>
            ) : (
              <Link
                href={item.path}
                className="text-gray-400 hover:text-white transition-colors px-2 py-1 rounded hover:bg-zinc-700"
              >
                {item.label}
              </Link>
            )}
          </React.Fragment>
        ))}
      </div>
    </nav>
  );
}
