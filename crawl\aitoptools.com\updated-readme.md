# AI Tools Crawler

A robust, HTTP-based web crawler specialized for extracting AI tools data from aitoptools.com. This crawler implements advanced techniques for handling dynamic content loading, AJAX pagination, and detailed data extraction.

## Features

- **AJAX Pagination Handling**: Automatically handles "load more" functionality to access all tools
- **Multiple Category ID Detection Methods**: Robust detection of category IDs for accessing additional content
- **Concurrent Processing**: Multi-threaded design for faster crawling
- **Robust Error Handling**: Gracefully handles network issues and unexpected page formats
- **Checkpoint System**: Resume interrupted crawls from where they left off
- **Flexible Output**: Export data in JSON or CSV formats
- **Category Filtering**: Focus crawling on specific categories or subcategories

## Required Files

This crawler has been simplified to require only these core files:

- **enhanced_ajax_crawler.py**: Main crawler with AJAX handling capabilities
- **categories.py**: Defines the category structure for the website
- **fix_extractor.py**: Extracts detailed tool information from individual pages
- **utils.py**: Utility functions for data management and checkpointing
- **requirements.txt**: Lists the dependencies

## Installation

### Prerequisites

- Python 3.8+
- pip package manager

### Setup

1. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

```bash
python enhanced_ajax_crawler.py [options]
```

### Command-line Options

- `--format`: Output format - `json` (default) or `csv`
- `--max-tools`: Maximum number of tools to extract per subcategory (0 for all)
- `--concurrency`: Number of tool pages to process concurrently (default: 5)
- `--category`: Crawl only a specific main category
- `--subcategory`: Crawl only a specific subcategory (requires --category)

### Examples

#### Crawl a specific category and subcategory:
```bash
python enhanced_ajax_crawler.py --category "Tech & Development" --subcategory "Detector"
```

#### Extract all tools in a specific subcategory:
```bash
python enhanced_ajax_crawler.py --category "Tech & Development" --subcategory "Detector" --max-tools 0
```

#### Crawl the entire website:
```bash
python enhanced_ajax_crawler.py --max-tools 0
```

#### Export to CSV format:
```bash
python enhanced_ajax_crawler.py --format csv
```

#### Optimize for faster crawling:
```bash
python enhanced_ajax_crawler.py --concurrency 10
```

## Output Files

- **aitoptools_data_[timestamp].json/csv**: Contains all extracted tool data
- **aitoptools_crawler_checkpoint.json**: Checkpoint data for resuming interrupted crawls
- **enhanced_crawler.log**: Detailed logging information

## Troubleshooting

### Common Issues

1. **Network Errors**: If you encounter frequent network errors, try:
   - Reducing the concurrency level
   - Adding more sleep time between requests
   - Checking your internet connection

2. **Extraction Issues**: If the crawler misses data, check:
   - The log file for specific errors
   - Whether the website structure has changed
   
3. **Performance Issues**: For slow crawling:
   - Increase the concurrency level
   - Focus on specific categories/subcategories

### Resuming Failed Crawls

The crawler automatically creates checkpoints. To resume an interrupted crawl, simply run the command again with the same parameters.
