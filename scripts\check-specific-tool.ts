#!/usr/bin/env tsx

/**
 * Check Specific Tool
 * 
 * Check the specific tool from the logs to see why it doesn't have
 * the correct submission_source.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function checkSpecificTool() {
  console.log('🔍 Checking Specific Tool from Logs...\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Check the specific tool from the logs
    const toolId = 'e894f3ae-e8f4-4df5-bde3-1a5b6f3db1ed';
    
    console.log(`1. 🔍 Checking tool ${toolId}...`);
    const { data: tool, error: toolError } = await supabase
      .from('tools')
      .select('*')
      .eq('id', toolId)
      .single();

    if (toolError) {
      console.log(`❌ Error fetching tool: ${toolError.message}`);
      
      // Try to find FoodiePrep tools by name/website instead
      console.log('\n2. 🔍 Searching for FoodiePrep tools by name/website...');
      const { data: foodieTools, error: foodieError } = await supabase
        .from('tools')
        .select('*')
        .or('name.ilike.%foodieprep%,website.ilike.%foodieprep%')
        .order('created_at', { ascending: false });

      if (foodieError) {
        console.log(`❌ Error searching FoodiePrep: ${foodieError.message}`);
      } else if (foodieTools && foodieTools.length > 0) {
        console.log(`✅ Found ${foodieTools.length} FoodiePrep tool(s):`);
        
        foodieTools.forEach((t, index) => {
          console.log(`\n   Tool ${index + 1}: ${t.name} (${t.id})`);
          console.log(`      🌐 Website: ${t.website}`);
          console.log(`      📊 Content Status: ${t.content_status}`);
          console.log(`      🤖 AI Status: ${t.ai_generation_status}`);
          console.log(`      📝 Submission Type: ${t.submission_type}`);
          console.log(`      📋 Submission Source: ${t.submission_source}`);
          console.log(`      📅 Created: ${t.created_at}`);
          console.log(`      📅 Updated: ${t.updated_at}`);
          
          if (t.submission_source !== 'bulk_processing') {
            console.log(`      ❌ ISSUE: Should have submission_source = 'bulk_processing'`);
          } else {
            console.log(`      ✅ Correct submission_source`);
          }
        });
      } else {
        console.log('❌ No FoodiePrep tools found');
      }
      
    } else if (tool) {
      console.log(`✅ Tool found: ${tool.name}`);
      console.log(`   🌐 Website: ${tool.website}`);
      console.log(`   📊 Content Status: ${tool.content_status}`);
      console.log(`   🤖 AI Status: ${tool.ai_generation_status}`);
      console.log(`   📝 Submission Type: ${tool.submission_type}`);
      console.log(`   📋 Submission Source: ${tool.submission_source}`);
      console.log(`   📅 Created: ${tool.created_at}`);
      console.log(`   📅 Updated: ${tool.updated_at}`);
      
      if (tool.submission_source !== 'bulk_processing') {
        console.log('\n🔧 FIXING: Updating submission_source to bulk_processing...');
        
        const { error: updateError } = await supabase
          .from('tools')
          .update({
            submission_source: 'bulk_processing',
            submission_type: 'admin',
            updated_at: new Date().toISOString()
          })
          .eq('id', toolId);

        if (updateError) {
          console.log(`❌ Failed to update: ${updateError.message}`);
        } else {
          console.log(`✅ Updated tool to have submission_source = 'bulk_processing'`);
        }
      } else {
        console.log('✅ Tool already has correct submission_source');
      }
    } else {
      console.log('❌ Tool not found');
    }

    // 3. Check recent AI generation jobs for this tool
    console.log('\n3. 🔍 Checking recent AI generation jobs...');
    const { data: jobs, error: jobError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .eq('tool_id', toolId)
      .order('created_at', { ascending: false })
      .limit(3);

    if (jobError) {
      console.log(`❌ Error fetching jobs: ${jobError.message}`);
    } else if (jobs && jobs.length > 0) {
      console.log(`✅ Found ${jobs.length} job(s) for this tool:`);
      
      jobs.forEach((job, index) => {
        console.log(`\n   Job ${index + 1}: ${job.id}`);
        console.log(`      📋 Type: ${job.job_type}`);
        console.log(`      📊 Status: ${job.status}`);
        console.log(`      📈 Progress: ${job.progress}%`);
        console.log(`      📅 Created: ${job.created_at}`);
        console.log(`      📅 Updated: ${job.updated_at}`);
        if (job.error_logs) {
          console.log(`      ❌ Errors: ${JSON.stringify(job.error_logs)}`);
        }
      });
    } else {
      console.log('❌ No jobs found for this tool');
    }

    console.log('\n🎯 Summary:');
    console.log('   The tool needs submission_source = "bulk_processing" to trigger the bypass');
    console.log('   Once fixed, the pipeline should show "🚀 Bulk processing detected"');
    console.log('   And the tool should be published directly without manual review');

  } catch (error) {
    console.error('💥 Check failed:', error);
  }
}

// Run the check
checkSpecificTool().catch(console.error);
