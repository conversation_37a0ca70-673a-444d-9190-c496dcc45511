#!/usr/bin/env tsx

/**
 * Migration 010 Runner: Add 'archived' status to bulk_processing_jobs constraint
 * 
 * This script executes Migration 010 which adds 'archived' as a valid status
 * for the bulk_processing_jobs table, resolving constraint violations in the
 * job cleanup service.
 */

import { config } from 'dotenv';
import { readFileSync } from 'fs';
import { join } from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

// Also try .env as fallback
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration010() {
  console.log('🚀 Starting Migration 010: Add archived status to bulk_processing_jobs');
  console.log('📋 This migration will:');
  console.log('   • Drop existing bulk_processing_jobs status constraint');
  console.log('   • Add new constraint including "archived" status');
  console.log('   • Test the new constraint with verification queries');
  console.log('');

  try {
    // Read the migration SQL file
    const migrationPath = join(process.cwd(), 'src', 'lib', 'database', 'migrations', '010_add_archived_status_to_bulk_jobs.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf-8');

    console.log('📄 Loaded migration file: 010_add_archived_status_to_bulk_jobs.sql');

    // Try automatic execution first
    console.log('⚡ Attempting automatic SQL execution...');

    try {
      const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });

      if (error) {
        throw new Error(`RPC execution failed: ${error.message}`);
      }

      console.log('✅ Migration 010 executed automatically!');

    } catch (rpcError) {
      console.log('⚠️  Automatic execution failed, switching to manual mode...');
      console.log(`   Error: ${rpcError}`);
      console.log('');

      // Fallback to manual execution
      console.log('📋 MANUAL SQL EXECUTION REQUIRED');
      console.log('Please copy and paste the following SQL into your Supabase SQL Editor:');
      console.log('');
      console.log('🔗 Supabase SQL Editor: https://supabase.com/dashboard/project/gvcdqspryxrvxadfpwux/sql');
      console.log('');
      console.log('--- COPY AND PASTE THE FOLLOWING SQL ---');
      console.log(migrationSQL);
      console.log('--- END OF SQL ---');
      console.log('');

      // Wait for user confirmation
      console.log('After executing the SQL in Supabase, press Enter to continue...');
      await new Promise(resolve => {
        process.stdin.once('data', () => resolve(undefined));
      });

      console.log('✅ Manual execution completed!');
    }
    console.log('');

    // Verify the constraint was updated
    console.log('🔍 Verifying constraint update...');

    try {
      const { data: constraints, error: constraintError } = await supabase
        .from('pg_constraint')
        .select('conname, pg_get_constraintdef(oid)')
        .eq('conname', 'bulk_processing_jobs_status_check');

      if (constraintError) {
        console.warn('⚠️ Could not verify constraint (migration may still be successful):', constraintError.message);
      } else {
        console.log('✅ Constraint verification completed');
        if (constraints && constraints.length > 0) {
          console.log('📋 Updated constraint found in database');
        } else {
          console.log('⚠️ Constraint not found - please verify manually in Supabase');
        }
      }
    } catch (verifyError) {
      console.warn('⚠️ Constraint verification failed (migration may still be successful):', verifyError);
    }

    console.log('');
    console.log('🎉 Migration 010 completed successfully!');
    console.log('✅ Job cleanup service can now archive old jobs');
    console.log('✅ Constraint violation error should be resolved');

  } catch (error) {
    console.error('❌ Migration 010 failed with error:', error);
    process.exit(1);
  }
}

// Execute the migration
runMigration010().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
