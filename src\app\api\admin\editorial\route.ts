import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { submissionTransformer } from '@/lib/submission-transformer';
import { validateApiKey } from '@/lib/auth';

/**
 * GET /api/admin/editorial
 * Get editorial workflow data for the admin content review page
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get editorial reviews with tool information
    const { data: editorialReviews, error: reviewsError } = await supabase
      .from('editorial_reviews')
      .select(`
        id,
        tool_id,
        reviewed_by,
        review_status,
        review_date,
        featured_date,
        review_notes,
        editorial_text,
        quality_score,
        content_flags,
        approval_workflow,
        created_at,
        updated_at,
        tools!editorial_reviews_tool_id_fkey (
          id,
          name,
          website,
          description,
          generated_content,
          content_status,
          ai_generation_status
        )
      `)
      .order('created_at', { ascending: false })
      .limit(100);

    if (reviewsError) {
      console.error('Error fetching editorial reviews:', reviewsError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch editorial reviews' },
        { status: 500 }
      );
    }

    console.log(`📊 Editorial API: Found ${editorialReviews?.length || 0} editorial reviews`);
    editorialReviews?.forEach((review, index) => {
      console.log(`  ${index + 1}. Review ID: ${review.id}`);
      console.log(`     Tool ID: ${review.tool_id}`);
      console.log(`     Status: ${review.review_status}`);
      console.log(`     Has Tool Data: ${!!review.tools}`);
      console.log(`     Content Flags: ${JSON.stringify(review.content_flags)}`);
    });

    // Get AI generation jobs that need review
    const { data: aiJobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select(`
        id,
        tool_id,
        job_type,
        status,
        progress,
        ai_responses,
        created_at,
        updated_at,
        tools!ai_generation_jobs_tool_id_fkey (
          id,
          name,
          website,
          description,
          ai_generation_status,
          editorial_review_id
        )
      `)
      .eq('status', 'completed')
      .eq('job_type', 'generate')
      .order('created_at', { ascending: false })
      .limit(50);

    if (jobsError) {
      console.error('Error fetching AI generation jobs:', jobsError);
      // Don't fail the request, just log the error
    }

    // Transform data for the frontend
    const reviewItems = [];

    // Add existing editorial reviews
    if (editorialReviews) {
      for (const review of editorialReviews) {
        const tool = review.tools as any;

        // Always add the review, even if tool data is missing
        reviewItems.push({
          id: review.id,
          toolId: review.tool_id,
          toolName: tool?.name || `Tool ${review.tool_id}`,
          url: tool?.website || 'https://example.com',
          status: review.review_status,
          priority: 'medium', // Default priority
          qualityScore: review.quality_score || 75,
          generatedAt: review.created_at,
          reviewedAt: review.updated_at,
          reviewedBy: review.reviewed_by,
          reviewNotes: review.review_notes,
          contentPreview: tool?.description?.substring(0, 200) + '...' || 'No content preview available',
          wordCount: tool?.description?.split(' ').length || 0,
          issues: Array.isArray(review.content_flags) ? review.content_flags :
                 review.content_flags ? [review.content_flags] : [],
          type: 'editorial_review',
          // Add debug info
          debug: {
            hasToolData: !!tool,
            toolId: review.tool_id,
            contentFlags: review.content_flags
          }
        });
      }
    }

    // Add AI generation jobs that need review (only those without editorial review)
    if (aiJobs) {
      for (const job of aiJobs) {
        const tool = job.tools as any;
        if (tool && !tool.editorial_review_id) {
          const aiContent = job.ai_responses?.content || job.ai_responses?.description || '';
          reviewItems.push({
            id: `ai_job_${job.id}`,
            toolId: tool.id,
            toolName: tool.name,
            url: tool.website || 'https://example.com',
            status: 'pending',
            priority: 'medium',
            qualityScore: Math.floor(Math.random() * 30) + 70, // Random score between 70-100
            generatedAt: job.created_at,
            contentPreview: aiContent.substring(0, 200) + '...' || 'AI-generated content pending review',
            wordCount: aiContent.split(' ').length || 0,
            issues: [],
            type: 'ai_generation'
          });
        }
      }
    }

    return NextResponse.json({
      success: true,
      submissions: reviewItems,
      stats: {
        totalSubmissions: reviewItems.length,
        pendingReview: reviewItems.filter(r => r.status === 'pending').length,
        underReview: reviewItems.filter(r => r.status === 'under_review').length,
        approvedToday: reviewItems.filter(r => {
          const today = new Date().toISOString().split('T')[0];
          return r.status === 'approved' && r.reviewedAt?.startsWith(today);
        }).length,
        rejectedToday: reviewItems.filter(r => {
          const today = new Date().toISOString().split('T')[0];
          return r.status === 'rejected' && r.reviewedAt?.startsWith(today);
        }).length,
        averageReviewTime: 0
      }
    });

  } catch (error) {
    console.error('Editorial API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/editorial
 * Handle editorial workflow actions
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, data } = body;

    if (!action || !data) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: action, data' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'review':
        // Handle review approval/rejection
        const { id, status, reviewNotes, reviewedBy, toolId } = data;

        if (!id || !status) {
          return NextResponse.json(
            { success: false, error: 'Missing required fields: id, status' },
            { status: 400 }
          );
        }

        console.log(`📝 Processing review action: ${status} for review ID: ${id}`);

        // Check if this is an AI generation job or editorial review
        if (id.startsWith('ai_job_')) {
          const jobId = id.replace('ai_job_', '');
          console.log('Processing AI job approval:', { id, jobId });

          // First, get the AI generation job to find the correct tool_id
          const { data: aiJob, error: jobError } = await supabase
            .from('ai_generation_jobs')
            .select('tool_id')
            .eq('id', jobId)
            .single();

          console.log('AI job query result:', { aiJob, jobError });

          if (jobError || !aiJob) {
            console.error('Error fetching AI generation job:', jobError);
            return NextResponse.json(
              { success: false, error: 'AI generation job not found' },
              { status: 404 }
            );
          }

          // Create new editorial review for AI job using the correct tool_id
          console.log('Creating editorial review with tool_id:', aiJob.tool_id);
          const { data: newReview, error: createError } = await supabase
            .from('editorial_reviews')
            .insert({
              tool_id: aiJob.tool_id,
              reviewed_by: reviewedBy || 'admin',
              review_status: status,
              review_date: new Date().toISOString().split('T')[0],
              review_notes: reviewNotes,
              quality_score: Math.min(10, Math.max(1, Math.round((data.qualityScore || 85) / 10)))
            })
            .select()
            .single();

          if (createError) {
            console.error('Error creating editorial review:', createError);
            return NextResponse.json(
              { success: false, error: 'Failed to create editorial review' },
              { status: 500 }
            );
          }

          // Update tool with editorial review reference
          await supabase
            .from('tools')
            .update({ editorial_review_id: newReview.id })
            .eq('id', aiJob.tool_id);

        } else {
          // Update existing editorial review
          console.log(`📝 Updating editorial review ${id} with status: ${status}`);

          const { data: updateResult, error: updateError } = await supabase
            .from('editorial_reviews')
            .update({
              review_status: status,
              review_notes: reviewNotes || `Review ${status} by admin`,
              reviewed_by: reviewedBy || 'admin',
              review_date: new Date().toISOString().split('T')[0],
              updated_at: new Date().toISOString()
            })
            .eq('id', id)
            .select();

          if (updateError) {
            console.error('Error updating editorial review:', updateError);
            return NextResponse.json(
              { success: false, error: 'Failed to update editorial review' },
              { status: 500 }
            );
          }

          console.log(`✅ Editorial review ${id} updated successfully:`, updateResult);

          // If approved, trigger publication workflow
          if (status === 'approved') {
            console.log(`🚀 Triggering publication workflow for tool: ${toolId || 'unknown'}`);

            // Get the tool ID from the editorial review if not provided
            const actualToolId = toolId || updateResult[0]?.tool_id;

            if (actualToolId) {
              // Update tool status to published
              const { data: publishedTool, error: publishError } = await supabase
                .from('tools')
                .update({
                  content_status: 'published',
                  published_at: new Date().toISOString(),
                  editorial_review_id: id,
                  updated_at: new Date().toISOString()
                })
                .eq('id', actualToolId)
                .select();

              if (publishError) {
                console.error('❌ Failed to publish tool:', publishError);
                // Don't fail the approval, just log the error
              } else {
                console.log(`✅ Tool ${actualToolId} published successfully:`, publishedTool);
              }
            } else {
              console.warn('⚠️ Could not determine tool ID for publication');
            }
          }
        }

        return NextResponse.json({
          success: true,
          message: `Review ${status} successfully`
        });

      case 'approve_submission':
        // Handle submission approval with workflow differentiation
        const { submissionId, approvedBy } = data;

        if (!submissionId) {
          return NextResponse.json(
            { success: false, error: 'Missing required field: submissionId' },
            { status: 400 }
          );
        }

        // Update submission status to approved
        const { error: approvalError } = await supabase
          .from('tool_submissions')
          .update({
            status: 'approved',
            reviewed_by: approvedBy || 'admin',
            reviewed_at: new Date().toISOString(),
            review_notes: 'Approved for processing'
          })
          .eq('id', submissionId);

        if (approvalError) {
          console.error('Error approving submission:', approvalError);
          return NextResponse.json(
            { success: false, error: 'Failed to approve submission' },
            { status: 500 }
          );
        }

        // Process the approved submission based on type
        const result = await submissionTransformer.processApprovedSubmission(submissionId, approvedBy);

        return NextResponse.json({
          success: result.success,
          message: result.message,
          toolId: result.toolId
        });

      default:
        return NextResponse.json(
          { success: false, error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Editorial POST API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
