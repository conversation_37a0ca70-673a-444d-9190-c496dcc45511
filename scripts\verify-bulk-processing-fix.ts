#!/usr/bin/env tsx

/**
 * Verify Bulk Processing Database Fix
 * 
 * This script verifies that the PostgreSQL functions required for
 * atomic bulk processing operations are working correctly in Supabase.
 */

import { createClient } from '@supabase/supabase-js';

// Environment variables
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function testFunction(functionName: string, params: any) {
  console.log(`🧪 Testing ${functionName}...`);
  
  try {
    const { data, error } = await supabase.rpc(functionName, params);
    
    if (error) {
      console.log(`   ❌ Error: ${error.message}`);
      return false;
    }
    
    console.log(`   ✅ Function exists and responds correctly`);
    console.log(`   📄 Response:`, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.log(`   ❌ Exception:`, error);
    return false;
  }
}

async function verifyFunctions() {
  console.log('🔍 Verifying bulk processing database functions...\n');
  
  const testUuid = '00000000-0000-0000-0000-000000000000';

  const tests = [
    {
      name: 'update_bulk_job_status_atomic',
      params: {
        p_job_id: testUuid,
        p_new_status: 'processing'
      },
      expectedError: 'job_not_found'
    },
    {
      name: 'update_bulk_job_progress_atomic',
      params: {
        p_job_id: testUuid,
        p_processed_items: 5,
        p_successful_items: 3,
        p_failed_items: 2
      },
      expectedError: 'job_not_found'
    },
    {
      name: 'append_bulk_job_log_atomic',
      params: {
        p_job_id: testUuid,
        p_log_entry: {
          timestamp: new Date().toISOString(),
          message: 'Test log entry',
          level: 'info'
        }
      },
      expectedError: 'job_not_found'
    },
    {
      name: 'complete_bulk_job_atomic',
      params: {
        p_job_id: testUuid
      },
      expectedError: 'job_not_found'
    },
    {
      name: 'is_valid_status_transition',
      params: {
        p_current_status: 'pending',
        p_new_status: 'processing'
      },
      expectedResult: true
    }
  ];
  
  let allPassed = true;
  
  for (const test of tests) {
    const result = await testFunction(test.name, test.params);
    if (!result) {
      allPassed = false;
    }
    console.log(''); // Add spacing
  }
  
  return allPassed;
}

async function testWithRealJob() {
  console.log('🧪 Testing with a real bulk processing job...\n');
  
  try {
    // Get an existing job from the database
    const { data: jobs, error } = await supabase
      .from('bulk_processing_jobs')
      .select('id, status, version')
      .limit(1);
    
    if (error) {
      console.log('❌ Could not fetch existing jobs:', error.message);
      return false;
    }
    
    if (!jobs || jobs.length === 0) {
      console.log('ℹ️  No existing bulk processing jobs found to test with');
      return true;
    }
    
    const job = jobs[0];
    console.log(`📋 Testing with job: ${job.id} (status: ${job.status}, version: ${job.version})`);
    
    // Test status update with correct version
    console.log('🧪 Testing status update with correct version...');
    const { data: updateResult, error: updateError } = await supabase.rpc('update_bulk_job_status_atomic', {
      p_job_id: job.id, // job.id is already UUID from database
      p_new_status: job.status, // Same status to avoid changing anything
      p_expected_version: job.version
    });
    
    if (updateError) {
      console.log('❌ Status update failed:', updateError.message);
      return false;
    }
    
    console.log('✅ Status update successful');
    console.log('📄 Result:', JSON.stringify(updateResult, null, 2));
    
    // Test version mismatch
    console.log('\n🧪 Testing version mismatch detection...');
    const { data: mismatchResult, error: mismatchError } = await supabase.rpc('update_bulk_job_status_atomic', {
      p_job_id: job.id, // job.id is already UUID from database
      p_new_status: job.status,
      p_expected_version: 999999 // Intentionally wrong version
    });
    
    if (mismatchError) {
      console.log('❌ Version mismatch test failed:', mismatchError.message);
      return false;
    }
    
    if (mismatchResult && mismatchResult.error === 'version_mismatch') {
      console.log('✅ Version mismatch correctly detected');
      console.log('📄 Result:', JSON.stringify(mismatchResult, null, 2));
    } else {
      console.log('❌ Version mismatch not detected properly');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ Real job test failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting bulk processing database verification...\n');
  
  // Test all functions
  const functionsWork = await verifyFunctions();
  
  if (!functionsWork) {
    console.log('❌ Some functions are not working correctly.');
    console.log('📋 Please ensure you have executed the SQL from scripts/supabase-bulk-processing-fix.sql');
    process.exit(1);
  }
  
  // Test with real job if available
  const realJobTest = await testWithRealJob();
  
  if (functionsWork && realJobTest) {
    console.log('🎉 All bulk processing database functions are working correctly!');
    console.log('✅ Bulk processing jobs should now complete their full lifecycle without errors.');
    console.log('');
    console.log('📋 Next steps:');
    console.log('1. Restart your Next.js development server');
    console.log('2. Try creating a new bulk processing job');
    console.log('3. Monitor the logs for successful status updates');
  } else {
    console.log('⚠️  Some tests failed. Please check the output above for details.');
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error('💥 Verification script failed:', error);
  process.exit(1);
});
