#!/usr/bin/env tsx

/**
 * Test Script: Verify bulk processing system is working correctly
 * 
 * This script tests that the bulk processing system can initialize
 * and that all required environment variables are properly configured.
 */

import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

async function testBulkProcessingSystem() {
  console.log('🧪 Testing Bulk Processing System');
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Check environment variables
    console.log('1️⃣ Checking environment variables...');
    
    const requiredEnvVars = [
      'JOB_QUEUE_ENABLED',
      'MAX_CONCURRENT_JOBS',
      'JOB_RETRY_ATTEMPTS',
      'CONTENT_GENERATION_ENABLED',
      'ENABLE_BULK_PROCESSING'
    ];
    
    const missingVars: string[] = [];
    const envStatus: Record<string, string> = {};
    
    requiredEnvVars.forEach(varName => {
      const value = process.env[varName];
      if (!value) {
        missingVars.push(varName);
        envStatus[varName] = '❌ Missing';
      } else {
        envStatus[varName] = `✅ ${value}`;
      }
    });
    
    console.log('Environment Variables Status:');
    Object.entries(envStatus).forEach(([key, status]) => {
      console.log(`   ${key}: ${status}`);
    });
    
    if (missingVars.length > 0) {
      console.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
      return false;
    }
    
    console.log('✅ All required environment variables are set');
    
    // Step 2: Test job queue initialization
    console.log('2️⃣ Testing job queue initialization...');
    
    const { initializeEnhancedJobQueue, isEnhancedJobSystemAvailable } = await import('../src/lib/jobs/init');
    
    // Initialize the job queue
    initializeEnhancedJobQueue();
    
    // Check if it's available
    const isAvailable = isEnhancedJobSystemAvailable();
    
    if (!isAvailable) {
      console.error('❌ Enhanced job system is not available after initialization');
      return false;
    }
    
    console.log('✅ Enhanced job system initialized successfully');
    
    // Step 3: Test bulk processing engine import
    console.log('3️⃣ Testing bulk processing engine...');
    
    const { BulkProcessingEngine } = await import('../src/lib/bulk-processing/bulk-engine');
    
    // Try to create an instance (this tests constructor and dependencies)
    const bulkEngine = new BulkProcessingEngine();
    
    console.log('✅ Bulk processing engine created successfully');
    
    // Step 4: Test job manager
    console.log('4️⃣ Testing job manager...');
    
    const { getJobManager } = await import('../src/lib/jobs/job-manager');
    const jobManager = getJobManager();
    
    console.log('✅ Job manager initialized successfully');
    
    // Step 5: Test API endpoint availability
    console.log('5️⃣ Testing API endpoint configuration...');
    
    // Check if the bulk processing API route exists
    try {
      const fs = await import('fs');
      const path = await import('path');
      
      const apiRoutePath = path.join(process.cwd(), 'src', 'app', 'api', 'admin', 'bulk-processing', 'route.ts');
      
      if (fs.existsSync(apiRoutePath)) {
        console.log('✅ Bulk processing API route exists');
      } else {
        console.error('❌ Bulk processing API route not found');
        return false;
      }
    } catch (error) {
      console.error('❌ Error checking API route:', error);
      return false;
    }
    
    // Step 6: Test configuration values
    console.log('6️⃣ Testing configuration values...');
    
    const config = {
      jobQueueEnabled: process.env.JOB_QUEUE_ENABLED === 'true',
      maxConcurrentJobs: parseInt(process.env.MAX_CONCURRENT_JOBS || '3'),
      jobRetryAttempts: parseInt(process.env.JOB_RETRY_ATTEMPTS || '3'),
      contentGenerationEnabled: process.env.CONTENT_GENERATION_ENABLED === 'true',
      bulkProcessingEnabled: process.env.ENABLE_BULK_PROCESSING === 'true'
    };
    
    console.log('Configuration:');
    console.log(`   Job Queue Enabled: ${config.jobQueueEnabled ? '✅' : '❌'}`);
    console.log(`   Max Concurrent Jobs: ${config.maxConcurrentJobs}`);
    console.log(`   Job Retry Attempts: ${config.jobRetryAttempts}`);
    console.log(`   Content Generation: ${config.contentGenerationEnabled ? '✅' : '❌'}`);
    console.log(`   Bulk Processing: ${config.bulkProcessingEnabled ? '✅' : '❌'}`);
    
    if (!config.jobQueueEnabled) {
      console.error('❌ Job queue is disabled - bulk processing will not work');
      return false;
    }
    
    if (!config.bulkProcessingEnabled) {
      console.error('❌ Bulk processing is disabled');
      return false;
    }
    
    console.log('✅ All configuration values are correct');
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

async function main() {
  const success = await testBulkProcessingSystem();
  
  console.log('');
  console.log('=' .repeat(60));
  
  if (success) {
    console.log('🎉 All tests passed!');
    console.log('✅ Bulk processing system is properly configured');
    console.log('✅ Job queue is enabled and functional');
    console.log('✅ All required environment variables are set');
    console.log('');
    console.log('💡 The bulk processing system should now work correctly!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Restart your development server');
    console.log('2. Try creating a bulk processing job in the admin panel');
    console.log('3. Monitor the console for job queue initialization messages');
  } else {
    console.log('❌ Tests failed!');
    console.log('❌ Bulk processing system is not properly configured');
    console.log('');
    console.log('🔧 Troubleshooting steps:');
    console.log('1. Check that all environment variables are set correctly');
    console.log('2. Ensure JOB_QUEUE_ENABLED=true in your .env.local file');
    console.log('3. Restart your development server after making changes');
    console.log('4. Check the console for any initialization errors');
  }
  
  process.exit(success ? 0 : 1);
}

main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
