/**
 * Job Cleanup Service Tests
 * Tests comprehensive job lifecycle cleanup and orphaned job detection
 */

import { getJobCleanupService } from '@/lib/bulk-processing/job-cleanup-service';

// Mock Supabase client
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn().mockReturnValue({
        order: jest.fn().mockReturnValue({
          data: [
            {
              id: 'job-1',
              status: 'completed',
              created_at: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(), // 25 hours ago
              updated_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
              job_type: 'manual_entry'
            },
            {
              id: 'job-2',
              status: 'processing',
              created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
              updated_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // No updates for 3 hours
              job_type: 'text_file'
            },
            {
              id: 'job-3',
              status: 'pending',
              created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
              updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
              job_type: 'json_file'
            },
            {
              id: 'job-4',
              status: 'invalid_status',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              job_type: 'manual_entry'
            }
          ],
          error: null
        })
      }),
      update: jest.fn().mockReturnValue({ 
        eq: jest.fn().mockReturnValue({ error: null })
      })
    }))
  })),
}));

// Mock memory manager
jest.mock('@/lib/jobs/memory-manager', () => ({
  getJobMemoryManager: jest.fn(() => ({
    cleanupJobResources: jest.fn().mockResolvedValue(undefined),
    getMemoryStats: jest.fn().mockReturnValue({
      activeResources: 5,
      activeJobs: 3,
      heapUsed: 150,
      heapTotal: 512
    })
  }))
}));

// Mock sync manager
jest.mock('@/lib/bulk-processing/sync-manager', () => ({
  getSynchronizationManager: jest.fn(() => ({
    isLocked: jest.fn().mockReturnValue(false),
    cleanupExpiredLocks: jest.fn()
  }))
}));

// Mock transaction manager
jest.mock('@/lib/bulk-processing/transaction-manager', () => ({
  getTransactionManager: jest.fn(() => ({
    cleanup: jest.fn()
  }))
}));

describe('Job Cleanup Service', () => {
  let cleanupService: any;

  beforeEach(() => {
    cleanupService = getJobCleanupService();
    jest.clearAllMocks();
  });

  afterEach(() => {
    cleanupService.shutdown();
  });

  describe('Orphaned Job Detection', () => {
    test('should detect stale completed jobs', async () => {
      const orphanedJobs = await cleanupService.detectOrphanedJobs({
        maxAgeHours: 24
      });

      const staleJobs = orphanedJobs.filter((job: any) => job.reason === 'stale');
      expect(staleJobs).toHaveLength(1);
      expect(staleJobs[0].id).toBe('job-1');
      expect(staleJobs[0].status).toBe('completed');
    });

    test('should detect stuck processing jobs', async () => {
      const orphanedJobs = await cleanupService.detectOrphanedJobs({
        includeStuckJobs: true
      });

      const stuckJobs = orphanedJobs.filter((job: any) => job.reason === 'stuck');
      expect(stuckJobs).toHaveLength(1);
      expect(stuckJobs[0].id).toBe('job-2');
      expect(stuckJobs[0].status).toBe('processing');
    });

    test('should detect abandoned pending jobs', async () => {
      const orphanedJobs = await cleanupService.detectOrphanedJobs({
        includeAbandonedJobs: true
      });

      const abandonedJobs = orphanedJobs.filter((job: any) => job.reason === 'abandoned');
      expect(abandonedJobs).toHaveLength(1);
      expect(abandonedJobs[0].id).toBe('job-3');
      expect(abandonedJobs[0].status).toBe('pending');
    });

    test('should detect corrupted jobs with invalid status', async () => {
      const orphanedJobs = await cleanupService.detectOrphanedJobs();

      const corruptedJobs = orphanedJobs.filter((job: any) => job.reason === 'corrupted');
      expect(corruptedJobs).toHaveLength(1);
      expect(corruptedJobs[0].id).toBe('job-4');
      expect(corruptedJobs[0].status).toBe('invalid_status');
    });

    test('should respect configuration options', async () => {
      const orphanedJobs = await cleanupService.detectOrphanedJobs({
        maxAgeHours: 48, // Higher threshold
        includeStuckJobs: false,
        includeAbandonedJobs: false
      });

      // Should only find corrupted jobs when stuck and abandoned are disabled
      expect(orphanedJobs).toHaveLength(1);
      expect(orphanedJobs[0].reason).toBe('corrupted');
    });
  });

  describe('Job Cleanup', () => {
    test('should clean up orphaned jobs successfully', async () => {
      const orphanedJobs = [
        {
          id: 'job-1',
          status: 'completed',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          job_type: 'manual_entry',
          age_hours: 25,
          reason: 'stale' as const
        }
      ];

      const stats = await cleanupService.cleanupOrphanedJobs(orphanedJobs);

      expect(stats.totalJobsScanned).toBe(1);
      expect(stats.jobsCleaned).toBe(1);
      expect(stats.errors).toHaveLength(0);
    });

    test('should handle cleanup errors gracefully', async () => {
      // Create a fresh cleanup service instance to avoid mock interference
      const { JobCleanupService } = require('@/lib/bulk-processing/job-cleanup-service');
      const testCleanupService = new JobCleanupService();

      // Mock the cleanupSingleJob method to throw an error
      const originalCleanupSingleJob = testCleanupService.cleanupSingleJob;
      testCleanupService.cleanupSingleJob = jest.fn().mockRejectedValue(new Error('Cleanup failed'));

      const orphanedJobs = [
        {
          id: 'job-error',
          status: 'failed',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          job_type: 'manual_entry',
          age_hours: 25,
          reason: 'stale' as const
        }
      ];

      const stats = await testCleanupService.cleanupOrphanedJobs(orphanedJobs);

      expect(stats.totalJobsScanned).toBe(1);
      expect(stats.jobsCleaned).toBe(0);
      expect(stats.errors).toHaveLength(1);
      expect(stats.errors[0]).toContain('Cleanup failed');

      testCleanupService.shutdown();
    });

    test('should process jobs in batches', async () => {
      const orphanedJobs = Array.from({ length: 15 }, (_, i) => ({
        id: `job-${i}`,
        status: 'completed',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        job_type: 'manual_entry',
        age_hours: 25,
        reason: 'stale' as const
      }));

      const stats = await cleanupService.cleanupOrphanedJobs(orphanedJobs, {
        batchSize: 5
      });

      expect(stats.totalJobsScanned).toBe(15);
      expect(stats.jobsCleaned).toBe(15);
    });

    test('should support dry run mode', async () => {
      const orphanedJobs = [
        {
          id: 'job-dry-run',
          status: 'completed',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          job_type: 'manual_entry',
          age_hours: 25,
          reason: 'stale' as const
        }
      ];

      const stats = await cleanupService.cleanupOrphanedJobs(orphanedJobs, {
        dryRun: true
      });

      expect(stats.totalJobsScanned).toBe(1);
      expect(stats.jobsCleaned).toBe(0); // No actual cleanup in dry run
    });
  });

  describe('Manual Cleanup', () => {
    test('should perform manual cleanup with custom options', async () => {
      const stats = await cleanupService.performManualCleanup({
        maxAgeHours: 12,
        includeStuckJobs: true,
        includeAbandonedJobs: false,
        batchSize: 5
      });

      expect(stats.totalJobsScanned).toBeGreaterThan(0);
      expect(typeof stats.jobsCleaned).toBe('number');
      expect(Array.isArray(stats.errors)).toBe(true);
    });
  });

  describe('Automated Cleanup', () => {
    test('should start and stop automated cleanup', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      cleanupService.startAutomatedCleanup(1); // 1 minute for testing
      expect(consoleSpy).toHaveBeenCalledWith('🤖 Automated cleanup started (every 1 minutes)');

      cleanupService.shutdown();
      expect(consoleSpy).toHaveBeenCalledWith('🛑 Job cleanup service shutdown');

      consoleSpy.mockRestore();
    });

    test('should start and stop cleanup monitoring', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      cleanupService.startCleanupMonitoring(1); // 1 minute for testing
      expect(consoleSpy).toHaveBeenCalledWith('📊 Cleanup monitoring started (every 1 minutes)');

      cleanupService.shutdown();

      consoleSpy.mockRestore();
    });
  });

  describe('Error Handling', () => {
    test('should handle database errors during detection', async () => {
      // Create a fresh cleanup service instance with error mock
      const { JobCleanupService } = require('@/lib/bulk-processing/job-cleanup-service');

      // Mock createClient to return a client that throws errors
      const mockCreateClient = jest.fn(() => ({
        from: jest.fn(() => ({
          select: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              data: null,
              error: { message: 'Database connection failed' }
            })
          })
        }))
      }));

      // Temporarily override the createClient import
      const originalCreateClient = require('@supabase/supabase-js').createClient;
      require('@supabase/supabase-js').createClient = mockCreateClient;

      const testCleanupService = new JobCleanupService();

      await expect(testCleanupService.detectOrphanedJobs()).rejects.toThrow('Failed to fetch jobs: Database connection failed');

      // Restore original mock
      require('@supabase/supabase-js').createClient = originalCreateClient;
      testCleanupService.shutdown();
    });

    test('should handle invalid job data gracefully', async () => {
      // Mock Supabase to return invalid data
      const mockSupabase = require('@supabase/supabase-js').createClient();
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            data: [
              {
                id: 'invalid-job',
                status: null,
                created_at: 'invalid-date',
                updated_at: null,
                job_type: undefined
              }
            ],
            error: null
          })
        })
      });

      const orphanedJobs = await cleanupService.detectOrphanedJobs();
      
      // Should handle invalid data without crashing
      expect(Array.isArray(orphanedJobs)).toBe(true);
    });
  });
});
