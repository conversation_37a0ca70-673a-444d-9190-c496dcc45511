#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import { PromptManager } from '../src/lib/ai/prompt-manager';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testDynamicValidationRules() {
  console.log('🧪 TESTING DYNAMIC VALIDATION RULES');
  console.log('=' .repeat(60));

  try {
    // 1. Check current validation rules in database
    console.log('\n1️⃣ Checking current validation rules in database...');
    const { data: configData, error: configError } = await supabase
      .from('system_configuration')
      .select('config_value')
      .eq('config_key', 'validation_rules_config')
      .single();

    if (configError) {
      console.log('❌ No validation rules found in database, will use defaults');
      console.log(`   Error: ${configError.message}`);
    } else {
      console.log('✅ Found validation rules in database:');
      const rules = configData.config_value;
      if (rules.contentStandards) {
        console.log(`   • Min Detailed Description: ${rules.contentStandards.minDetailedDescriptionWords} words`);
        console.log(`   • Max Detailed Description: ${rules.contentStandards.maxDetailedDescriptionWords} words`);
        console.log(`   • Min Features: ${rules.contentStandards.minFeaturesCount}`);
        console.log(`   • Max Features: ${rules.contentStandards.maxFeaturesCount}`);
        console.log(`   • Min Pros: ${rules.contentStandards.minProsCount}`);
        console.log(`   • Max Pros: ${rules.contentStandards.maxProsCount}`);
        console.log(`   • Min Cons: ${rules.contentStandards.minConsCount}`);
        console.log(`   • Max Cons: ${rules.contentStandards.maxConsCount}`);
      }
    }

    // 2. Test schema generation with current rules
    console.log('\n2️⃣ Testing dynamic schema generation...');
    const schema = await PromptManager.getAIDudeDatabaseSchema();
    
    console.log('✅ Schema generated successfully');
    console.log(`   • detailed_description: ${schema.detailed_description}`);
    console.log(`   • features: ${schema.features[0]}`);
    console.log(`   • pros: ${schema.pros_and_cons.pros[0]}`);
    console.log(`   • cons: ${schema.pros_and_cons.cons[0]}`);
    console.log(`   • hashtags: ${schema.hashtags[0]}`);
    console.log(`   • meta_title: ${schema.meta_title}`);
    console.log(`   • meta_description: ${schema.meta_description}`);

    // 3. Test system prompt generation with dynamic schema
    console.log('\n3️⃣ Testing system prompt generation with dynamic schema...');
    const systemPrompt = await PromptManager.buildAIDudeSystemPrompt(schema);
    
    console.log('✅ System prompt generated successfully');
    console.log(`   • Length: ${systemPrompt.length} characters`);
    console.log(`   • Contains schema: ${systemPrompt.includes('detailed_description') ? '✅' : '❌'}`);
    console.log(`   • Contains validation rules: ${systemPrompt.includes('words') ? '✅' : '❌'}`);

    // 4. Test updating validation rules and checking if schema changes
    console.log('\n4️⃣ Testing validation rule updates...');
    
    // Create test validation rules
    const testValidationRules = {
      contentStandards: {
        minDetailedDescriptionWords: 200, // Changed from default 150
        maxDetailedDescriptionWords: 400, // Changed from default 300
        minFeaturesCount: 5, // Changed from default 3
        maxFeaturesCount: 12, // Changed from default 8
        minProsCount: 4, // Changed from default 3
        maxProsCount: 15, // Changed from default 10
        minConsCount: 4, // Changed from default 3
        maxConsCount: 15, // Changed from default 10
        minHashtagsCount: 8, // Changed from default 5
        maxHashtagsCount: 15, // Changed from default 10
        maxMetaTitleLength: 70, // Changed from default 60
        minMetaDescriptionLength: 140, // Changed from default 150
        maxMetaDescriptionLength: 170, // Changed from default 160
        maxTooltipLength: 120 // Changed from default 100
      }
    };

    // Update validation rules in database
    const { error: updateError } = await supabase
      .from('system_configuration')
      .upsert({
        config_key: 'validation_rules_config',
        config_value: testValidationRules,
        config_type: 'ai_provider',
        is_active: true,
        description: 'Content validation rules and standards configuration',
        updated_by: 'admin',
        updated_at: new Date().toISOString()
      });

    if (updateError) {
      console.log(`❌ Failed to update validation rules: ${updateError.message}`);
    } else {
      console.log('✅ Updated validation rules in database');

      // Generate schema again to see if it picks up the changes
      const updatedSchema = await PromptManager.getAIDudeDatabaseSchema();
      
      console.log('✅ Generated schema with updated rules:');
      console.log(`   • detailed_description: ${updatedSchema.detailed_description}`);
      console.log(`   • features: ${updatedSchema.features[0]}`);
      console.log(`   • pros: ${updatedSchema.pros_and_cons.pros[0]}`);
      console.log(`   • cons: ${updatedSchema.pros_and_cons.cons[0]}`);
      console.log(`   • hashtags: ${updatedSchema.hashtags[0]}`);
      console.log(`   • meta_title: ${updatedSchema.meta_title}`);
      console.log(`   • meta_description: ${updatedSchema.meta_description}`);

      // Check if the changes are reflected
      const hasUpdatedRules = 
        updatedSchema.detailed_description.includes('200-400') &&
        updatedSchema.features[0].includes('5-12') &&
        updatedSchema.pros_and_cons.pros[0].includes('4-15') &&
        updatedSchema.pros_and_cons.cons[0].includes('4-15');

      if (hasUpdatedRules) {
        console.log('✅ Validation rules are properly applied to schema!');
      } else {
        console.log('❌ Validation rules are not being applied correctly');
      }
    }

    // 5. Restore original validation rules (cleanup)
    console.log('\n5️⃣ Restoring original validation rules...');
    if (configData && !configError) {
      const { error: restoreError } = await supabase
        .from('system_configuration')
        .upsert({
          config_key: 'validation_rules_config',
          config_value: configData.config_value,
          config_type: 'ai_provider',
          is_active: true,
          description: 'Content validation rules and standards configuration',
          updated_by: 'admin',
          updated_at: new Date().toISOString()
        });

      if (restoreError) {
        console.log(`❌ Failed to restore original rules: ${restoreError.message}`);
      } else {
        console.log('✅ Restored original validation rules');
      }
    }

    console.log('\n📋 Test Summary:');
    console.log('✅ Dynamic validation rules system is working');
    console.log('✅ Schema generation uses current database rules');
    console.log('✅ System prompts include dynamic validation requirements');
    console.log('✅ Admin panel changes will now propagate to AI generation');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testDynamicValidationRules().catch(console.error);
}

export { testDynamicValidationRules };
