#!/usr/bin/env tsx

/**
 * Monitor for Duplicate Files
 * 
 * This script monitors the scraped-content directory for duplicate files
 * and reports any domains that have multiple .md files.
 */

import { promises as fs } from 'fs';
import { join } from 'path';

interface DomainFiles {
  domain: string;
  files: string[];
  count: number;
}

async function monitorDuplicates() {
  console.log('🔍 Monitoring for Duplicate Files...\n');

  try {
    const baseDir = join(process.cwd(), 'data', 'scraped-content');
    
    // Check if base directory exists
    try {
      await fs.access(baseDir);
    } catch {
      console.log('📁 Scraped content directory does not exist yet');
      return;
    }

    const entries = await fs.readdir(baseDir, { withFileTypes: true });
    const domains: DomainFiles[] = [];

    // Scan each domain directory
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const domainPath = join(baseDir, entry.name);
        
        try {
          const domainFiles = await fs.readdir(domainPath);
          const mdFiles = domainFiles.filter(f => f.endsWith('.md'));
          
          if (mdFiles.length > 0) {
            domains.push({
              domain: entry.name,
              files: mdFiles,
              count: mdFiles.length
            });
          }
        } catch (error) {
          console.warn(`⚠️ Could not read domain directory ${entry.name}:`, error);
        }
      }
    }

    // Sort by file count (duplicates first)
    domains.sort((a, b) => b.count - a.count);

    console.log(`📊 Found ${domains.length} domains with scraped content:\n`);

    let duplicatesFound = false;

    domains.forEach((domain, index) => {
      const status = domain.count > 1 ? '❌ DUPLICATES' : '✅ SINGLE';
      console.log(`${index + 1}. ${domain.domain} - ${domain.count} files ${status}`);
      
      if (domain.count > 1) {
        duplicatesFound = true;
        domain.files.forEach((file, fileIndex) => {
          console.log(`   ${fileIndex + 1}. ${file}`);
        });
      }
    });

    console.log('\n🎯 Summary:');
    
    if (duplicatesFound) {
      const duplicateDomains = domains.filter(d => d.count > 1);
      console.log(`❌ Found ${duplicateDomains.length} domains with duplicate files`);
      console.log('💡 Consider investigating why duplicates are being created');
      
      console.log('\n🔧 Quick fix commands:');
      duplicateDomains.forEach(domain => {
        const sortedFiles = domain.files.sort();
        const filesToRemove = sortedFiles.slice(0, -1); // Remove all but the last (newest)
        
        filesToRemove.forEach(file => {
          const filePath = `data/scraped-content/${domain.domain}/${file}`;
          console.log(`   rm "${filePath}"`);
        });
      });
    } else {
      console.log('✅ No duplicate files found - system is working correctly');
    }

    console.log(`\n📈 Total domains: ${domains.length}`);
    console.log(`📄 Total files: ${domains.reduce((sum, d) => sum + d.count, 0)}`);

  } catch (error) {
    console.error('💥 Monitoring failed:', error);
  }
}

// Run the monitor
monitorDuplicates().catch(console.error);
