import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Mock job types
enum JobType {
  WEB_SCRAPING = 'web_scraping',
  MEDIA_COLLECTION = 'media_collection',
  CONTENT_GENERATION = 'content_generation',
  TOOL_PROCESSING = 'tool_processing',
  BULK_PROCESSING = 'bulk_processing',
}

enum JobStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

interface Job {
  id: string;
  type: JobType;
  toolId?: string;
  status: JobStatus;
}

// Mock the enhanced queue's shouldProcessJobSequentially method
async function testShouldProcessJobSequentially(job: Job): Promise<boolean> {
  // ALL pipeline jobs require sequential processing due to dependencies:
  // 1. Web scraping must complete first to gather raw data
  // 2. Media collection depends on scraped content to extract images/assets
  // 3. AI content generation depends on both scraped content and media
  const pipelineJobTypes = [
    JobType.WEB_SCRAPING,     // Stage 1: Must complete before media collection
    JobType.MEDIA_COLLECTION, // Stage 2: Depends on web scraping results
    JobType.CONTENT_GENERATION, // Stage 3: Depends on both web scraping and media collection
    JobType.TOOL_PROCESSING,  // Bulk processing jobs need sequential execution
    JobType.BULK_PROCESSING,  // Bulk processing coordination
  ];

  // Check if job type requires sequential processing
  if (pipelineJobTypes.includes(job.type)) {
    console.log(`🔄 Job ${job.id} (${job.type}) requires sequential processing due to pipeline dependencies`);
    return true;
  }

  // For any job with a toolId, check if there are active jobs for the same tool
  // This prevents concurrent processing of the same tool across different job types
  if (job.toolId) {
    const { data: activeJobs, error } = await supabase
      .from('ai_generation_jobs')
      .select('id, tool_id, job_type, status')
      .eq('tool_id', job.toolId)
      .eq('status', JobStatus.PROCESSING)
      .neq('id', job.id); // Exclude current job

    if (error) {
      console.warn(`⚠️ Could not check for active jobs for tool ${job.toolId}:`, error);
      // Default to sequential processing if we can't determine
      console.log(`🔄 Job ${job.id} defaulting to sequential processing due to database error`);
      return true;
    }

    if (activeJobs && activeJobs.length > 0) {
      console.log(`🔄 Job ${job.id} must wait - tool ${job.toolId} is being processed by ${activeJobs.length} other job(s)`);
      activeJobs.forEach(activeJob => {
        console.log(`  Active job: ${activeJob.id} (${activeJob.job_type})`);
      });
      return true;
    }

    // Additional check: Ensure prerequisite jobs are completed for pipeline stages
    if (job.type === JobType.MEDIA_COLLECTION) {
      // Media collection requires web scraping to be completed
      const { data: webScrapingJobs, error: wsError } = await supabase
        .from('ai_generation_jobs')
        .select('id, job_type, status')
        .eq('tool_id', job.toolId)
        .eq('job_type', JobType.WEB_SCRAPING);

      if (wsError) {
        console.warn(`⚠️ Could not check web scraping prerequisite for job ${job.id}:`, wsError);
        return true;
      }

      const completedWebScraping = webScrapingJobs?.some(wsJob => wsJob.status === JobStatus.COMPLETED);
      if (!completedWebScraping) {
        console.log(`🔄 Job ${job.id} (media_collection) must wait - web scraping not completed for tool ${job.toolId}`);
        return true;
      }
    }

    if (job.type === JobType.CONTENT_GENERATION) {
      // Content generation requires both web scraping and media collection to be completed
      const { data: prerequisiteJobs, error: prereqError } = await supabase
        .from('ai_generation_jobs')
        .select('id, job_type, status')
        .eq('tool_id', job.toolId)
        .in('job_type', [JobType.WEB_SCRAPING, JobType.MEDIA_COLLECTION]);

      if (prereqError) {
        console.warn(`⚠️ Could not check prerequisites for job ${job.id}:`, prereqError);
        return true;
      }

      const completedWebScraping = prerequisiteJobs?.some(job =>
        job.job_type === JobType.WEB_SCRAPING && job.status === JobStatus.COMPLETED
      );
      const completedMediaCollection = prerequisiteJobs?.some(job =>
        job.job_type === JobType.MEDIA_COLLECTION && job.status === JobStatus.COMPLETED
      );

      if (!completedWebScraping || !completedMediaCollection) {
        console.log(`🔄 Job ${job.id} (content_generation) must wait - prerequisites not completed for tool ${job.toolId}`);
        console.log(`  Web scraping completed: ${completedWebScraping}`);
        console.log(`  Media collection completed: ${completedMediaCollection}`);
        return true;
      }
    }
  }

  // Default to parallel processing only for non-pipeline jobs without toolId conflicts
  console.log(`⚡ Job ${job.id} (${job.type}) can be processed in parallel`);
  return false;
}

async function runDependencyTests() {
  console.log('🧪 Testing Job Dependency Management...\n');

  // Test 1: Tool processing job should be sequential
  console.log('Test 1: Tool processing job should be sequential');
  const job1: Job = {
    id: 'test-job-1',
    type: JobType.TOOL_PROCESSING,
    toolId: '128c4bc9-605b-4a32-a340-97a842003f7c',
    status: JobStatus.PENDING
  };
  const result1 = await testShouldProcessJobSequentially(job1);
  console.log(`Result: ${result1 ? 'PASS' : 'FAIL'} - Expected: true, Got: ${result1}\n`);

  // Test 2: Bulk processing job should be sequential
  console.log('Test 2: Bulk processing job should be sequential');
  const job2: Job = {
    id: 'test-job-2',
    type: JobType.BULK_PROCESSING,
    status: JobStatus.PENDING
  };
  const result2 = await testShouldProcessJobSequentially(job2);
  console.log(`Result: ${result2 ? 'PASS' : 'FAIL'} - Expected: true, Got: ${result2}\n`);

  // Test 3: Web scraping job should be sequential (pipeline dependency)
  console.log('Test 3: Web scraping job should be sequential (pipeline dependency)');
  const job3: Job = {
    id: '550e8400-e29b-41d4-a716-************',
    type: JobType.WEB_SCRAPING,
    toolId: '550e8400-e29b-41d4-a716-************', // Non-existent but valid UUID
    status: JobStatus.PENDING
  };
  const result3 = await testShouldProcessJobSequentially(job3);
  console.log(`Result: ${result3 ? 'PASS' : 'FAIL'} - Expected: true, Got: ${result3}\n`);

  // Test 4: Content generation job should be sequential (pipeline dependency)
  console.log('Test 4: Content generation job should be sequential (pipeline dependency)');
  const job4: Job = {
    id: '550e8400-e29b-41d4-a716-************',
    type: JobType.CONTENT_GENERATION,
    toolId: '550e8400-e29b-41d4-a716-************', // Non-existent but valid UUID
    status: JobStatus.PENDING
  };
  const result4 = await testShouldProcessJobSequentially(job4);
  console.log(`Result: ${result4 ? 'PASS' : 'FAIL'} - Expected: true, Got: ${result4}\n`);

  // Test 5: Media collection job should be sequential (pipeline dependency)
  console.log('Test 5: Media collection job should be sequential (pipeline dependency)');
  const job5: Job = {
    id: '550e8400-e29b-41d4-a716-************',
    type: JobType.MEDIA_COLLECTION,
    status: JobStatus.PENDING
  };
  const result5 = await testShouldProcessJobSequentially(job5);
  console.log(`Result: ${result5 ? 'PASS' : 'FAIL'} - Expected: true, Got: ${result5}\n`);

  console.log('🎯 Summary:');
  const tests = [
    { name: 'Tool processing job should be sequential', result: result1, expected: true },
    { name: 'Bulk processing job should be sequential', result: result2, expected: true },
    { name: 'Web scraping job should be sequential', result: result3, expected: true },
    { name: 'Content generation job should be sequential', result: result4, expected: true },
    { name: 'Media collection job should be sequential', result: result5, expected: true },
  ];

  let passed = 0;
  tests.forEach((test, i) => {
    const success = test.result === test.expected;
    console.log(`  Test ${i + 1}: ${success ? '✅ PASS' : '❌ FAIL'} - ${test.name}`);
    if (success) passed++;
  });

  console.log(`\n📊 Results: ${passed}/${tests.length} tests passed`);
  
  if (passed === tests.length) {
    console.log('🎉 All job dependency management fixes are working correctly!');
  } else {
    console.log('⚠️ Some tests failed - job dependency management may need adjustment');
  }
}

runDependencyTests().catch(console.error);
