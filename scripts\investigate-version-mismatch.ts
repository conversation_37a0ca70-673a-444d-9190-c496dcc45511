#!/usr/bin/env tsx

/**
 * Investigate Version Mismatch Issues
 * 
 * This script investigates the persistent version_mismatch errors
 * in the bulk processing atomic functions to identify the root cause.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function investigateVersionMismatch() {
  console.log('🔍 Investigating Version Mismatch Issues...');
  console.log('=' .repeat(60));

  try {
    // Step 1: Check function signatures
    console.log('1️⃣ Checking atomic function signatures...');
    
    // Use a more reliable query for function parameters
    const { data: functionParams, error: paramsError } = await supabase.rpc('sql', {
      query: `
        SELECT
          p.specific_name,
          p.parameter_name,
          p.data_type,
          p.ordinal_position
        FROM information_schema.parameters p
        WHERE p.specific_schema = 'public'
        AND p.specific_name LIKE '%bulk%'
        ORDER BY p.specific_name, p.ordinal_position
      `
    }).then(result => {
      if (result.error) {
        // Fallback to direct table access
        return supabase
          .from('information_schema.parameters')
          .select('specific_name, parameter_name, data_type, ordinal_position')
          .eq('specific_schema', 'public')
          .like('specific_name', '%bulk%')
          .order('specific_name')
          .order('ordinal_position');
      }
      return result;
    });

    if (paramsError) {
      console.log('❌ Could not check function parameters:', paramsError.message);
    } else if (functionParams) {
      console.log('   Function parameters found:');
      
      const functionGroups = functionParams.reduce((groups, param) => {
        if (!groups[param.specific_name]) {
          groups[param.specific_name] = [];
        }
        groups[param.specific_name].push(param);
        return groups;
      }, {} as Record<string, any[]>);

      Object.entries(functionGroups).forEach(([funcName, params]) => {
        console.log(`   📋 ${funcName}:`);
        params.forEach(param => {
          const status = param.data_type === 'uuid' && param.parameter_name === 'p_job_id' ? '✅' : 
                        param.parameter_name === 'p_job_id' ? '❌' : '  ';
          console.log(`     ${status} ${param.parameter_name}: ${param.data_type}`);
        });
      });
    }

    // Step 2: Create a test bulk job to investigate version behavior
    console.log('\n2️⃣ Creating test bulk job to investigate versioning...');
    
    const testBulkJobId = generateUUID();
    const testBulkJob = {
      id: testBulkJobId,
      job_type: 'manual_entry',
      status: 'pending',
      total_items: 1,
      processed_items: 0,
      successful_items: 0,
      failed_items: 0,
      version: 1, // Start with version 1
      source_data: [{ url: 'https://example.com/test' }],
      processing_options: { test: true },
      created_by: 'version-mismatch-investigation',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: insertError } = await supabase
      .from('bulk_processing_jobs')
      .insert(testBulkJob);

    if (insertError) {
      console.log('❌ Failed to create test bulk job:', insertError.message);
      return;
    }

    console.log(`✅ Created test bulk job: ${testBulkJobId}`);

    // Step 3: Test atomic function calls
    console.log('\n3️⃣ Testing atomic function calls...');
    
    const atomicTests = [
      {
        name: 'update_bulk_job_status_atomic',
        params: { 
          p_job_id: testBulkJobId, 
          p_new_status: 'processing',
          p_expected_version: 1 // Should match current version
        }
      },
      {
        name: 'update_bulk_job_progress_atomic',
        params: { 
          p_job_id: testBulkJobId, 
          p_processed_items: 1, 
          p_successful_items: 1, 
          p_failed_items: 0,
          p_expected_version: 2 // Should be incremented after status update
        }
      }
    ];

    for (const test of atomicTests) {
      console.log(`   🧪 Testing ${test.name}...`);
      
      try {
        const { data: result, error } = await supabase.rpc(test.name, test.params);
        
        if (error) {
          console.log(`     ❌ Error: ${error.message}`);
        } else if (result) {
          console.log(`     📊 Result:`, result);
          
          if (result.success) {
            console.log(`     ✅ Function call successful`);
            console.log(`     📈 New version: ${result.new_version}`);
          } else {
            console.log(`     ❌ Function returned error: ${result.error}`);
            if (result.error === 'version_mismatch') {
              console.log(`     🔍 Expected version: ${test.params.p_expected_version}`);
              console.log(`     🔍 Current version: ${result.current_version || 'unknown'}`);
            }
          }
        }
      } catch (funcError) {
        console.log(`     ❌ Exception: ${funcError}`);
      }
    }

    // Step 4: Check current job state
    console.log('\n4️⃣ Checking current bulk job state...');
    
    const { data: currentJob, error: fetchError } = await supabase
      .from('bulk_processing_jobs')
      .select('*')
      .eq('id', testBulkJobId)
      .single();

    if (fetchError) {
      console.log('❌ Could not fetch current job state:', fetchError.message);
    } else if (currentJob) {
      console.log('   📊 Current job state:');
      console.log(`     ID: ${currentJob.id}`);
      console.log(`     Status: ${currentJob.status}`);
      console.log(`     Version: ${currentJob.version}`);
      console.log(`     Processed Items: ${currentJob.processed_items}`);
      console.log(`     Successful Items: ${currentJob.successful_items}`);
      console.log(`     Failed Items: ${currentJob.failed_items}`);
      console.log(`     Updated At: ${currentJob.updated_at}`);
    }

    // Step 5: Test version mismatch scenario
    console.log('\n5️⃣ Testing intentional version mismatch...');
    
    const wrongVersionTest = {
      name: 'update_bulk_job_status_atomic',
      params: { 
        p_job_id: testBulkJobId, 
        p_new_status: 'completed',
        p_expected_version: 999 // Intentionally wrong version
      }
    };

    try {
      const { data: result, error } = await supabase.rpc(wrongVersionTest.name, wrongVersionTest.params);
      
      if (error) {
        console.log(`   ❌ Error (expected): ${error.message}`);
      } else if (result) {
        console.log(`   📊 Result:`, result);
        
        if (result.error === 'version_mismatch') {
          console.log(`   ✅ Version mismatch correctly detected`);
          console.log(`   🔍 Expected: ${wrongVersionTest.params.p_expected_version}`);
          console.log(`   🔍 Current: ${result.current_version}`);
        } else {
          console.log(`   ⚠️  Unexpected result - version mismatch not detected`);
        }
      }
    } catch (funcError) {
      console.log(`   ❌ Exception: ${funcError}`);
    }

    // Cleanup
    console.log('\n6️⃣ Cleaning up test data...');
    
    await supabase
      .from('bulk_processing_jobs')
      .delete()
      .eq('id', testBulkJobId);
    
    console.log('✅ Test bulk job cleaned up');

    console.log('\n📊 Investigation Summary:');
    console.log('=' .repeat(60));
    console.log('✅ Function signature verification completed');
    console.log('✅ Atomic function behavior tested');
    console.log('✅ Version mismatch detection verified');
    console.log('');
    console.log('🔍 Key findings:');
    console.log('   • Check if functions are using correct UUID parameter types');
    console.log('   • Verify version tracking is working correctly');
    console.log('   • Confirm atomic operations are properly implemented');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('   • Review function call patterns in bulk processing code');
    console.log('   • Ensure version numbers are being tracked correctly');
    console.log('   • Test with real bulk processing workflow');

  } catch (error) {
    console.error('❌ Investigation failed:', error);
    process.exit(1);
  }
}

// Run the investigation
if (require.main === module) {
  investigateVersionMismatch().catch(console.error);
}

export { investigateVersionMismatch };
