#!/usr/bin/env tsx

/**
 * Diagnose Tool Database Issue
 * 
 * This script investigates the specific database issue with tool ID
 * cf89c34b-51f3-4938-8494-8d03df3cca16 that's causing submission source detection to fail.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function diagnoseToolDatabaseIssue() {
  console.log('🔍 DIAGNOSING TOOL DATABASE ISSUE\n');
  console.log('=' .repeat(70) + '\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    const problemToolId = 'cf89c34b-51f3-4938-8494-8d03df3cca16';

    console.log(`🎯 Investigating tool ID: ${problemToolId}`);
    console.log('');

    // 1. Check if tool exists at all
    console.log('1. 🔍 CHECKING TOOL EXISTENCE:');
    const { data: allMatches, error: allError } = await supabase
      .from('tools')
      .select('*')
      .eq('id', problemToolId);

    if (allError) {
      console.log(`❌ Error querying tools table: ${allError.message}`);
      return;
    }

    console.log(`   📊 Found ${allMatches?.length || 0} row(s) with this ID`);

    if (!allMatches || allMatches.length === 0) {
      console.log('   ❌ ISSUE IDENTIFIED: Tool ID does not exist in database');
      console.log('   💡 This explains the "no rows returned" error');
      
      // Check if there are similar tools
      console.log('\n2. 🔍 SEARCHING FOR SIMILAR TOOLS:');
      const { data: foodieTools, error: foodieError } = await supabase
        .from('tools')
        .select('*')
        .or('name.ilike.%foodieprep%,website.ilike.%foodieprep%')
        .order('created_at', { ascending: false });

      if (foodieError) {
        console.log(`❌ Error searching for FoodiePrep tools: ${foodieError.message}`);
      } else if (foodieTools && foodieTools.length > 0) {
        console.log(`   ✅ Found ${foodieTools.length} FoodiePrep tool(s):`);
        foodieTools.forEach((tool, index) => {
          console.log(`      Tool ${index + 1}: ${tool.id}`);
          console.log(`         Name: ${tool.name}`);
          console.log(`         Website: ${tool.website}`);
          console.log(`         Submission Source: ${tool.submission_source}`);
          console.log(`         Created: ${tool.created_at}`);
          console.log('');
        });
      }
      return;
    }

    if (allMatches.length > 1) {
      console.log('   ❌ ISSUE IDENTIFIED: Multiple rows found with same ID');
      console.log('   💡 This explains the "multiple rows returned" error');
      console.log('   🚨 This indicates a serious database integrity issue!');
      console.log('');
      console.log('   📋 Duplicate tools found:');
      allMatches.forEach((tool, index) => {
        console.log(`      Duplicate ${index + 1}:`);
        console.log(`         ID: ${tool.id}`);
        console.log(`         Name: ${tool.name}`);
        console.log(`         Website: ${tool.website}`);
        console.log(`         Submission Source: ${tool.submission_source}`);
        console.log(`         Created: ${tool.created_at}`);
        console.log('');
      });
      return;
    }

    // Single tool found - analyze it
    const tool = allMatches[0];
    console.log('   ✅ Single tool found (as expected)');
    console.log('');

    console.log('2. 📋 TOOL DETAILS ANALYSIS:');
    console.log(`   ID: ${tool.id}`);
    console.log(`   Name: ${tool.name}`);
    console.log(`   Website: ${tool.website}`);
    console.log(`   Submission Type: ${tool.submission_type}`);
    console.log(`   Submission Source: ${tool.submission_source}`);
    console.log(`   Content Status: ${tool.content_status}`);
    console.log(`   AI Status: ${tool.ai_generation_status}`);
    console.log(`   Created: ${tool.created_at}`);
    console.log(`   Updated: ${tool.updated_at}`);
    console.log('');

    // 3. Test the exact query that's failing
    console.log('3. 🧪 TESTING EXACT FAILING QUERY:');
    console.log('   Simulating: .select().eq().single()');
    
    try {
      const { data: singleTool, error: singleError } = await supabase
        .from('tools')
        .select('submission_source, submission_type, name, website')
        .eq('id', problemToolId)
        .single();

      if (singleError) {
        console.log(`   ❌ Query failed: ${singleError.message}`);
        console.log(`   🔍 Error code: ${singleError.code}`);
        console.log(`   📋 Error details: ${JSON.stringify(singleError.details)}`);
      } else {
        console.log('   ✅ Query succeeded!');
        console.log(`   📋 Result: ${JSON.stringify(singleTool, null, 2)}`);
      }
    } catch (queryError: any) {
      console.log(`   💥 Query exception: ${queryError.message}`);
    }

    // 4. Check for database constraints and indexes
    console.log('\n4. 🔍 CHECKING DATABASE INTEGRITY:');
    
    // Check for duplicate IDs in the entire table
    const { data: duplicateCheck, error: dupError } = await supabase
      .from('tools')
      .select('id')
      .eq('id', problemToolId);

    if (dupError) {
      console.log(`   ❌ Error checking duplicates: ${dupError.message}`);
    } else {
      console.log(`   📊 Total rows with this ID: ${duplicateCheck?.length || 0}`);
      if (duplicateCheck && duplicateCheck.length > 1) {
        console.log('   🚨 CRITICAL: Multiple rows with same primary key!');
        console.log('   💡 This violates database constraints and needs immediate fix');
      }
    }

    // 5. Check recent AI generation jobs for this tool
    console.log('\n5. 🔍 CHECKING RELATED AI JOBS:');
    const { data: jobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .eq('tool_id', problemToolId)
      .order('created_at', { ascending: false });

    if (jobsError) {
      console.log(`   ❌ Error fetching jobs: ${jobsError.message}`);
    } else if (jobs && jobs.length > 0) {
      console.log(`   ✅ Found ${jobs.length} related job(s):`);
      jobs.forEach((job, index) => {
        console.log(`      Job ${index + 1}: ${job.id}`);
        console.log(`         Type: ${job.job_type}`);
        console.log(`         Status: ${job.status}`);
        console.log(`         Created: ${job.created_at}`);
      });
    } else {
      console.log('   📋 No related AI generation jobs found');
    }

    // 6. Provide recommendations
    console.log('\n6. 💡 RECOMMENDATIONS:');
    
    if (tool.submission_source !== 'bulk_processing') {
      console.log('   🔧 ISSUE: Tool has incorrect submission_source');
      console.log(`      Current: ${tool.submission_source}`);
      console.log('      Expected: bulk_processing');
      console.log('      Action: Update submission_source field');
    } else {
      console.log('   ✅ Tool has correct submission_source: bulk_processing');
    }

    if (tool.submission_type !== 'admin') {
      console.log('   🔧 ISSUE: Tool has incorrect submission_type');
      console.log(`      Current: ${tool.submission_type}`);
      console.log('      Expected: admin');
      console.log('      Action: Update submission_type field');
    } else {
      console.log('   ✅ Tool has correct submission_type: admin');
    }

  } catch (error) {
    console.error('💥 Diagnosis failed:', error);
  }
}

// Run the diagnosis
diagnoseToolDatabaseIssue().catch(console.error);
