#!/usr/bin/env tsx

/**
 * Run Migration 008: Fix Job Constraints
 * 
 * This script executes Migration 008 to fix job_type and status constraints
 * for the ai_generation_jobs table to support all TypeScript enum values.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
config({ path: '.env.local' });
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration008() {
  console.log('🚀 Running Migration 008: Fix Job Constraints...');
  console.log('=' .repeat(70));

  try {
    // Step 1: Load migration SQL
    console.log('1️⃣ Loading migration SQL...');
    
    const migrationPath = join(process.cwd(), 'src/lib/database/migrations/008_fix_job_constraints.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    console.log('✅ Migration SQL loaded successfully');
    console.log(`   File: ${migrationPath}`);
    console.log(`   Size: ${migrationSQL.length} characters`);

    // Step 2: Display migration for manual execution
    console.log('\n2️⃣ Migration SQL for Manual Execution:');
    console.log('=' .repeat(70));
    console.log('⚠️  COPY AND PASTE THE FOLLOWING SQL INTO SUPABASE SQL EDITOR:');
    console.log('');
    console.log('--- MIGRATION 008 SQL START ---');
    console.log(migrationSQL);
    console.log('--- MIGRATION 008 SQL END ---');
    console.log('');

    // Step 3: Wait for user confirmation
    console.log('3️⃣ Waiting for manual execution...');
    console.log('📋 Instructions:');
    console.log('   1. Copy the SQL above');
    console.log('   2. Open Supabase Dashboard → SQL Editor');
    console.log('   3. Paste and execute the SQL');
    console.log('   4. Return here and press Enter to verify');
    console.log('');
    console.log('Press Enter after executing the SQL in Supabase...');
    
    // Wait for user input
    await new Promise<void>((resolve) => {
      process.stdin.once('data', () => resolve());
    });

    // Step 4: Verify migration was applied
    console.log('\n4️⃣ Verifying migration was applied...');
    
    // Check job_type constraint
    console.log('   Checking job_type constraint...');
    const { data: jobTypeConstraints, error: jobTypeError } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            conname as constraint_name,
            pg_get_constraintdef(oid) as constraint_definition
          FROM pg_constraint 
          WHERE conrelid = 'ai_generation_jobs'::regclass 
          AND conname LIKE '%job_type%'
          ORDER BY conname
        `
      });

    if (jobTypeError) {
      console.log(`   ❌ Could not check job_type constraint: ${jobTypeError.message}`);
    } else if (jobTypeConstraints && jobTypeConstraints.length > 0) {
      console.log('   ✅ Job type constraint found:');
      jobTypeConstraints.forEach(constraint => {
        console.log(`      ${constraint.constraint_name}: ${constraint.constraint_definition}`);
      });
    } else {
      console.log('   ❌ No job_type constraint found');
    }

    // Check status constraint
    console.log('   Checking status constraint...');
    const { data: statusConstraints, error: statusError } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            conname as constraint_name,
            pg_get_constraintdef(oid) as constraint_definition
          FROM pg_constraint 
          WHERE conrelid = 'ai_generation_jobs'::regclass 
          AND conname LIKE '%status%'
          ORDER BY conname
        `
      });

    if (statusError) {
      console.log(`   ❌ Could not check status constraint: ${statusError.message}`);
    } else if (statusConstraints && statusConstraints.length > 0) {
      console.log('   ✅ Status constraint found:');
      statusConstraints.forEach(constraint => {
        console.log(`      ${constraint.constraint_name}: ${constraint.constraint_definition}`);
      });
    } else {
      console.log('   ❌ No status constraint found');
    }

    // Step 5: Test the fixes
    console.log('\n5️⃣ Testing the constraint fixes...');
    
    // Test web_scraping job type
    console.log('   Testing web_scraping job type...');
    const testJobId = `test-${Date.now()}`;
    
    try {
      const { error: webScrapingError } = await supabase
        .from('ai_generation_jobs')
        .insert({
          id: testJobId,
          job_type: 'web_scraping',
          status: 'pending',
          progress: 0,
          attempts: 0,
          max_attempts: 3,
          priority: 1
        });

      if (webScrapingError) {
        console.log(`   ❌ web_scraping test failed: ${webScrapingError.message}`);
      } else {
        console.log('   ✅ web_scraping job type works!');
        
        // Test retrying status
        console.log('   Testing retrying status...');
        const { error: retryingError } = await supabase
          .from('ai_generation_jobs')
          .update({ status: 'retrying' })
          .eq('id', testJobId);

        if (retryingError) {
          console.log(`   ❌ retrying status test failed: ${retryingError.message}`);
        } else {
          console.log('   ✅ retrying status works!');
        }

        // Clean up test job
        await supabase.from('ai_generation_jobs').delete().eq('id', testJobId);
        console.log('   ✅ Test job cleaned up');
      }
    } catch (error) {
      console.log(`   ❌ Test failed with error: ${error}`);
    }

    // Step 6: Summary
    console.log('\n📊 Migration 008 Summary:');
    console.log('=' .repeat(70));
    
    const hasJobTypeConstraint = jobTypeConstraints && jobTypeConstraints.length > 0;
    const hasStatusConstraint = statusConstraints && statusConstraints.length > 0;
    
    if (hasJobTypeConstraint && hasStatusConstraint) {
      console.log('✅ Migration 008 completed successfully!');
      console.log('   • Job type constraint updated with all TypeScript enum values');
      console.log('   • Status constraint updated with all TypeScript enum values');
      console.log('   • web_scraping job type now supported');
      console.log('   • retrying status now supported');
      console.log('   • Enhanced job queue system fully operational');
      console.log('');
      console.log('🎉 Critical constraint violations should now be resolved!');
    } else {
      console.log('❌ Migration 008 may not have been applied correctly');
      console.log('   Please check the SQL execution in Supabase and try again');
      console.log('');
      console.log('📋 Troubleshooting:');
      console.log('   1. Ensure the SQL was executed without errors in Supabase');
      console.log('   2. Check Supabase logs for any constraint creation failures');
      console.log('   3. Verify you have sufficient permissions to modify constraints');
    }

  } catch (error) {
    console.error('❌ Migration 008 failed:', error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  runMigration008().catch(console.error);
}

export { runMigration008 };
