/**
 * Content Processor for Enhanced Scrape.do Integration
 * Main orchestrator for scraping, media collection, and content processing
 */

import {
  EnhancedScrapeRequest,
  EnhancedScrapeResult,
  ScrapeResult,
  ImageCollection,
  MultiPageScrapingConfig
} from './types';
import { scrapeDoClient } from './scrape-do-client';
import { costOptimizer } from './cost-optimizer';
import { ContentAnalyzer } from './content-analyzer';
import { mediaExtractor } from './media-extractor';
import { multiPageScraper } from './multi-page-scraper';
import { dataStorage } from './data-storage';
import { supabaseAdmin } from '@/lib/supabase';
import { getFallbackManager } from './fallback-manager';

export class ContentProcessor {
  private contentAnalyzer: ContentAnalyzer;

  constructor() {
    this.contentAnalyzer = new ContentAnalyzer();
  }

  /**
   * Main enhanced scraping method that orchestrates all components
   */
  async processEnhancedScrape(request: EnhancedScrapeRequest): Promise<EnhancedScrapeResult> {
    const startTime = Date.now();
    console.log(`🚀 Starting enhanced scraping for: ${request.url}`);

    // Handle timeout if specified
    const timeout = request.options?.timeout;
    if (timeout) {
      return Promise.race([
        this.executeEnhancedScrape(request, startTime),
        new Promise<EnhancedScrapeResult>((_, reject) =>
          setTimeout(() => reject(new Error(`Request timeout after ${timeout}ms`)), timeout)
        )
      ]).catch(error => {
        if (error.message.includes('timeout')) {
          return this.createFailedResult(request.url, `Request timeout after ${timeout}ms`);
        }
        throw error;
      });
    }

    return this.executeEnhancedScrape(request, startTime);
  }

  /**
   * Execute the enhanced scraping workflow
   */
  private async executeEnhancedScrape(request: EnhancedScrapeRequest, startTime: number): Promise<EnhancedScrapeResult> {
    try {
      // Step 1: Main page scraping with cost optimization
      const mainResult = await this.scrapeMainPage(request);
      
      if (!mainResult.success) {
        return this.createFailedResult(request.url, mainResult.error || 'Main page scraping failed');
      }

      // Step 2: Content analysis
      const contentAnalysis = this.contentAnalyzer.analyzeContentQuality(mainResult.content, request.url);

      // Step 3: Media collection REMOVED - now handled by separate media collection job
      // Enhanced scraping now focuses only on text content extraction
      let mediaAssets: ImageCollection | undefined;

      // Only extract basic media from already-scraped content (no additional API calls)
      if (request.mediaCollection !== false) {
        console.log('📸 MEDIA-OPTIMIZED: Extracting basic media from scraped content (no additional API calls)');
        mediaAssets = await this.extractMediaFromContent(request.url, mainResult.content);
      }

      // Step 4: Multi-page scraping (enabled by default unless explicitly disabled)
      let additionalPages: ScrapeResult[] = [];
      if (request.multiPageConfig?.enabled !== false) {
        const config = request.multiPageConfig || {
          enabled: true,
          mode: 'conditional' as const,
          maxPagesPerTool: 3,
          creditThreshold: 10,
          pageTypes: {
            pricing: { enabled: true, priority: 'high' as const, patterns: ['/pricing', '/plans'], selectors: [], required: false },
            faq: { enabled: true, priority: 'medium' as const, patterns: ['/faq', '/help'], selectors: [], required: false },
            features: { enabled: true, priority: 'medium' as const, patterns: ['/features'], selectors: [], required: false },
            about: { enabled: true, priority: 'low' as const, patterns: ['/about'], selectors: [], required: false }
          },
          fallbackStrategy: { searchInMainPage: true, useNavigation: true, useSitemap: false }
        };
        additionalPages = await this.processMultiPageScraping(request.url, mainResult.content, config);
      }

      // Step 5: Content validation
      const validation = this.contentAnalyzer.validateContentQuality(mainResult.content, request.url);
      
      // Step 6: Calculate cost analysis
      const costAnalysis = this.calculateCostAnalysis(mainResult, additionalPages);

      const processingTime = Date.now() - startTime;
      console.log(`✅ Enhanced scraping completed in ${processingTime}ms`);

      const result: EnhancedScrapeResult = {
        ...mainResult,
        mediaAssets,
        additionalPages,
        costAnalysis,
        contentAnalysis,
        metadata: {
          ...mainResult.metadata,
          processingTime,
          validation: validation.isValid,
          qualityScore: validation.qualityScore
        }
      };

      // Step 7: Store scraped content for future AI processing (if enabled)
      if (request.persistentStorage !== false) {
        try {
          // Store in database (fail silently - no retries to avoid wasting credits)
          try {
            await this.storeInDatabase(result);
            console.log('✅ Database storage successful');
          } catch (dbError) {
            console.warn('⚠️ Database storage failed (non-critical, no retry):', dbError instanceof Error ? dbError.message : dbError);
            // Don't retry database storage - if it fails once, it will likely fail again
            // File storage below will ensure content is preserved
          }

          // Store in organized file system using data storage service
          const storageResult = await dataStorage.storeScrapedContent(result);
          if (storageResult.success) {
            console.log(`📁 Content stored: ${storageResult.filePath}`);
          } else {
            console.warn(`⚠️ File storage failed: ${storageResult.error}`);
          }
        } catch (error) {
          console.warn('Storage error (non-critical):', error instanceof Error ? error.message : error);
          // Don't throw - storage failures shouldn't break the scraping pipeline
          // The scraped content is still available in the result object for AI processing
        }
      }

      return result;

    } catch (error) {
      console.error('Enhanced scraping failed:', error);
      return this.createFailedResult(request.url, (error as Error).message);
    }
  }

  /**
   * Scrape main page with intelligent cost optimization and fallback strategy
   */
  private async scrapeMainPage(request: EnhancedScrapeRequest): Promise<ScrapeResult> {
    if (request.costOptimization !== false) {
      // Use cost optimizer for intelligent scraping - it handles all cost decisions
      const basicResult = await costOptimizer.scrapeWithMaxCostOptimization(request.url);

      // The cost optimizer has already made the optimal decision
      // If it returned basic content, it means that's the best cost/quality balance
      if (basicResult.success) {
        // Check if cost optimizer already decided to use basic content
        const optimizationStrategy = basicResult.metadata?.optimizationStrategy || '';
        const isBasicOptimized = optimizationStrategy.includes('COST-SAVE') ||
                                optimizationStrategy.includes('COST-OPTIMIZED') ||
                                (basicResult.metadata?.creditsUsed || 0) <= 1;

        if (isBasicOptimized) {
          console.log('💰 COST-OPTIMIZER: Using basic content as determined by cost analysis');
          return basicResult;
        }

        // If cost optimizer returned enhanced content, use it directly
        if ((basicResult.metadata?.creditsUsed || 0) >= 5) {
          console.log('⚡ COST-OPTIMIZER: Using enhanced content as determined by cost analysis');
          return basicResult;
        }

        // For edge cases, check content sufficiency as fallback
        if (this.isContentSufficient(basicResult)) {
          console.log('💰 CONTENT-CHECK: Basic content is sufficient, skipping enhanced scraping');
          return basicResult;
        }
      }

      // Only use fallback manager if cost optimizer failed or returned insufficient content
      console.log('🔄 FALLBACK: Cost optimizer result insufficient, attempting enhanced scraping');
      const fallbackManager = getFallbackManager();
      const fallbackResult = await fallbackManager.executeWithFallback(request, basicResult);

      if (fallbackResult.success && fallbackResult.result) {
        // Convert enhanced result back to ScrapeResult format
        return {
          success: true,
          content: fallbackResult.result.content,
          url: fallbackResult.result.url,
          timestamp: fallbackResult.result.timestamp,
          metadata: {
            ...fallbackResult.result.metadata,
            creditsUsed: fallbackResult.result.costAnalysis?.creditsUsed || 1,
            optimizationStrategy: fallbackResult.result.costAnalysis?.optimizationStrategy || 'Unknown',
            fallbackStrategy: fallbackResult.strategy
          }
        };
      } else {
        // Fallback failed, return basic result if available
        if (basicResult.success) {
          console.log('⚠️ Enhanced scraping failed, using basic result as final fallback');
          return basicResult;
        } else {
          // Both failed
          return {
            success: false,
            content: '',
            url: request.url,
            timestamp: new Date().toISOString(),
            error: fallbackResult.reasoning || 'Both enhanced and basic scraping failed',
            metadata: { creditsUsed: 0 }
          };
        }
      }
    } else {
      // Direct scraping with provided options (no cost optimization)
      return await scrapeDoClient.scrapePage(request.url, request.options);
    }
  }

  /**
   * Check if content is sufficient for AI processing
   */
  private isContentSufficient(result: ScrapeResult): boolean {
    if (!result.success || !result.content) {
      return false;
    }

    const contentLength = result.content.length;

    // For large content (like PhotoAI.com with 125k chars), it's definitely sufficient
    if (contentLength > 10000) {
      console.log(`✅ CONTENT-SUFFICIENT: Large content (${contentLength} chars) is definitely sufficient`);
      return true;
    }

    // For medium content, check quality
    if (contentLength > 1000) {
      const wordCount = result.content.split(/\s+/).length;
      const hasStructure = /<h[1-6]|^#{1,6}\s/m.test(result.content);

      if (wordCount > 100 && hasStructure) {
        console.log(`✅ CONTENT-SUFFICIENT: Medium content (${contentLength} chars, ${wordCount} words) with structure`);
        return true;
      }
    }

    // For small content, use strict criteria
    const minLength = 300;
    if (contentLength < minLength) {
      return false;
    }

    // Check for substantial content (not just metadata)
    const substantialContentRegex = /[a-zA-Z]{50,}/g;
    const substantialMatches = result.content.match(substantialContentRegex);

    return substantialMatches && substantialMatches.length >= 3;
  }

  /**
   * DEPRECATED: Media collection moved to separate background job
   * This method is kept for backward compatibility but should not be used
   */
  private async collectMediaAssets(url: string, content: string): Promise<ImageCollection | undefined> {
    console.warn('⚠️ collectMediaAssets is deprecated - use separate MediaCollectionJob instead');

    // Return basic media extraction only (no screenshots)
    return await this.extractMediaFromContent(url, content);
  }

  /**
   * Extract media from already-scraped content without additional scraping
   */
  private async extractMediaFromContent(url: string, content: string): Promise<ImageCollection> {
    // Extract OG images and favicon from the scraped content without screenshot capture
    const ogImages = this.extractOGImagesFromContent(content);
    const favicon = this.extractFaviconFromContent(content, url);

    return {
      favicon,
      ogImages,
      screenshot: null // No screenshot for cost-optimized content
    };
  }

  /**
   * Extract OG images from scraped content
   */
  private extractOGImagesFromContent(content: string): string[] {
    const ogImages: string[] = [];

    // Look for og:image meta tags
    const ogImageMatches = content.match(/og:image["']?\s*content=["']([^"']+)["']/gi);
    if (ogImageMatches) {
      ogImageMatches.forEach(match => {
        const urlMatch = match.match(/content=["']([^"']+)["']/i);
        if (urlMatch && urlMatch[1]) {
          ogImages.push(urlMatch[1]);
        }
      });
    }

    // Look for twitter:image meta tags
    const twitterImageMatches = content.match(/twitter:image(?::src)?["']?\s*content=["']([^"']+)["']/gi);
    if (twitterImageMatches) {
      twitterImageMatches.forEach(match => {
        const urlMatch = match.match(/content=["']([^"']+)["']/i);
        if (urlMatch && urlMatch[1]) {
          ogImages.push(urlMatch[1]);
        }
      });
    }

    // Remove duplicates
    return [...new Set(ogImages)];
  }

  /**
   * Extract favicon from scraped content
   */
  private extractFaviconFromContent(content: string, baseUrl: string): string | null {
    // Look for favicon link tags
    const faviconMatches = content.match(/<link[^>]*rel=["'](?:icon|shortcut icon)["'][^>]*href=["']([^"']+)["']/gi);
    if (faviconMatches && faviconMatches.length > 0) {
      const urlMatch = faviconMatches[0].match(/href=["']([^"']+)["']/i);
      if (urlMatch && urlMatch[1]) {
        const faviconUrl = urlMatch[1];
        // Convert relative URLs to absolute
        if (faviconUrl.startsWith('/')) {
          const baseUrlObj = new URL(baseUrl);
          return `${baseUrlObj.protocol}//${baseUrlObj.host}${faviconUrl}`;
        }
        return faviconUrl;
      }
    }

    // Fallback to default favicon location
    try {
      const baseUrlObj = new URL(baseUrl);
      return `${baseUrlObj.protocol}//${baseUrlObj.host}/favicon.ico`;
    } catch {
      return null;
    }
  }

  /**
   * Process multi-page scraping if configured
   */
  private async processMultiPageScraping(
    mainUrl: string,
    mainContent: string,
    config: MultiPageScrapingConfig
  ): Promise<ScrapeResult[]> {
    try {
      // Update multi-page scraper configuration
      multiPageScraper.updateConfig(config);

      // Discover and plan scraping
      const decision = await multiPageScraper.discoverAndPlanScraping(mainUrl, mainContent);
      
      console.log(`Multi-page decision: ${decision.reason}`);
      
      // Execute scraping for immediate pages
      if (decision.scrapeNow.length > 0) {
        return await multiPageScraper.executeMultiPageScraping(decision);
      }

      return [];
    } catch (error) {
      console.error('Multi-page scraping failed:', error);
      return [];
    }
  }

  /**
   * Calculate cost analysis for the scraping operation
   */
  private calculateCostAnalysis(mainResult: ScrapeResult, additionalPages: ScrapeResult[]) {
    const mainCredits = mainResult.metadata?.creditsUsed || 0;
    const additionalCredits = additionalPages.reduce((sum, page) => sum + (page.metadata?.creditsUsed || 0), 0);
    const totalCredits = mainCredits + additionalCredits;

    // Determine optimization strategy based on request type and proxy type
    const requestType = mainResult.metadata?.requestType || 'Unknown';
    const proxyType = mainResult.metadata?.proxyType || 'datacenter';
    const browserEnabled = mainResult.metadata?.browserEnabled || false;

    let optimizationStrategy = 'Basic Datacenter';

    if (browserEnabled && proxyType === 'residential') {
      optimizationStrategy = 'Residential + Browser (Premium)';
    } else if (browserEnabled && proxyType === 'datacenter') {
      optimizationStrategy = 'Datacenter + Browser';
    } else if (proxyType === 'residential') {
      optimizationStrategy = 'Residential Proxy';
    } else {
      optimizationStrategy = 'Datacenter Proxy (Cost-Optimized)';
    }

    // Estimate savings based on optimization strategy
    const estimatedSavings = this.estimateSavings(mainResult, additionalPages);

    return {
      creditsUsed: totalCredits,
      estimatedSavings,
      optimizationStrategy,
      requestType,
      mainPageCredits: mainCredits,
      additionalPagesCredits: additionalCredits
    };
  }

  /**
   * Estimate cost savings from optimization
   */
  private estimateSavings(mainResult: ScrapeResult, additionalPages: ScrapeResult[]): number {
    // Calculate savings based on optimization patterns used
    let savings = 0;

    // Main page savings
    if (mainResult.metadata?.requestType === 'Datacenter Proxy') {
      savings += 4; // Saved 4 credits by not using enhanced scraping
    }

    // Additional pages savings
    additionalPages.forEach(page => {
      if (page.metadata?.requestType === 'Datacenter Proxy') {
        savings += 4;
      }
    });

    return savings;
  }

  /**
   * Store scraped content in database
   */
  private async storeInDatabase(result: EnhancedScrapeResult): Promise<void> {
    try {
      console.log('🔍 DEBUG: Attempting database storage...');

      // Validate Supabase admin client and environment
      if (!supabaseAdmin) {
        throw new Error('Supabase admin client is not initialized - missing SUPABASE_SERVICE_ROLE_KEY');
      }

      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

      if (!supabaseUrl || !supabaseServiceKey) {
        throw new Error('Missing Supabase environment variables');
      }

      console.log('🔍 DEBUG: Environment check passed');
      console.log('🔍 DEBUG: Supabase URL:', supabaseUrl.substring(0, 30) + '...');
      console.log('🔍 DEBUG: Service key present:', !!supabaseServiceKey);

      // Test database connection first
      try {
        const { data: connectionTest, error: connectionError } = await supabaseAdmin
          .from('scraped_content')
          .select('count')
          .limit(1);

        if (connectionError) {
          console.error('🔍 DEBUG: Database connection test failed:', connectionError);
          throw new Error(`Database connection failed: ${connectionError.message}`);
        }
        console.log('🔍 DEBUG: Database connection test passed');
      } catch (connError) {
        console.error('🔍 DEBUG: Database connection exception:', connError);
        throw new Error(`Database connection exception: ${connError instanceof Error ? connError.message : String(connError)}`);
      }

      // Prepare insert data
      const insertData = {
        url: result.url,
        content: result.content,
        success: result.success,
        timestamp: result.timestamp,
        credits_used: result.costAnalysis?.creditsUsed || 0,
        metadata: result.metadata || {}
      };

      console.log('🔍 DEBUG: Insert data prepared:', {
        url: insertData.url,
        contentLength: insertData.content?.length,
        success: insertData.success,
        timestamp: insertData.timestamp,
        credits_used: insertData.credits_used,
        metadataKeys: Object.keys(insertData.metadata)
      });

      // Perform the insert using admin client
      const { data, error } = await supabaseAdmin
        .from('scraped_content')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('🔍 DEBUG: Database storage failed with error:', {
          message: error.message || 'No error message',
          details: error.details || 'No error details',
          hint: error.hint || 'No error hint',
          code: error.code || 'No error code',
          fullError: error
        });
        throw new Error(`Database storage failed: ${error.message || 'Unknown error'} (Code: ${error.code || 'Unknown'})`);
      } else {
        console.log('✅ Database storage successful:', data);
      }
    } catch (error) {
      console.error('🔍 DEBUG: Database storage exception:', {
        errorType: error instanceof Error ? error.constructor.name : typeof error,
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        fullError: error
      });
      throw error; // Re-throw to be handled by caller
    }
  }

  /**
   * Store scraped content in file system
   */
  private async storeInFileSystem(result: EnhancedScrapeResult): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');

      const sanitizedUrl = result.url?.replace(/[^a-zA-Z0-9]/g, '_') || 'unknown';
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `scraped_${sanitizedUrl}_${timestamp}.md`;

      const filePath = path.join(process.cwd(), 'data', 'scraped-content', filename);

      // Ensure directory exists
      await fs.mkdir(path.dirname(filePath), { recursive: true });

      // Prepare content with metadata
      const fileContent = `# Scraped Content: ${result.url}

## Metadata
- Scraped at: ${result.timestamp}
- Success: ${result.success}
- Credits used: ${result.costAnalysis?.creditsUsed || 0}

## Content
${result.content}
`;

      await fs.writeFile(filePath, fileContent, 'utf-8');
      console.log(`Content stored in file system: ${filePath}`);
    } catch (error) {
      console.warn('File system storage error:', error);
    }
  }

  /**
   * Create a failed result object
   */
  private createFailedResult(url: string, error: string): EnhancedScrapeResult {
    return {
      success: false,
      content: '',
      error,
      timestamp: new Date().toISOString(),
      url,
      metadata: {
        creditsUsed: 0,
        requestType: 'failed'
      }
    };
  }

  /**
   * Process content for AI consumption
   */
  optimizeContentForAI(content: string): string {
    // Remove excessive whitespace and navigation elements
    content = content.replace(/\n{3,}/g, '\n\n');
    content = content.replace(/^(Navigation|Menu|Footer|Header)[\s\S]*?(?=\n#|\n\n|$)/gm, '');

    // Clean HTML artifacts and normalize headers
    content = content.replace(/<[^>]*>/g, '');
    content = content.replace(/^#{4,}/gm, '###');

    // Ensure content fits within token limits (reserve 20% for response)
    const maxLength = 50000; // ~12-15K tokens for Gemini input
    if (content.length > maxLength) {
      content = content.substring(0, maxLength) + '\n\n[Content truncated for AI processing]';
    }

    return content.trim();
  }

  /**
   * Batch processing for multiple URLs
   */
  async processBatch(urls: string[], options: {
    costOptimization?: boolean;
    mediaCollection?: boolean;
    multiPageScraping?: boolean;
    batchSize?: number;
  } = {}): Promise<EnhancedScrapeResult[]> {
    const {
      costOptimization = true,
      mediaCollection = false, // Disabled by default for batch processing
      multiPageScraping = false, // Disabled by default for batch processing
      batchSize = 5
    } = options;

    const results: EnhancedScrapeResult[] = [];
    
    // Process URLs in batches to avoid overwhelming the API
    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(urls.length / batchSize)}`);

      const batchPromises = batch.map(url => 
        this.processEnhancedScrape({
          url,
          options: {},
          costOptimization,
          mediaCollection,
          multiPageConfig: multiPageScraping ? undefined : {
            enabled: false,
            mode: 'conditional' as const,
            maxPagesPerTool: 0,
            creditThreshold: 0,
            pageTypes: {
              pricing: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false },
              faq: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false },
              features: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false },
              about: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false }
            },
            fallbackStrategy: { searchInMainPage: false, useNavigation: false, useSitemap: false }
          }
        })
      );

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error(`Batch processing failed for ${batch[index]}:`, result.reason);
          results.push(this.createFailedResult(batch[index], result.reason));
        }
      });

      // Add delay between batches to respect rate limits
      if (i + batchSize < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return results;
  }

  /**
   * Save scraped content to file system (for later AI processing)
   */
  async saveScrapedContent(result: EnhancedScrapeResult, filename?: string): Promise<string> {
    const fs = await import('fs/promises');
    const path = await import('path');

    const sanitizedUrl = result.url?.replace(/[^a-zA-Z0-9]/g, '_') || 'unknown';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const finalFilename = filename || `scraped_${sanitizedUrl}_${timestamp}.md`;
    
    const filePath = path.join(process.cwd(), 'data', 'scraped-content', finalFilename);
    
    // Ensure directory exists
    await fs.mkdir(path.dirname(filePath), { recursive: true });
    
    // Prepare content with metadata
    const fileContent = `# Scraped Content: ${result.url}

## Metadata
- Scraped at: ${result.timestamp}
- Success: ${result.success}
- Credits used: ${result.costAnalysis?.creditsUsed || 0}
- Quality score: ${result.metadata?.qualityScore || 'N/A'}

## Content Analysis
- Has substantial content: ${result.contentAnalysis?.hasSubstantialContent}
- Has structure: ${result.contentAnalysis?.hasStructure}
- Scenario: ${result.contentAnalysis?.scenario}

## Main Content
${result.content}

${result.additionalPages?.length ? `
## Additional Pages
${result.additionalPages.map(page => `
### ${page.metadata?.pageType || 'Unknown'} Page
${page.content}
`).join('\n')}
` : ''}
`;

    await fs.writeFile(filePath, fileContent, 'utf-8');
    console.log(`Scraped content saved to: ${filePath}`);
    
    return filePath;
  }
}

// Export singleton instance
export const contentProcessor = new ContentProcessor();
