import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Create both clients like the pipeline does
const supabase = createClient(supabaseUrl, supabaseAnonKey); // Anonymous client
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey); // Admin client

async function testClientDifference() {
  const toolId = '8acbfaac-a5d2-4e28-b3ee-2e4b44c01892';
  
  console.log('🔍 TESTING SUPABASE CLIENT DIFFERENCE');
  console.log('=' .repeat(50));
  console.log(`Tool ID: ${toolId}`);
  
  // Test 1: Anonymous client (what the pipeline was using before)
  console.log('\n📋 Test 1: Anonymous Client (ANON_KEY)');
  try {
    const { data: anonTools, error: anonError } = await supabase
      .from('tools')
      .select('submission_source, submission_type, name, website, id')
      .eq('id', toolId);
      
    if (anonError) {
      console.log('❌ Anonymous client error:', anonError.message);
    } else if (anonTools && anonTools.length > 0) {
      console.log('✅ Anonymous client found tool:', anonTools[0]);
    } else {
      console.log('❌ Anonymous client: Tool not found');
    }
  } catch (error) {
    console.log('💥 Anonymous client exception:', error);
  }
  
  // Test 2: Admin client (what the pipeline should use)
  console.log('\n📋 Test 2: Admin Client (SERVICE_ROLE_KEY)');
  try {
    const { data: adminTools, error: adminError } = await supabaseAdmin
      .from('tools')
      .select('submission_source, submission_type, name, website, id')
      .eq('id', toolId);
      
    if (adminError) {
      console.log('❌ Admin client error:', adminError.message);
    } else if (adminTools && adminTools.length > 0) {
      console.log('✅ Admin client found tool:', adminTools[0]);
    } else {
      console.log('❌ Admin client: Tool not found');
    }
  } catch (error) {
    console.log('💥 Admin client exception:', error);
  }
  
  // Test 3: Enhanced lookup with admin client (our fixed method)
  console.log('\n📋 Test 3: Enhanced Lookup with Admin Client');
  const result = await testEnhancedLookupWithAdminClient(toolId);
  console.log(`Result: ${result ? 'SUCCESS - detected as bulk processing' : 'FAILED - would default to user submission'}`);
  
  console.log('\n' + '=' .repeat(50));
  console.log('🎯 CONCLUSION');
  console.log('If anonymous client fails but admin client succeeds,');
  console.log('then our fix should resolve the pipeline issue.');
}

// Enhanced lookup with admin client (our fixed method)
async function testEnhancedLookupWithAdminClient(toolId: string): Promise<boolean> {
  const maxRetries = 5;
  const retryDelay = 1000;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`  🔍 Admin client attempt ${attempt}/${maxRetries}`);

      if (attempt === 1) {
        await new Promise(resolve => setTimeout(resolve, 750)); // Initial delay
      }

      if (!supabaseAdmin) {
        console.log(`  ❌ Admin client not available`);
        return false;
      }

      const { data: tools, error: queryError } = await supabaseAdmin
        .from('tools')
        .select('submission_source, submission_type, name, website, id')
        .eq('id', toolId.trim());

      if (queryError) {
        console.log(`  ❌ Database error: ${queryError.message}`);
        if (attempt === maxRetries) return false;
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        continue;
      }

      if (!tools || tools.length === 0) {
        console.log(`  ❌ Tool not found`);
        if (attempt < maxRetries) {
          console.log(`  🔄 Retrying in ${retryDelay * attempt}ms...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
          continue;
        }
        return false;
      }

      const tool = tools[0];
      const isBulkProcessing = tool.submission_source === 'bulk_processing';
      console.log(`  ✅ Found: ${tool.name} - ${tool.submission_source}`);
      console.log(`  🎯 Result: ${isBulkProcessing ? 'BULK PROCESSING' : 'USER SUBMISSION'}`);
      return isBulkProcessing;
    } catch (error) {
      console.log(`  💥 Exception: ${error}`);
      if (attempt === maxRetries) return false;
      await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
    }
  }

  return false;
}

testClientDifference().catch(console.error);
