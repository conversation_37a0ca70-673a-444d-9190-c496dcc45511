#!/usr/bin/env tsx

/**
 * Vercel Deployment Configuration Script
 * 
 * This script helps configure environment variables and deployment settings
 * for the AI Dude Directory application on Vercel.
 */

import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

interface VercelConfig {
  deploymentUrl: string;
  customDomains: string[];
  environmentVariables: Record<string, string>;
}

const VERCEL_CONFIG: VercelConfig = {
  deploymentUrl: 'dudeai-dwigto9vp-arun-christudhas-projects.vercel.app',
  customDomains: [
    'dudeai.vercel.app',
    'dudeai-arun-christudhas-projects.vercel.app'
  ],
  environmentVariables: {
    // Vercel Configuration
    'VERCEL_URL': 'dudeai-dwigto9vp-arun-christudhas-projects.vercel.app',
    'NEXT_PUBLIC_VERCEL_URL': 'dudeai-dwigto9vp-arun-christudhas-projects.vercel.app',
    'VERCEL_ENV': 'production',
    'SITE_URL': 'https://dudeai.vercel.app',
    'NEXT_PUBLIC_SITE_URL': 'https://dudeai.vercel.app',
    'CANONICAL_URL': 'https://dudeai.vercel.app',
    
    // Production Settings
    'NODE_ENV': 'production',
    'NEXT_TELEMETRY_DISABLED': '1',
    
    // Job Queue Configuration
    'JOB_QUEUE_ENABLED': 'true',
    'MAX_CONCURRENT_JOBS': '3',
    'JOB_RETRY_ATTEMPTS': '3',
    'JOB_TIMEOUT_MS': '300000',

    // Feature Flags
    'CONTENT_GENERATION_ENABLED': 'true',
    'SCRAPING_ENABLED': 'true',
    'ENHANCED_SCRAPING_ENABLED': 'true',
    'ENABLE_BULK_PROCESSING': 'true',
    'ENABLE_ADMIN_PANEL': 'true',
    'ENABLE_API_ENDPOINTS': 'true',
    'ENABLE_SEARCH_FUNCTIONALITY': 'true',
    
    // Performance & Security
    'ENABLE_RATE_LIMITING': 'true',
    'RATE_LIMIT_REQUESTS_PER_MINUTE': '100',
    'ENABLE_CACHING': 'true',
    'CACHE_TTL_SECONDS': '3600',
    
    // Database Optimization
    'DATABASE_POOL_MIN': '2',
    'DATABASE_POOL_MAX': '10',
    'DATABASE_TIMEOUT': '30000'
  }
};

async function generateVercelEnvCommands() {
  console.log('🚀 Generating Vercel Environment Variable Commands');
  console.log('=' .repeat(60));
  
  try {
    // Read the local environment file
    const envPath = join(process.cwd(), '.env.local');
    const envContent = readFileSync(envPath, 'utf-8');
    
    // Parse environment variables
    const envVars: Record<string, string> = {};
    
    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    // Merge with Vercel-specific config
    const allEnvVars = { ...envVars, ...VERCEL_CONFIG.environmentVariables };
    
    // Generate Vercel CLI commands
    const commands: string[] = [];
    
    console.log('📋 Environment Variables to Set in Vercel Dashboard:');
    console.log('');
    
    Object.entries(allEnvVars).forEach(([key, value]) => {
      // Skip local-only variables
      if (key.includes('localhost') || key.includes('N8N_') && !value) {
        return;
      }
      
      console.log(`${key}=${value}`);
      commands.push(`vercel env add ${key} production`);
    });
    
    console.log('');
    console.log('🔧 Vercel CLI Commands (if using CLI):');
    console.log('');
    
    commands.forEach(cmd => {
      console.log(cmd);
    });
    
    // Generate deployment checklist
    console.log('');
    console.log('✅ Deployment Checklist:');
    console.log('');
    console.log('1. Set environment variables in Vercel dashboard');
    console.log('2. Configure custom domains:');
    VERCEL_CONFIG.customDomains.forEach(domain => {
      console.log(`   - ${domain}`);
    });
    console.log('3. Enable Vercel Analytics and Speed Insights');
    console.log('4. Configure deployment settings');
    console.log('5. Test all functionality in production');
    
    // Save commands to file
    const commandsFile = join(process.cwd(), 'vercel-env-commands.txt');
    writeFileSync(commandsFile, commands.join('\n'));
    
    console.log('');
    console.log(`📄 Commands saved to: ${commandsFile}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Failed to generate Vercel configuration:', error);
    return false;
  }
}

async function validateDeployment() {
  console.log('');
  console.log('🔍 Deployment Validation Checklist:');
  console.log('=' .repeat(60));
  
  const checks = [
    'Supabase connection working',
    'OpenAI API integration functional',
    'OpenRouter API integration functional', 
    'Web scraping (Scrape.do) working',
    'Database migrations applied',
    'Admin panel accessible',
    'Search functionality working',
    'Content generation pipeline operational',
    'Bulk processing system functional',
    'Analytics and monitoring active'
  ];
  
  checks.forEach((check, index) => {
    console.log(`${index + 1}. [ ] ${check}`);
  });
  
  console.log('');
  console.log('🌐 Test URLs:');
  console.log(`- Main site: https://${VERCEL_CONFIG.customDomains[0]}`);
  console.log(`- Admin panel: https://${VERCEL_CONFIG.customDomains[0]}/admin`);
  console.log(`- API health: https://${VERCEL_CONFIG.customDomains[0]}/api/health`);
  console.log(`- Search API: https://${VERCEL_CONFIG.customDomains[0]}/api/search`);
}

async function main() {
  console.log('🎯 AI Dude Directory - Vercel Deployment Configuration');
  console.log('');
  
  const success = await generateVercelEnvCommands();
  
  if (success) {
    await validateDeployment();
    
    console.log('');
    console.log('🎉 Vercel configuration generated successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Copy environment variables to Vercel dashboard');
    console.log('2. Deploy your application');
    console.log('3. Test all functionality');
    console.log('4. Configure custom domains');
    
  } else {
    console.log('❌ Configuration generation failed');
    process.exit(1);
  }
}

main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
