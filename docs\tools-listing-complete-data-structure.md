# DudeAI Tools Listing - Complete Data Structure

## Overview

This document provides a comprehensive overview of all data fields used in the DudeAI tools listing system, including database schema, API responses, UI components, and data transformations.

## Complete Tool Data Structure

### AITool Interface (Frontend)
```typescript
interface AITool {
  // === CORE IDENTIFICATION ===
  id: string;                    // Unique tool identifier
  name: string;                  // Tool name
  slug: string;                  // URL-friendly identifier
  
  // === VISUAL ASSETS ===
  logoUrl?: string;              // Tool logo/icon URL
  screenshots?: string[];        // Array of screenshot URLs
  
  // === DESCRIPTIONS ===
  description: string;           // Main description
  shortDescription?: string;     // Brief summary (150 chars max)
  detailedDescription?: string;  // Extended description
  
  // === NAVIGATION & LINKS ===
  link: string;                  // Internal tool page URL (/tools/tool-id)
  website?: string;              // External website URL
  
  // === CATEGORIZATION ===
  category?: string;             // Primary category ID
  subcategory?: string;          // Subcategory name
  company?: string;              // Company/creator name
  
  // === STATUS & VERIFICATION ===
  isVerified?: boolean;          // Verification status
  isClaimed?: boolean;           // Claimed by owner
  contentStatus?: string;        // 'published', 'draft', 'under_review', etc.
  
  // === RICH CONTENT ===
  features?: string[];           // Feature list array
  pricing?: PricingInfo;         // Pricing structure
  prosAndCons?: {               // Pros and cons analysis
    pros: string[];
    cons: string[];
  };
  
  // === FAQ SYSTEM ===
  faqs?: FAQ[];                 // Frequently Asked Questions array
  
  // === SOCIAL & REVIEWS ===
  socialLinks?: SocialLinks;     // Social media links
  reviews?: {                   // Review data
    rating: number;
    totalReviews: number;
  };
  
  // === METADATA & TAGS ===
  tags?: Tag[];                 // Tool tags for filtering
  hashtags?: string[];          // Social media hashtags
  haiku?: string;               // Creative haiku description
  
  // === SEO METADATA ===
  metaTitle?: string;           // SEO page title
  metaDescription?: string;     // SEO meta description
  metaKeywords?: string;        // SEO keywords (comma-separated)
  
  // === AI GENERATION METADATA ===
  aiGenerationStatus?: string;   // 'pending', 'completed', 'failed'
  contentQualityScore?: number;  // AI-assessed quality score (0-100)
  lastAiUpdate?: string;        // Last AI content update timestamp
  
  // === VERSIONING & AUDIT ===
  version?: number;             // Content version number
  createdAt?: string;           // Creation timestamp
  updatedAt?: string;           // Last update timestamp
  publishedAt?: string;         // Publication timestamp
  
  // === ADDITIONAL METADATA ===
  submissionSource?: string;    // 'user_submission', 'bulk_processing', 'admin'
  errorMessage?: string;        // Error message if processing failed
  processingNotes?: string;     // Admin processing notes
}
```

### Database Schema (tools table)
```sql
CREATE TABLE tools (
  -- === CORE IDENTIFICATION ===
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  
  -- === VISUAL ASSETS ===
  logo_url TEXT,
  screenshots JSONB,            -- Array of screenshot URLs
  
  -- === DESCRIPTIONS ===
  description TEXT,
  short_description VARCHAR(150),
  detailed_description TEXT,
  
  -- === NAVIGATION & LINKS ===
  link TEXT NOT NULL,           -- Internal tool page URL
  website TEXT,                 -- External website URL
  
  -- === CATEGORIZATION ===
  category_id VARCHAR(255) REFERENCES categories(id),
  subcategory VARCHAR(255),
  company VARCHAR(255),
  
  -- === STATUS & VERIFICATION ===
  is_verified BOOLEAN DEFAULT FALSE,
  is_claimed BOOLEAN DEFAULT FALSE,
  content_status VARCHAR(50) DEFAULT 'draft',
  
  -- === RICH CONTENT (JSONB) ===
  features JSONB,               -- Feature list array
  pricing JSONB,                -- Pricing structure object
  pros_and_cons JSONB,          -- Pros and cons object
  social_links JSONB,           -- Social media links object
  haiku JSONB,                  -- Creative haiku object
  hashtags JSONB,               -- Social hashtags array
  releases JSONB,               -- Release information array
  
  -- === FAQ SYSTEM ===
  faqs JSONB,                   -- FAQ array (see FAQ structure below)
  
  -- === SEO METADATA ===
  meta_title VARCHAR(255),      -- SEO page title
  meta_description TEXT,        -- SEO meta description
  meta_keywords TEXT,           -- SEO keywords (comma-separated)
  
  -- === AI GENERATION METADATA ===
  ai_generation_status VARCHAR(50) DEFAULT 'pending',
  content_quality_score INTEGER CHECK (content_quality_score >= 0 AND content_quality_score <= 100),
  last_ai_update TIMESTAMP,
  
  -- === VERSIONING & AUDIT ===
  version INTEGER DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP,
  
  -- === ADDITIONAL METADATA ===
  submission_source VARCHAR(50) DEFAULT 'user_submission',
  error_message TEXT,
  processing_notes TEXT,
  
  -- === CLAIM INFORMATION ===
  claim_info JSONB             -- Tool claiming information
);
```

## Supporting Data Structures

### FAQ Structure (JSONB Array)
```typescript
interface FAQ {
  id?: string;                  // UUID
  question: string;             // FAQ question
  answer: string;               // FAQ answer
  displayOrder?: number;        // Display order (0-based)
  priority?: number;            // Priority level (0-10)
  category?: 'general' | 'pricing' | 'features' | 'support' | 'getting-started';
  tags?: string[];              // Associated tags
  isActive?: boolean;           // Active status
  isFeatured?: boolean;         // Featured status
  source?: 'manual' | 'ai_generated' | 'scraped' | 'user_submitted';
  sourceMetadata?: {            // Source metadata
    aiModel?: string;
    confidence?: number;
    scrapedFrom?: string;
    submittedBy?: string;
  };
  metaKeywords?: string;        // SEO keywords for this FAQ
  helpScore?: number;           // Helpfulness score (0-10)
  viewCount?: number;           // View count
}
```

### Pricing Structure (JSONB Object)
```typescript
interface PricingInfo {
  type: 'free' | 'freemium' | 'paid' | 'open source' | 'subscription';
  startingPrice?: number;       // Starting price in USD
  currency?: string;            // Currency code (default: USD)
  billingCycle?: 'monthly' | 'yearly' | 'one-time' | 'usage-based';
  tiers?: PricingTier[];        // Pricing tiers array
  freeTrialDays?: number;       // Free trial period
  hasFreePlan?: boolean;        // Whether free plan exists
  description?: string;         // Pricing description
}

interface PricingTier {
  name: string;                 // Tier name (e.g., "Basic", "Pro")
  price: number;                // Price amount
  currency: string;             // Currency code
  billingCycle: string;         // Billing cycle
  features: string[];           // Features included
  isPopular?: boolean;          // Popular tier flag
  description?: string;         // Tier description
}
```

### Social Links Structure (JSONB Object)
```typescript
interface SocialLinks {
  twitter?: string;             // Twitter/X URL
  linkedin?: string;            // LinkedIn URL
  github?: string;              // GitHub URL
  facebook?: string;            // Facebook URL
  youtube?: string;             // YouTube URL
  discord?: string;             // Discord URL
  telegram?: string;            // Telegram URL
  instagram?: string;           // Instagram URL
  tiktok?: string;              // TikTok URL
  reddit?: string;              // Reddit URL
}
```

### Tag Structure
```typescript
interface Tag {
  type: 'Trending' | 'New' | 'Premium' | 'AI' | 'Featured' | 'Popular';
  color?: string;               // Hex color code
  description?: string;         // Tag description
}
```

## API Response Structures

### Tools Listing API Response
```typescript
interface ToolsListingResponse {
  success: boolean;
  data: AITool[];               // Array of complete tool objects
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  metadata?: {                  // Optional metadata
    totalCategories: number;
    totalVerified: number;
    lastUpdated: string;
  };
}
```

### Individual Tool API Response
```typescript
interface ToolDetailResponse {
  success: boolean;
  data: AITool;                 // Complete tool object with all fields
  related?: AITool[];           // Related tools (optional)
  metadata?: {
    viewCount: number;
    lastViewed: string;
    similarTools: number;
  };
}
```
