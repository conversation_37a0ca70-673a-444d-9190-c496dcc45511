-- Migration 010: Add 'archived' status to bulk_processing_jobs constraint
-- This migration updates the status constraint to include 'archived' status
-- which is used by the job cleanup service for archiving old jobs

-- =====================================================
-- UPDATE STATUS CONSTRAINT
-- =====================================================

-- Drop the existing constraint
ALTER TABLE bulk_processing_jobs DROP CONSTRAINT IF EXISTS bulk_processing_jobs_status_check;

-- Add the updated constraint with 'archived' included
ALTER TABLE bulk_processing_jobs ADD CONSTRAINT bulk_processing_jobs_status_check 
CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'paused', 'archived'));

-- =====================================================
-- VERIFICATION TESTS
-- =====================================================

-- Test archived status insertion
DO $$
DECLARE
    test_uuid UUID := gen_random_uuid();
BEGIN
    -- Test archived status
    INSERT INTO bulk_processing_jobs (
        id, 
        job_type, 
        status, 
        total_items,
        processed_items,
        successful_items,
        failed_items,
        source_data,
        processing_options,
        created_by
    ) VALUES (
        test_uuid, 
        'manual_entry', 
        'archived', 
        1,
        1,
        1,
        0,
        '{"test": true}'::jsonb,
        '{}'::jsonb,
        'system'
    );
    
    RAISE NOTICE 'SUCCESS: archived status accepted';
    
    -- Clean up test record
    DELETE FROM bulk_processing_jobs WHERE id = test_uuid;
    
EXCEPTION
    WHEN check_violation THEN
        RAISE EXCEPTION 'FAILED: archived status still rejected by constraint';
    WHEN OTHERS THEN
        RAISE NOTICE 'Test completed with other error (may be expected): %', SQLERRM;
        -- Clean up test record if it exists
        DELETE FROM bulk_processing_jobs WHERE id = test_uuid;
END $$;

-- =====================================================
-- CONSTRAINT DOCUMENTATION
-- =====================================================

COMMENT ON CONSTRAINT bulk_processing_jobs_status_check ON bulk_processing_jobs 
IS 'Ensures status matches allowed values including archived for job cleanup service';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check the updated constraint
SELECT
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conname = 'bulk_processing_jobs_status_check';

-- Show all allowed status values
SELECT 
    'bulk_processing_jobs_status_check' as constraint_name,
    unnest(ARRAY[
        'pending',
        'processing', 
        'completed',
        'failed',
        'cancelled',
        'paused',
        'archived'
    ]) as allowed_status;

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

-- Log successful migration
DO $$
BEGIN
    RAISE NOTICE '✅ Migration 010 completed successfully';
    RAISE NOTICE '✅ Added archived status to bulk_processing_jobs constraint';
    RAISE NOTICE '✅ Job cleanup service can now archive old jobs';
    RAISE NOTICE '✅ Constraint violation error should be resolved';
END $$;
