import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function verifyFixReady() {
  console.log('🔧 VERIFYING FIX IS READY FOR DEPLOYMENT');
  console.log('=' .repeat(50));
  
  // Test with the most recent failing tool
  const toolId = '576b72e0-2442-4e3b-921b-085a62f919ff';
  
  console.log(`Testing with tool: ${toolId}`);
  
  // Simulate the exact pipeline logic with our fixes
  const result = await simulateFixedPipeline(toolId);
  
  console.log('\n' + '=' .repeat(50));
  console.log('🎯 VERIFICATION RESULT');
  console.log('=' .repeat(50));
  
  if (result) {
    console.log('✅ SUCCESS: Fix is ready!');
    console.log('✅ Tool will be detected as bulk processing');
    console.log('✅ Pipeline will bypass manual review');
    console.log('✅ Tool will be published directly');
    console.log('\n🚀 NEXT STEP: Restart the Next.js application to apply the fix');
    console.log('   The application needs to restart to pick up the code changes');
  } else {
    console.log('❌ ISSUE: Fix needs more work');
    console.log('❌ Tool will still default to user submission workflow');
  }
}

// Simulate the fixed pipeline logic
async function simulateFixedPipeline(toolId: string): Promise<boolean> {
  const maxRetries = 5;
  const retryDelay = 1000;

  console.log('\n📋 Simulating Fixed Pipeline Logic');
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔍 Attempt ${attempt}/${maxRetries} with admin client`);

      // Validate tool ID format
      if (!toolId || typeof toolId !== 'string' || toolId.trim().length === 0) {
        console.log(`❌ Invalid tool ID format: ${toolId}`);
        return false;
      }

      // Add progressive delay to allow for database consistency
      if (attempt === 1) {
        await new Promise(resolve => setTimeout(resolve, 750));
      }

      // Use admin client for server-side operations to ensure proper permissions
      if (!supabaseAdmin) {
        console.log(`❌ Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY`);
        return false;
      }

      const { data: tools, error: queryError } = await supabaseAdmin
        .from('tools')
        .select('submission_source, submission_type, name, website, id')
        .eq('id', toolId.trim());

      if (queryError) {
        console.log(`❌ Database query error (attempt ${attempt}): ${queryError.message}`);
        
        if (attempt === maxRetries) {
          console.log(`❌ Final attempt failed. Error details:`, {
            code: queryError.code,
            message: queryError.message,
            details: queryError.details,
            hint: queryError.hint
          });
          return false;
        }
        
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        continue;
      }

      if (!tools || tools.length === 0) {
        console.log(`❌ Tool not found (attempt ${attempt})`);
        
        if (attempt < maxRetries) {
          console.log(`🔄 Retrying in ${retryDelay * attempt}ms - likely race condition`);
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
          continue;
        }
        
        console.log(`❌ Tool not found after ${maxRetries} attempts`);
        return false;
      }

      if (tools.length > 1) {
        console.log(`⚠️ Multiple tools found with ID ${toolId} - using first match`);
      }

      const tool = tools[0];

      if (!tool.submission_source) {
        console.log(`⚠️ Tool has null/undefined submission_source`);
        return false;
      }

      console.log(`📋 Tool details: ${tool.name} (${tool.website})`);
      console.log(`📝 Submission type: ${tool.submission_type}`);
      console.log(`📋 Submission source: ${tool.submission_source}`);

      const isBulkProcessing = tool.submission_source === 'bulk_processing';
      console.log(`🎯 Result: ${isBulkProcessing ? 'BULK PROCESSING - will bypass manual review' : 'USER SUBMISSION - will go to manual review'}`);

      return isBulkProcessing;
    } catch (error) {
      console.log(`💥 Exception (attempt ${attempt}):`, error);
      
      if (attempt === maxRetries) {
        console.log(`Could not determine submission source after ${maxRetries} attempts`);
        return false;
      }
      
      await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
    }
  }

  return false;
}

verifyFixReady().catch(console.error);
