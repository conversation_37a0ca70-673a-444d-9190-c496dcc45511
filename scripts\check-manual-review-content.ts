#!/usr/bin/env tsx

/**
 * Check Manual Review Content
 * 
 * This script checks if the FoodiePrep tool has generated content
 * available for manual review and shows where to find it.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function checkManualReviewContent() {
  console.log('🔍 Checking Manual Review Content for FoodiePrep...\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Find FoodiePrep tool
    console.log('1. 🔍 Searching for FoodiePrep tool...');
    const { data: tools, error: toolError } = await supabase
      .from('tools')
      .select('*')
      .or('name.ilike.%foodieprep%,website.ilike.%foodieprep%')
      .order('created_at', { ascending: false });

    if (toolError) {
      throw new Error(`Failed to search for tools: ${toolError.message}`);
    }

    if (!tools || tools.length === 0) {
      console.log('❌ No FoodiePrep tools found in database');
      return;
    }

    console.log(`✅ Found ${tools.length} FoodiePrep tool(s):\n`);

    for (const tool of tools) {
      console.log(`📋 Tool: ${tool.name} (ID: ${tool.id})`);
      console.log(`   🌐 Website: ${tool.website}`);
      console.log(`   📊 Status: ${tool.content_status}`);
      console.log(`   🤖 AI Status: ${tool.ai_generation_status}`);
      console.log(`   📈 Quality Score: ${tool.content_quality_score || 'N/A'}`);
      console.log(`   📅 Last AI Update: ${tool.last_ai_update || 'N/A'}`);
      
      // Check if generated content exists
      if (tool.generated_content) {
        console.log(`   ✅ Generated Content: Available (${JSON.stringify(tool.generated_content).length} characters)`);
        
        // Show content structure
        const content = tool.generated_content;
        console.log(`   📝 Content Structure:`);
        if (typeof content === 'object') {
          Object.keys(content).forEach(key => {
            console.log(`      - ${key}: ${typeof content[key]}`);
          });
        } else {
          console.log(`      - Type: ${typeof content}`);
        }
      } else {
        console.log(`   ❌ Generated Content: Not available`);
      }

      // Check editorial review status
      if (tool.editorial_review_id) {
        console.log(`   📋 Editorial Review ID: ${tool.editorial_review_id}`);
        
        // Get editorial review details
        const { data: review, error: reviewError } = await supabase
          .from('editorial_reviews')
          .select('*')
          .eq('id', tool.editorial_review_id)
          .single();

        if (!reviewError && review) {
          console.log(`   📋 Review Status: ${review.status}`);
          console.log(`   👤 Reviewed By: ${review.reviewed_by || 'N/A'}`);
          console.log(`   📅 Review Date: ${review.reviewed_at || 'N/A'}`);
        }
      } else {
        console.log(`   📋 Editorial Review: Not created yet`);
      }

      console.log('');
    }

    // Check AI generation jobs
    console.log('2. 🔍 Checking AI generation jobs...');
    const { data: jobs, error: jobError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .in('tool_id', tools.map(t => t.id))
      .order('created_at', { ascending: false });

    if (jobError) {
      console.warn(`⚠️ Failed to fetch AI jobs: ${jobError.message}`);
    } else if (jobs && jobs.length > 0) {
      console.log(`✅ Found ${jobs.length} AI generation job(s):\n`);
      
      jobs.forEach((job, index) => {
        console.log(`   Job ${index + 1}: ${job.id}`);
        console.log(`   📋 Type: ${job.type}`);
        console.log(`   📊 Status: ${job.status}`);
        console.log(`   📈 Progress: ${job.progress}%`);
        console.log(`   📅 Created: ${job.created_at}`);
        console.log(`   📅 Updated: ${job.updated_at}`);
        if (job.error) {
          console.log(`   ❌ Error: ${job.error}`);
        }
        console.log('');
      });
    } else {
      console.log('❌ No AI generation jobs found');
    }

    // Show manual review interface locations
    console.log('3. 📍 Manual Review Interface Locations:');
    console.log('');
    console.log('   🎯 Primary Review Interface:');
    console.log('   📍 URL: /admin/content/review');
    console.log('   📁 File: src/app/admin/content/review/page.tsx');
    console.log('   📋 Purpose: Review AI-generated content before publishing');
    console.log('');
    console.log('   🎯 Editorial Dashboard:');
    console.log('   📍 URL: /admin/editorial');
    console.log('   📁 File: src/app/admin/editorial/page.tsx');
    console.log('   📋 Purpose: Initial submission review and approval');
    console.log('');
    console.log('   🎯 Individual Tool Review:');
    console.log('   📍 URL: /admin/tools/[tool-id]');
    console.log('   📁 File: src/app/admin/tools/[id]/page.tsx');
    console.log('   📋 Purpose: Detailed tool editing and content management');
    console.log('');

    // Show database locations
    console.log('4. 🗄️ Database Storage Locations:');
    console.log('');
    console.log('   📊 Generated Content:');
    console.log('   🗄️ Table: tools');
    console.log('   📋 Column: generated_content (JSONB)');
    console.log('   📝 Contains: Complete AI Dude output (features, pros/cons, etc.)');
    console.log('');
    console.log('   📊 Editorial Reviews:');
    console.log('   🗄️ Table: editorial_reviews');
    console.log('   📋 Purpose: Manual review decisions and notes');
    console.log('');
    console.log('   📊 AI Generation Jobs:');
    console.log('   🗄️ Table: ai_generation_jobs');
    console.log('   📋 Purpose: Job tracking and AI responses');
    console.log('');

    console.log('🎯 Next Steps:');
    console.log('1. Visit /admin/content/review to see pending content');
    console.log('2. Look for tools with status "pending_manual_review"');
    console.log('3. Review and approve the AI-generated content');
    console.log('4. Content will be published after approval');

  } catch (error) {
    console.error('💥 Check failed:', error);
  }
}

// Run the check
checkManualReviewContent().catch(console.error);
