import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function investigateMissingTool() {
  const toolId = '8acbfaac-a5d2-4e28-b3ee-2e4b44c01892';
  const bulkJobId = '1b17a3e4-7bb3-4ce8-ab7e-1eb17349f3f9';
  
  console.log('🔍 INVESTIGATING MISSING TOOL ISSUE');
  console.log('=' .repeat(50));
  console.log(`Tool ID: ${toolId}`);
  console.log(`Bulk Job ID: ${bulkJobId}`);
  
  // 1. Check if tool exists at all
  console.log('\n📋 Step 1: Check if tool exists in database');
  const { data: tools, error: toolError } = await supabase
    .from('tools')
    .select('*')
    .eq('id', toolId);
    
  if (toolError) {
    console.log('❌ Error querying tools:', toolError.message);
  } else if (tools && tools.length > 0) {
    console.log('✅ Tool found:', {
      id: tools[0].id,
      name: tools[0].name,
      website: tools[0].website,
      submission_source: tools[0].submission_source,
      submission_type: tools[0].submission_type,
      created_at: tools[0].created_at
    });
  } else {
    console.log('❌ Tool NOT found in database');
  }
  
  // 2. Check bulk processing job
  console.log('\n📋 Step 2: Check bulk processing job');
  const { data: bulkJobs, error: bulkError } = await supabase
    .from('bulk_processing_jobs')
    .select('*')
    .eq('id', bulkJobId);
    
  if (bulkError) {
    console.log('❌ Error querying bulk jobs:', bulkError.message);
  } else if (bulkJobs && bulkJobs.length > 0) {
    console.log('✅ Bulk job found:', {
      id: bulkJobs[0].id,
      status: bulkJobs[0].status,
      total_tools: bulkJobs[0].total_tools,
      processed_tools: bulkJobs[0].processed_tools,
      created_at: bulkJobs[0].created_at
    });
  } else {
    console.log('❌ Bulk job NOT found');
  }
  
  // 3. Check AI generation jobs for this tool
  console.log('\n📋 Step 3: Check AI generation jobs for this tool');
  const { data: aiJobs, error: aiJobError } = await supabase
    .from('ai_generation_jobs')
    .select('*')
    .eq('tool_id', toolId)
    .order('created_at', { ascending: false });
    
  if (aiJobError) {
    console.log('❌ Error querying AI jobs:', aiJobError.message);
  } else {
    console.log(`📊 Found ${aiJobs?.length || 0} AI generation jobs for this tool:`);
    if (aiJobs && aiJobs.length > 0) {
      aiJobs.forEach((job, i) => {
        console.log(`  ${i + 1}. ${job.id} - ${job.job_type} - ${job.status} (${job.created_at})`);
        if (job.error_message) {
          console.log(`     Error: ${job.error_message}`);
        }
      });
    }
  }
  
  // 4. Search for any tools with similar patterns
  console.log('\n📋 Step 4: Search for recent tools (last 10 minutes)');
  const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();
  const { data: recentTools, error: recentError } = await supabase
    .from('tools')
    .select('id, name, website, submission_source, created_at')
    .gte('created_at', tenMinutesAgo)
    .order('created_at', { ascending: false })
    .limit(10);
    
  if (recentError) {
    console.log('❌ Error querying recent tools:', recentError.message);
  } else {
    console.log(`📊 Found ${recentTools?.length || 0} recent tools:`);
    if (recentTools && recentTools.length > 0) {
      recentTools.forEach((tool, i) => {
        console.log(`  ${i + 1}. ${tool.id} - ${tool.name} (${tool.submission_source}) - ${tool.created_at}`);
      });
    }
  }
  
  // 5. Check if there are any tools with the same website
  console.log('\n📋 Step 5: Check for tools with same website pattern');
  const { data: sameWebsiteTools, error: websiteError } = await supabase
    .from('tools')
    .select('id, name, website, submission_source, created_at')
    .order('created_at', { ascending: false })
    .limit(5);
    
  if (websiteError) {
    console.log('❌ Error querying by website:', websiteError.message);
  } else {
    console.log(`📊 Recent tools (any website):`);
    if (sameWebsiteTools && sameWebsiteTools.length > 0) {
      sameWebsiteTools.forEach((tool, i) => {
        console.log(`  ${i + 1}. ${tool.id} - ${tool.name} (${tool.website}) - ${tool.submission_source} - ${tool.created_at}`);
      });
    }
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('🎯 INVESTIGATION COMPLETE');
  
  if (!tools || tools.length === 0) {
    console.log('❌ ISSUE IDENTIFIED: Tool was never created in the database');
    console.log('💡 This suggests a problem in the bulk processing tool creation phase');
    console.log('🔧 Need to investigate the bulk processing engine tool creation logic');
  } else {
    console.log('✅ Tool exists - this would be a different issue');
  }
}

investigateMissingTool().catch(console.error);
