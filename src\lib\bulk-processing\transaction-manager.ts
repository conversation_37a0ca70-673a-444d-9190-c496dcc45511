/**
 * Transaction Manager for Bulk Processing
 * Handles database transactions and rollback operations
 */

import { createClient } from '@supabase/supabase-js';

export interface TransactionContext {
  id: string;
  operations: TransactionOperation[];
  startedAt: Date;
  status: 'pending' | 'committed' | 'rolled_back' | 'failed';
}

export interface TransactionOperation {
  id: string;
  type: 'insert' | 'update' | 'delete' | 'rpc';
  table?: string;
  data?: any;
  conditions?: any;
  rpcName?: string;
  rpcParams?: any;
  executedAt?: Date;
  rollbackData?: any;
}

export interface TransactionResult {
  success: boolean;
  transactionId: string;
  operationsExecuted: number;
  error?: string;
  rollbackPerformed?: boolean;
}

/**
 * Transaction Manager
 * Provides transaction support for bulk processing operations
 */
export class TransactionManager {
  private supabase;
  private activeTransactions = new Map<string, TransactionContext>();

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Begin a new transaction
   */
  async beginTransaction(): Promise<string> {
    const transactionId = `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const context: TransactionContext = {
      id: transactionId,
      operations: [],
      startedAt: new Date(),
      status: 'pending'
    };

    this.activeTransactions.set(transactionId, context);
    console.log(`🔄 Started transaction ${transactionId}`);
    
    return transactionId;
  }

  /**
   * Add an operation to the transaction
   */
  addOperation(
    transactionId: string,
    operation: Omit<TransactionOperation, 'id' | 'executedAt'>
  ): string {
    const context = this.activeTransactions.get(transactionId);
    if (!context) {
      throw new Error(`Transaction ${transactionId} not found`);
    }

    if (context.status !== 'pending') {
      throw new Error(`Transaction ${transactionId} is not in pending state`);
    }

    const operationId = `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullOperation: TransactionOperation = {
      ...operation,
      id: operationId
    };

    context.operations.push(fullOperation);
    console.log(`📝 Added operation ${operationId} to transaction ${transactionId}`);
    
    return operationId;
  }

  /**
   * Execute all operations in the transaction
   */
  async executeTransaction(transactionId: string): Promise<TransactionResult> {
    const context = this.activeTransactions.get(transactionId);
    if (!context) {
      return {
        success: false,
        transactionId,
        operationsExecuted: 0,
        error: 'Transaction not found'
      };
    }

    if (context.status !== 'pending') {
      return {
        success: false,
        transactionId,
        operationsExecuted: 0,
        error: `Transaction is in ${context.status} state`
      };
    }

    console.log(`⚡ Executing transaction ${transactionId} with ${context.operations.length} operations`);

    let executedOperations = 0;
    const rollbackData: any[] = [];

    try {
      // Execute operations sequentially
      for (const operation of context.operations) {
        const result = await this.executeOperation(operation);
        
        if (!result.success) {
          throw new Error(`Operation ${operation.id} failed: ${result.error}`);
        }

        operation.executedAt = new Date();
        operation.rollbackData = result.rollbackData;
        rollbackData.push(result.rollbackData);
        executedOperations++;

        console.log(`✅ Executed operation ${operation.id} (${executedOperations}/${context.operations.length})`);
      }

      // Mark transaction as committed
      context.status = 'committed';
      console.log(`✅ Transaction ${transactionId} committed successfully`);

      return {
        success: true,
        transactionId,
        operationsExecuted: executedOperations
      };

    } catch (error) {
      console.error(`❌ Transaction ${transactionId} failed:`, error);
      
      // Perform rollback
      const rollbackResult = await this.rollbackTransaction(transactionId, executedOperations);
      
      context.status = 'failed';
      
      return {
        success: false,
        transactionId,
        operationsExecuted: executedOperations,
        error: (error as Error).message,
        rollbackPerformed: rollbackResult.success
      };
    }
  }

  /**
   * Execute a single operation
   */
  private async executeOperation(operation: TransactionOperation): Promise<{
    success: boolean;
    error?: string;
    rollbackData?: any;
  }> {
    try {
      switch (operation.type) {
        case 'insert':
          return await this.executeInsert(operation);
        case 'update':
          return await this.executeUpdate(operation);
        case 'delete':
          return await this.executeDelete(operation);
        case 'rpc':
          return await this.executeRpc(operation);
        default:
          throw new Error(`Unknown operation type: ${operation.type}`);
      }
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Execute insert operation
   */
  private async executeInsert(operation: TransactionOperation): Promise<{
    success: boolean;
    error?: string;
    rollbackData?: any;
  }> {
    const { data, error } = await this.supabase
      .from(operation.table!)
      .insert(operation.data)
      .select();

    if (error) {
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      rollbackData: {
        type: 'delete',
        table: operation.table,
        conditions: data?.[0] ? { id: data[0].id } : null
      }
    };
  }

  /**
   * Execute update operation
   */
  private async executeUpdate(operation: TransactionOperation): Promise<{
    success: boolean;
    error?: string;
    rollbackData?: any;
  }> {
    // First, get the current data for rollback
    const { data: currentData, error: selectError } = await this.supabase
      .from(operation.table!)
      .select('*')
      .match(operation.conditions);

    if (selectError) {
      return {
        success: false,
        error: selectError.message
      };
    }

    // Perform the update
    const { error: updateError } = await this.supabase
      .from(operation.table!)
      .update(operation.data)
      .match(operation.conditions);

    if (updateError) {
      return {
        success: false,
        error: updateError.message
      };
    }

    return {
      success: true,
      rollbackData: {
        type: 'update',
        table: operation.table,
        data: currentData?.[0],
        conditions: operation.conditions
      }
    };
  }

  /**
   * Execute delete operation
   */
  private async executeDelete(operation: TransactionOperation): Promise<{
    success: boolean;
    error?: string;
    rollbackData?: any;
  }> {
    // First, get the data that will be deleted for rollback
    const { data: dataToDelete, error: selectError } = await this.supabase
      .from(operation.table!)
      .select('*')
      .match(operation.conditions);

    if (selectError) {
      return {
        success: false,
        error: selectError.message
      };
    }

    // Perform the delete
    const { error: deleteError } = await this.supabase
      .from(operation.table!)
      .delete()
      .match(operation.conditions);

    if (deleteError) {
      return {
        success: false,
        error: deleteError.message
      };
    }

    return {
      success: true,
      rollbackData: {
        type: 'insert',
        table: operation.table,
        data: dataToDelete
      }
    };
  }

  /**
   * Execute RPC operation
   */
  private async executeRpc(operation: TransactionOperation): Promise<{
    success: boolean;
    error?: string;
    rollbackData?: any;
  }> {
    const { data, error } = await this.supabase.rpc(
      operation.rpcName!,
      operation.rpcParams || {}
    );

    if (error) {
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      rollbackData: {
        type: 'rpc_result',
        rpcName: operation.rpcName,
        result: data
      }
    };
  }

  /**
   * Rollback a transaction
   */
  async rollbackTransaction(
    transactionId: string,
    operationsToRollback?: number
  ): Promise<{ success: boolean; error?: string }> {
    const context = this.activeTransactions.get(transactionId);
    if (!context) {
      return {
        success: false,
        error: 'Transaction not found'
      };
    }

    console.log(`🔄 Rolling back transaction ${transactionId}`);

    const opsToRollback = operationsToRollback || context.operations.length;
    const operationsToReverse = context.operations
      .slice(0, opsToRollback)
      .filter(op => op.executedAt && op.rollbackData)
      .reverse(); // Rollback in reverse order

    try {
      for (const operation of operationsToReverse) {
        await this.rollbackOperation(operation);
        console.log(`↩️ Rolled back operation ${operation.id}`);
      }

      context.status = 'rolled_back';
      console.log(`✅ Transaction ${transactionId} rolled back successfully`);

      return { success: true };

    } catch (error) {
      console.error(`❌ Rollback failed for transaction ${transactionId}:`, error);
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Rollback a single operation
   */
  private async rollbackOperation(operation: TransactionOperation): Promise<void> {
    const rollbackData = operation.rollbackData;
    if (!rollbackData) return;

    switch (rollbackData.type) {
      case 'delete':
        if (rollbackData.conditions) {
          await this.supabase
            .from(rollbackData.table)
            .delete()
            .match(rollbackData.conditions);
        }
        break;

      case 'update':
        if (rollbackData.data && rollbackData.conditions) {
          await this.supabase
            .from(rollbackData.table)
            .update(rollbackData.data)
            .match(rollbackData.conditions);
        }
        break;

      case 'insert':
        if (rollbackData.data) {
          await this.supabase
            .from(rollbackData.table)
            .insert(rollbackData.data);
        }
        break;

      default:
        console.log(`Cannot rollback operation type: ${rollbackData.type}`);
    }
  }

  /**
   * Clean up completed transactions
   */
  cleanup(): void {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    this.activeTransactions.forEach((context, id) => {
      if (context.startedAt < oneHourAgo &&
          (context.status === 'committed' || context.status === 'rolled_back' || context.status === 'failed')) {
        this.activeTransactions.delete(id);
        console.log(`🧹 Cleaned up transaction ${id}`);
      }
    });
  }

  /**
   * Get transaction status
   */
  getTransactionStatus(transactionId: string): TransactionContext | null {
    return this.activeTransactions.get(transactionId) || null;
  }
}

// Singleton instance
let transactionManagerInstance: TransactionManager | null = null;

export function getTransactionManager(): TransactionManager {
  if (!transactionManagerInstance) {
    transactionManagerInstance = new TransactionManager();
  }
  return transactionManagerInstance;
}
