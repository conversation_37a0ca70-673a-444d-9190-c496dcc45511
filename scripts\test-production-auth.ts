#!/usr/bin/env tsx

/**
 * Test Script: Check production authentication
 * 
 * This script tests the production admin API authentication
 */

import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

async function testProductionAuth() {
  console.log('🔐 Testing Production Admin API Authentication');
  console.log('=' .repeat(60));
  
  const baseUrl = 'https://dudeai.vercel.app';
  const adminApiKey = process.env.ADMIN_API_KEY;
  
  console.log('Configuration:');
  console.log(`   Production URL: ${baseUrl}`);
  console.log(`   Local ADMIN_API_KEY: ${adminApiKey ? `${adminApiKey.substring(0, 10)}...` : 'NOT_SET'}`);
  console.log('');
  
  if (!adminApiKey) {
    console.error('❌ ADMIN_API_KEY is not set in local environment');
    return false;
  }
  
  // Test 1: Bulk processing GET endpoint with correct key
  console.log('1️⃣ Testing bulk processing GET with local admin key...');
  try {
    const response = await fetch(`${baseUrl}/api/admin/bulk-processing`, {
      method: 'GET',
      headers: {
        'x-admin-api-key': adminApiKey
      }
    });
    
    console.log(`   Response status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Authentication successful with local admin key');
      console.log(`   Found ${data.data?.jobs?.length || 0} jobs`);
      return true;
    } else if (response.status === 401) {
      console.error('❌ Authentication failed - 401 Unauthorized');
      console.error('   This means the local admin key does not match the production key');
      
      const errorText = await response.text();
      console.error(`   Response: ${errorText}`);
      return false;
    } else {
      console.error(`❌ Unexpected response: ${response.status}`);
      const errorText = await response.text();
      console.error(`   Response: ${errorText}`);
      return false;
    }
  } catch (error) {
    console.error('❌ Request failed:', error);
    return false;
  }
}

async function main() {
  const success = await testProductionAuth();
  
  console.log('');
  console.log('=' .repeat(60));
  
  if (success) {
    console.log('🎉 Production authentication test passed!');
    console.log('✅ Local admin key matches production environment');
    console.log('✅ Bulk processing API is accessible');
  } else {
    console.log('❌ Production authentication test failed!');
    console.log('');
    console.log('🔧 Possible issues:');
    console.log('1. ADMIN_API_KEY not set in Vercel environment variables');
    console.log('2. NEXT_PUBLIC_ADMIN_API_KEY not set in Vercel environment variables');
    console.log('3. Local and production admin keys do not match');
    console.log('4. Environment variables not properly deployed');
    console.log('');
    console.log('💡 Solutions:');
    console.log('1. Check Vercel dashboard → Project → Settings → Environment Variables');
    console.log('2. Ensure ADMIN_API_KEY and NEXT_PUBLIC_ADMIN_API_KEY are both set');
    console.log('3. Verify both keys have the same value: aidude_admin_2024_secure_key_xyz789');
    console.log('4. Redeploy the application after setting environment variables');
  }
  
  process.exit(success ? 0 : 1);
}

main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
