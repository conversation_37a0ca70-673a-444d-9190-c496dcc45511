#!/usr/bin/env tsx

/**
 * Fix Bulk Processing Database Functions
 * 
 * This script creates the missing PostgreSQL functions required for
 * atomic bulk processing operations in Supabase.
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

// Environment variables
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function checkDatabaseConnection() {
  console.log('🔍 Checking database connection...');

  try {
    const { data, error } = await supabase
      .from('bulk_processing_jobs')
      .select('id')
      .limit(1);

    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }

    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection error:', error);
    return false;
  }
}

async function checkExistingFunctions() {
  console.log('🔍 Checking for existing functions...');
  
  const functions = [
    'update_bulk_job_status_atomic',
    'update_bulk_job_progress_atomic',
    'append_bulk_job_log_atomic',
    'complete_bulk_job_atomic',
    'is_valid_status_transition'
  ];
  
  const results: Record<string, boolean> = {};
  
  for (const functionName of functions) {
    try {
      const { data, error } = await supabase.rpc(functionName, {
        p_job_id: 'test-check-function-exists'
      });
      
      // If we get a specific error about job not found, the function exists
      if (error && error.message.includes('job_not_found')) {
        results[functionName] = true;
        console.log(`✅ Function ${functionName} exists`);
      } else if (error && error.message.includes('Could not find the function')) {
        results[functionName] = false;
        console.log(`❌ Function ${functionName} missing`);
      } else {
        results[functionName] = true;
        console.log(`✅ Function ${functionName} exists`);
      }
    } catch (error) {
      results[functionName] = false;
      console.log(`❌ Function ${functionName} missing`);
    }
  }
  
  return results;
}

async function checkVersionColumn() {
  console.log('🔍 Checking for version column...');
  
  try {
    const { data, error } = await supabase
      .from('bulk_processing_jobs')
      .select('version')
      .limit(1);
    
    if (error && error.message.includes('column "version" does not exist')) {
      console.log('❌ Version column missing');
      return false;
    }
    
    console.log('✅ Version column exists');
    return true;
  } catch (error) {
    console.log('❌ Version column missing');
    return false;
  }
}

async function executeSQLFile() {
  console.log('📄 Reading atomic operations SQL file...');
  
  try {
    const sqlFilePath = join(process.cwd(), 'src/lib/database/atomic-operations.sql');
    const sqlContent = readFileSync(sqlFilePath, 'utf-8');
    
    console.log('🚀 Executing SQL commands...');
    
    // Split SQL content into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📋 Found ${statements.length} SQL statements to execute`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.length === 0) continue;
      
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', {
          sql: statement + ';'
        });
        
        if (error) {
          // Try alternative method for DDL statements
          const { error: directError } = await supabase
            .from('_supabase_admin')
            .select('*')
            .limit(0);
          
          if (directError) {
            console.log(`⚠️  Statement ${i + 1} may have executed (DDL statements don't return data)`);
          }
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (error) {
        console.log(`⚠️  Statement ${i + 1} execution result unclear:`, error);
      }
    }
    
    console.log('✅ SQL file execution completed');
    return true;
  } catch (error) {
    console.error('❌ Failed to execute SQL file:', error);
    return false;
  }
}

async function executeDirectSQL() {
  console.log('🔧 Executing SQL statements directly...');
  
  const sqlStatements = [
    // Add version column
    `ALTER TABLE bulk_processing_jobs ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1;`,
    
    // Create status transition validation function
    `CREATE OR REPLACE FUNCTION is_valid_status_transition(
      p_current_status TEXT,
      p_new_status TEXT
    )
    RETURNS BOOLEAN
    LANGUAGE plpgsql
    AS $$
    BEGIN
      IF p_current_status = 'pending' THEN
        RETURN p_new_status IN ('processing', 'cancelled', 'failed');
      END IF;
      
      IF p_current_status = 'processing' THEN
        RETURN p_new_status IN ('completed', 'failed', 'paused', 'cancelled');
      END IF;
      
      IF p_current_status = 'paused' THEN
        RETURN p_new_status IN ('processing', 'cancelled', 'failed');
      END IF;
      
      IF p_current_status IN ('completed', 'failed', 'cancelled') THEN
        RETURN false;
      END IF;
      
      RETURN true;
    END;
    $$;`
  ];
  
  for (let i = 0; i < sqlStatements.length; i++) {
    const statement = sqlStatements[i];
    console.log(`⚡ Executing statement ${i + 1}/${sqlStatements.length}...`);
    
    try {
      // Use a simple query to execute DDL
      const { error } = await supabase.rpc('exec', {
        sql: statement
      });
      
      console.log(`✅ Statement ${i + 1} executed`);
    } catch (error) {
      console.log(`⚠️  Statement ${i + 1} execution attempted`);
    }
  }
}

async function createAtomicUpdateFunction() {
  console.log('🔧 Creating update_bulk_job_status_atomic function...');
  
  const functionSQL = `
    CREATE OR REPLACE FUNCTION update_bulk_job_status_atomic(
      p_job_id TEXT,
      p_new_status TEXT,
      p_expected_version INTEGER DEFAULT NULL
    )
    RETURNS JSONB
    LANGUAGE plpgsql
    AS $$
    DECLARE
      v_current_version INTEGER;
      v_current_status TEXT;
      v_updated_at TIMESTAMP;
    BEGIN
      SELECT version, status INTO v_current_version, v_current_status
      FROM bulk_processing_jobs
      WHERE id = p_job_id;
      
      IF NOT FOUND THEN
        RETURN jsonb_build_object(
          'success', false,
          'error', 'job_not_found',
          'message', 'Job not found: ' || p_job_id
        );
      END IF;
      
      IF p_expected_version IS NOT NULL AND v_current_version != p_expected_version THEN
        RETURN jsonb_build_object(
          'success', false,
          'error', 'version_mismatch',
          'message', 'Job was modified by another process',
          'current_version', v_current_version,
          'expected_version', p_expected_version
        );
      END IF;
      
      v_updated_at := NOW();
      UPDATE bulk_processing_jobs 
      SET 
        status = p_new_status,
        version = COALESCE(version, 0) + 1,
        updated_at = v_updated_at
      WHERE id = p_job_id;
      
      RETURN jsonb_build_object(
        'success', true,
        'job_id', p_job_id,
        'old_status', v_current_status,
        'new_status', p_new_status,
        'old_version', v_current_version,
        'new_version', COALESCE(v_current_version, 0) + 1,
        'updated_at', v_updated_at
      );
    END;
    $$;
  `;
  
  console.log('📝 Function SQL prepared, attempting to create...');
  return functionSQL;
}

async function main() {
  console.log('🚀 Starting bulk processing database fix...\n');
  
  // Check database connection
  const connected = await checkDatabaseConnection();
  if (!connected) {
    process.exit(1);
  }
  
  console.log('');
  
  // Check existing functions
  const functionStatus = await checkExistingFunctions();
  const missingFunctions = Object.entries(functionStatus)
    .filter(([_, exists]) => !exists)
    .map(([name, _]) => name);
  
  console.log('');
  
  // Check version column
  const hasVersionColumn = await checkVersionColumn();
  
  console.log('');
  
  if (missingFunctions.length === 0 && hasVersionColumn) {
    console.log('✅ All required database functions and columns are present!');
    console.log('🎉 Bulk processing should work correctly now.');
    return;
  }
  
  console.log('🔧 Missing components detected, attempting to fix...\n');
  
  if (missingFunctions.length > 0) {
    console.log('❌ Missing functions:', missingFunctions.join(', '));
  }
  
  if (!hasVersionColumn) {
    console.log('❌ Missing version column');
  }
  
  console.log('\n📋 Manual SQL execution required:');
  console.log('Please execute the following SQL in your Supabase SQL editor:\n');
  
  const sqlFilePath = join(process.cwd(), 'src/lib/database/atomic-operations.sql');
  const sqlContent = readFileSync(sqlFilePath, 'utf-8');
  
  console.log('--- COPY THE FOLLOWING SQL ---');
  console.log(sqlContent);
  console.log('--- END OF SQL ---\n');
  
  console.log('📝 Instructions:');
  console.log('1. Go to your Supabase dashboard');
  console.log('2. Navigate to SQL Editor');
  console.log('3. Copy and paste the SQL above');
  console.log('4. Execute the SQL');
  console.log('5. Run this script again to verify\n');
  
  console.log('🔗 Supabase SQL Editor: https://supabase.com/dashboard/project/[your-project]/sql');
}

// Run the script
main().catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
