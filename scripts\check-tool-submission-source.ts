#!/usr/bin/env tsx

/**
 * Check Tool Submission Source
 * 
 * This script checks the submission_source of specific tools to debug
 * why the bulk processing bypass isn't working.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function checkToolSubmissionSource() {
  console.log('🔍 Checking Tool Submission Source...\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Check the specific tool from the logs
    const toolId = '7a866780-9f4b-4d00-8013-49ad1f42ff2e';
    
    console.log(`1. 🔍 Checking tool ${toolId}...`);
    const { data: tool, error: toolError } = await supabase
      .from('tools')
      .select('*')
      .eq('id', toolId)
      .single();

    if (toolError) {
      console.log(`❌ Error fetching tool: ${toolError.message}`);
    } else if (tool) {
      console.log(`✅ Tool found: ${tool.name}`);
      console.log(`   🌐 Website: ${tool.website}`);
      console.log(`   📊 Content Status: ${tool.content_status}`);
      console.log(`   🤖 AI Status: ${tool.ai_generation_status}`);
      console.log(`   📝 Submission Type: ${tool.submission_type}`);
      console.log(`   📋 Submission Source: ${tool.submission_source}`);
      console.log(`   📅 Created: ${tool.created_at}`);
      console.log(`   📅 Updated: ${tool.updated_at}`);
      
      if (tool.submission_source === 'bulk_processing') {
        console.log('   ✅ CORRECT: Tool has bulk_processing submission source');
      } else {
        console.log(`   ❌ ISSUE: Tool has submission source "${tool.submission_source}" instead of "bulk_processing"`);
      }
    } else {
      console.log('❌ Tool not found');
    }

    console.log('\n2. 🔍 Checking all FoodiePrep tools...');
    const { data: foodiePrepTools, error: foodieError } = await supabase
      .from('tools')
      .select('*')
      .or('name.ilike.%foodieprep%,website.ilike.%foodieprep%')
      .order('created_at', { ascending: false });

    if (foodieError) {
      console.log(`❌ Error fetching FoodiePrep tools: ${foodieError.message}`);
    } else if (foodiePrepTools && foodiePrepTools.length > 0) {
      console.log(`✅ Found ${foodiePrepTools.length} FoodiePrep tool(s):\n`);
      
      foodiePrepTools.forEach((tool, index) => {
        console.log(`   Tool ${index + 1}: ${tool.name} (${tool.id})`);
        console.log(`      🌐 Website: ${tool.website}`);
        console.log(`      📊 Content Status: ${tool.content_status}`);
        console.log(`      🤖 AI Status: ${tool.ai_generation_status}`);
        console.log(`      📝 Submission Type: ${tool.submission_type}`);
        console.log(`      📋 Submission Source: ${tool.submission_source}`);
        console.log(`      📅 Created: ${tool.created_at}`);
        
        if (tool.submission_source === 'bulk_processing') {
          console.log('      ✅ Has bulk_processing source');
        } else {
          console.log(`      ❌ Has "${tool.submission_source}" source (should be bulk_processing)`);
        }
        console.log('');
      });
    } else {
      console.log('❌ No FoodiePrep tools found');
    }

    console.log('\n3. 🔍 Checking recent AI generation jobs...');
    const { data: jobs, error: jobError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5);

    if (jobError) {
      console.log(`❌ Error fetching jobs: ${jobError.message}`);
    } else if (jobs && jobs.length > 0) {
      console.log(`✅ Found ${jobs.length} recent job(s):\n`);
      
      for (const job of jobs) {
        console.log(`   Job: ${job.id}`);
        console.log(`      📋 Type: ${job.job_type}`);
        console.log(`      📊 Status: ${job.status}`);
        console.log(`      🔗 Tool ID: ${job.tool_id}`);
        console.log(`      📅 Created: ${job.created_at}`);
        
        // Check the tool for this job
        if (job.tool_id) {
          const { data: jobTool } = await supabase
            .from('tools')
            .select('name, submission_source')
            .eq('id', job.tool_id)
            .single();
            
          if (jobTool) {
            console.log(`      🏷️ Tool: ${jobTool.name}`);
            console.log(`      📋 Tool Source: ${jobTool.submission_source}`);
          }
        }
        console.log('');
      }
    }

    console.log('\n🎯 Summary:');
    console.log('   If tools show submission_source other than "bulk_processing",');
    console.log('   they will go through manual review instead of being published directly.');
    console.log('   This explains why the bypass is not working.');

  } catch (error) {
    console.error('💥 Check failed:', error);
  }
}

// Run the check
checkToolSubmissionSource().catch(console.error);
