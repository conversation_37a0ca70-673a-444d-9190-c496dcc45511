import asyncio
import aiohttp
import re
import json
import logging
import sys
from bs4 import BeautifulSoup

from categories import CATEGORY_MAP

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("fix_extractor.log", encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("fix_extractor")

# Build subcategory mapping
SUBCATEGORY_TO_CATEGORY = {}
for category, data in CATEGORY_MAP.items():
    for subcat in data["subcategories"]:
        SUBCATEGORY_TO_CATEGORY[subcat["name"]] = category

async def extract_tool_data(url, subcategory="Email Finder/Verifier"):
    """Extract data from a single tool URL with exact extract_tool.py methods"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    }
    
    # Get the category from subcategory
    category = SUBCATEGORY_TO_CATEGORY.get(subcategory, "")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(url, headers=headers) as response:
            if response.status != 200:
                logger.error(f"Failed to fetch URL: {url}")
                return None
            
            html = await response.text()
    
    # Parse with BeautifulSoup
    soup = BeautifulSoup(html, 'html.parser')
    
    # Initialize result structure
    data = {
        "category": category,
        "subcategory": subcategory,
        "name": "",
        "short_description": "",
        "image_url": "",
        "website_url": "",
        "pricing_type": "",
        "pricing_amount": "",
        "full_description": "",
        "use_cases_and_features": [],
        "page_url": url
    }
    
    # 1. Extract name
    name_element = soup.select_one('h1.elementor-heading-title')
    if name_element:
        data["name"] = name_element.text.strip()
    else:
        h1 = soup.find('h1')
        if h1:
            data["name"] = h1.text.strip()
    
    # 2. Extract short description
    meta_desc = soup.select_one('meta[name="description"]')
    if meta_desc and meta_desc.has_attr('content'):
        data["short_description"] = meta_desc['content'].strip()
    
    # 3. Extract image URL - PRIORITY IS .WEBP FROM CSS
    # First, specifically look for the CSS background image
    webp_found = False
    for style in soup.select('style'):
        if not style.string:
            continue
            
        # Look for background-image:url("...") patterns
        bg_images = re.findall(r'background-image\s*:\s*url\([\'"]([^\'"]+)[\'"]\)', style.string)
        for bg_image in bg_images:
            if '/wp-content/smush-webp/' in bg_image and bg_image.endswith('.webp'):
                data["image_url"] = bg_image
                webp_found = True
                logger.info(f"Found .webp image in CSS: {bg_image}")
                break
        if webp_found:
            break
    
    # If no webp found, check meta og:image
    if not webp_found:
        meta_image = soup.select_one('meta[property="og:image"]')
        if meta_image and meta_image.has_attr('content'):
            data["image_url"] = meta_image['content']
    
    # 4. Extract website URL
    website_links = soup.select('a.jet-listing-dynamic-link__link')
    if website_links:
        for link in website_links:
            if link.has_attr('href'):
                # Get the URL without query parameters
                full_url = link['href']
                base_url = full_url.split('?')[0] if '?' in full_url else full_url
                data["website_url"] = base_url
                break
    
    # If we didn't find the URL with the above method, try looking for any "Visit" buttons
    if not data["website_url"]:
        visit_links = soup.select('a')
        for link in visit_links:
            link_text = link.text.strip().lower()
            if ('visit' in link_text or 'try' in link_text) and link.has_attr('href'):
                full_url = link['href']
                base_url = full_url.split('?')[0] if '?' in full_url else full_url
                data["website_url"] = base_url
                break
    
    # 5. Extract pricing information
    pricing_elements = soup.select('.jet-listing-dynamic-field__content')
    pricing_types = ["Free", "Freemium", "Free Trial", "Paid", "Contact for Pricing"]
    pricing_found = False
    
    for element in pricing_elements:
        element_text = element.text.strip()
        
        # Check if this element contains a pricing type
        for pricing_type in pricing_types:
            if pricing_type.lower() == element_text.lower():
                data["pricing_type"] = element_text
                pricing_found = True
                break
        
        # Check if this element contains pricing amount
        if '$' in element_text or '/month' in element_text.lower() or '/mo' in element_text.lower():
            data["pricing_amount"] = element_text
        
        # If we found both pricing type and amount, we can stop
        if pricing_found and data["pricing_amount"]:
            break
    
    # If we couldn't find pricing with the above method, try broader search
    if not pricing_found:
        for pricing_type in pricing_types:
            pricing_pattern = re.compile(pricing_type, re.IGNORECASE)
            pricing_element = soup.find(string=pricing_pattern)
            if pricing_element:
                data["pricing_type"] = pricing_type
                break
    
    # 6. Extract full description
    description_elements = soup.select('.elementor-widget-theme-post-content p')
    for p in description_elements:
        p_text = p.text.strip()
        if len(p_text) > 100:  # Only substantial paragraphs
            data["full_description"] = p_text
            break
    
    # 7. Extract use cases and features using the exact approach from extract_tool.py
    # First try to find the section with "Use Cases And Features" heading
    features_heading = soup.find(string=re.compile("Use Cases And Features", re.IGNORECASE))
    if features_heading:
        # Look for the text editor div after the heading
        feature_widget = None
        current = features_heading.parent
        
        # Navigate up to find a proper parent element
        while current and not feature_widget:
            # Try to find the closest text editor after this element
            feature_widget = current.find_next(class_="elementor-widget-text-editor")
            current = current.parent
        
        if feature_widget:
            # Get the container with the numbered list
            features_element = feature_widget.select_one('.elementor-widget-container')
            if features_element:
                # Get the raw HTML to preserve <br> tags
                features_html = str(features_element)
                
                # Extract numbered items with regex
                numbered_features = re.findall(r'(\d+)\.\s*([^<>\.]+)\.?', features_html)
                if numbered_features:
                    # Sort by the number to ensure correct order
                    sorted_features = sorted(numbered_features, key=lambda x: int(x[0]))
                    data["use_cases_and_features"] = [feature[1].strip() for feature in sorted_features]
    
    # If we couldn't extract features using the heading, try alternative methods
    if not data["use_cases_and_features"]:
        # Try to find text that looks like numbered list items
        for element in soup.select('p, div'):
            element_text = element.get_text()
            if re.search(r'1\.\s+', element_text):
                # Extract all numbered items
                feature_items = re.findall(r'(\d+)\.\s+([^\.]+)\.?', element_text)
                if feature_items:
                    # Sort by the number to ensure correct order
                    sorted_features = sorted(feature_items, key=lambda x: int(x[0]))
                    data["use_cases_and_features"] = [feature[1].strip() for feature in sorted_features]
                    break
    
    # If still no features, try ordered lists
    if not data["use_cases_and_features"]:
        for ol in soup.find_all('ol'):
            features = []
            for li in ol.find_all('li'):
                feature_text = li.text.strip()
                if len(feature_text) > 5:
                    features.append(feature_text)
            if features:
                data["use_cases_and_features"] = features
                break
    
    # Clean up features
    if data["use_cases_and_features"]:
        data["use_cases_and_features"] = [f for f in data["use_cases_and_features"] if len(f) > 5 and not any(
            x in f.lower() for x in ["submit tool", "contact", "about us", "privacy"])]

    
    return data

async def main():
    import argparse
    parser = argparse.ArgumentParser(description="Fix extraction for a specific URL")
    parser.add_argument("--url", required=True, help="URL to extract")
    parser.add_argument("--subcategory", default="Email Finder/Verifier", help="Subcategory of the tool")
    args = parser.parse_args()
    
    data = await extract_tool_data(args.url, args.subcategory)
    
    if data:
        print("\nExtracted AI Tool Data:\n")
        print(f"Category: {data.get('category', '')}")
        print(f"Subcategory: {data.get('subcategory', '')}")
        print(f"Name: {data.get('name', '')}")
        print(f"Short Description: {data.get('short_description', '')}")
        print(f"Image URL: {data.get('image_url', '')}")
        print(f"Website URL: {data.get('website_url', '')}")
        print(f"Pricing Type: {data.get('pricing_type', '')}")
        print(f"Pricing Amount: {data.get('pricing_amount', '')}")
        print("\nFull Description:")
        print(data.get('full_description', ''))
        print("\nUse Cases And Features:")
        for i, feature in enumerate(data.get('use_cases_and_features', []), 1):
            print(f"{i}. {feature}")
        
        # Also output as JSON
        print("\nJSON Output:")
        print(json.dumps(data, indent=2))
    else:
        print("Failed to extract data")

if __name__ == "__main__":
    asyncio.run(main())
