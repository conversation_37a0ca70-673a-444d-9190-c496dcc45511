# Media Collection Troubleshooting Guide

## 🚨 **Critical Issue: Database Schema Error**

### **Error Message:**
```
Could not find the 'error_message' column of 'tools' in the schema cache
```

### **Root Cause:**
The media collection system tries to update the `error_message` column in the `tools` table, but this column doesn't exist in the current database schema.

## 🔧 **IMMEDIATE FIX REQUIRED**

### **Step 1: Add Missing Database Columns**

Go to **Supabase Dashboard → SQL Editor** and run this SQL:

```sql
-- Add error_message column to tools table
ALTER TABLE tools ADD COLUMN IF NOT EXISTS error_message TEXT;

-- Add processing_notes column if it doesn't exist  
ALTER TABLE tools ADD COLUMN IF NOT EXISTS processing_notes TEXT;

-- Create index for error tracking
CREATE INDEX IF NOT EXISTS idx_tools_error_message ON tools(error_message) WHERE error_message IS NOT NULL;

-- Update any existing tools with failed status but no error message
UPDATE tools 
SET error_message = 'Legacy error - details not available'
WHERE ai_generation_status = 'failed' 
  AND (error_message IS NULL OR error_message = '');
```

### **Step 2: Verify the Fix**

After running the SQL, test with:
```bash
npm run fix:error-column
```

Expected output: `✅ error_message column is ready!`

## 📊 **Additional Issues Identified**

### **1. Orphaned Jobs (9 out of 20 jobs)**

**Symptoms:**
- Jobs stuck in 'processing' or 'pending' status
- Memory usage climbing (28MB/35MB)
- Jobs not completing or failing properly

**Causes:**
- Jobs started but never completed due to errors
- Pipeline dependencies not met
- System restarts while jobs were running

**Solution:**
```sql
-- Reset orphaned jobs (older than 30 minutes)
UPDATE ai_generation_jobs 
SET status = 'failed',
    error_logs = jsonb_build_object(
        'error', 'Job orphaned - reset by cleanup',
        'timestamp', NOW()
    ),
    updated_at = NOW()
WHERE status IN ('processing', 'pending')
  AND created_at < NOW() - INTERVAL '30 minutes';
```

### **2. Pipeline Dependencies**

**Issue:** Media collection requires web scraping to be completed first

**Current Logic:**
```
Web Scraping → Media Collection → Content Generation
```

**Problem:** If web scraping fails or is incomplete, media collection gets stuck

**Solution:** Check and fix pipeline dependencies:
```sql
-- Find media collection jobs waiting for web scraping
SELECT 
    mc.id as media_job_id,
    mc.tool_id,
    mc.status as media_status,
    ws.status as webscraping_status
FROM ai_generation_jobs mc
LEFT JOIN ai_generation_jobs ws ON mc.tool_id = ws.tool_id AND ws.job_type = 'web_scraping'
WHERE mc.job_type = 'media_collection'
  AND mc.status IN ('pending', 'processing')
  AND (ws.status IS NULL OR ws.status != 'completed');
```

### **3. Memory and Resource Management**

**Current Status:**
- Memory: 28MB/34MB (82% usage)
- Active jobs: 1
- Resources: 0
- Cleanup running every 30 minutes

**Recommendations:**
- Increase cleanup frequency during high load
- Implement job timeout mechanisms
- Add memory monitoring alerts

## 🔄 **Recovery Procedures**

### **Immediate Recovery (Run Now):**

1. **Fix Database Schema:**
   ```sql
   ALTER TABLE tools ADD COLUMN IF NOT EXISTS error_message TEXT;
   ALTER TABLE tools ADD COLUMN IF NOT EXISTS processing_notes TEXT;
   ```

2. **Clean Orphaned Jobs:**
   ```sql
   UPDATE ai_generation_jobs 
   SET status = 'failed'
   WHERE status IN ('processing', 'pending')
     AND created_at < NOW() - INTERVAL '30 minutes';
   ```

3. **Restart Media Collection:**
   - The system should automatically pick up pending jobs
   - Monitor logs for successful processing

### **Verification Steps:**

1. **Check Database Schema:**
   ```bash
   npm run fix:error-column
   ```

2. **Monitor Job Processing:**
   ```bash
   # Check active jobs
   npm run test:bulk-processing
   ```

3. **Check System Health:**
   ```bash
   # Monitor memory and job status
   curl https://dudeai.vercel.app/api/health
   ```

## 📋 **Prevention Measures**

### **1. Database Migrations**
- Always run schema migrations before deploying code changes
- Test migrations in development first
- Keep migration scripts in version control

### **2. Job Monitoring**
- Implement job timeout mechanisms (30 minutes max)
- Add automatic retry logic for transient failures
- Monitor job queue depth and processing times

### **3. Pipeline Resilience**
- Make pipeline stages more independent
- Add fallback mechanisms for missing dependencies
- Implement graceful degradation

## 🎯 **Expected Results After Fix**

### **✅ When Working Correctly:**
- Media collection jobs complete successfully
- No "error_message column" errors in logs
- Job queue processes smoothly
- Memory usage stable
- Pipeline dependencies resolved properly

### **📊 Monitoring Metrics:**
- Job completion rate > 90%
- Average job processing time < 5 minutes
- Memory usage < 80%
- Error rate < 5%

## 🚀 **Quick Fix Commands**

```bash
# 1. Test database schema fix
npm run fix:error-column

# 2. Run comprehensive media collection fix
npm run fix:media-collection

# 3. Test bulk processing system
npm run test:bulk-processing

# 4. Monitor production health
curl https://dudeai.vercel.app/api/health
```

## 📞 **Support Resources**

- **Database Schema**: `supabase/migrations/010_add_error_message_column.sql`
- **Fix Scripts**: `scripts/fix-media-collection-issues.ts`
- **Health Check**: `/api/health` endpoint
- **Job Monitor**: Admin panel → Jobs section

---

**Status**: 🔧 **REQUIRES IMMEDIATE DATABASE SCHEMA FIX**
**Priority**: **HIGH** - Media collection completely broken without schema fix
**ETA**: **5 minutes** after running the SQL migration
