# AI Model Configuration Guide

## Current Model Configuration

The system is currently configured to use **GPT-4o (2024-11-20)** as the primary OpenAI model for content generation.

### About GPT-4.1

**Important Note**: "GPT-4.1" is not a valid OpenAI model identifier. OpenAI uses different naming conventions for their models:

- `gpt-4o` - Latest GPT-4 Omni model
- `gpt-4o-2024-11-20` - Specific dated version of GPT-4o
- `gpt-4o-mini` - Smaller, faster version of GPT-4o
- `gpt-4-turbo` - Previous generation model
- `gpt-4` - Original GPT-4 model

## Current Configuration

### Primary Models
- **OpenAI**: `gpt-4o-2024-11-20` (Latest GPT-4o with 128K context, 16K output)
- **OpenRouter**: `google/gemini-2.5-pro-preview` (1M context, 65K output)

### Model Selection Strategy
The system uses intelligent model selection based on:
1. **Content Size**: Large content uses Gemini for 1M+ token context
2. **Complexity**: Complex tasks prefer Gemini's advanced reasoning
3. **Speed**: Speed-critical tasks use GPT-4o for faster response
4. **Cost**: Cost-conscious processing uses GPT-4o

## How to Update the Model

### Option 1: Use Admin Interface (Recommended)

1. Go to `http://localhost:3000/admin/content/ai-config`
2. Find the OpenAI provider section
3. Update the "Model" field to your desired model
4. Save the configuration

**Available OpenAI Models in Admin Interface:**
- `gpt-4o-2024-11-20` (Current, recommended)
- `gpt-4o` (Latest stable)
- `gpt-4o-mini` (Faster, cheaper)

### Option 2: Update Configuration Files

If you want to change the default model system-wide:

1. **Update Model Types** (`src/lib/ai/types.ts`):
```typescript
export type AIModel = 
  | 'gpt-4o' 
  | 'gpt-4o-2024-11-20'
  | 'gpt-4o-mini'
  // Add new model here if needed
```

2. **Update Default Configuration** (`src/lib/ai/types.ts`):
```typescript
export const OPENAI_CONFIG: AIModelConfig = {
  provider: 'openai',
  model: 'gpt-4o-2024-11-20', // Change this to your preferred model
  maxInputTokens: 128000,
  maxOutputTokens: 16384,
  // ... other config
};
```

3. **Update Model Selector** (`src/lib/ai/model-selector.ts`):
```typescript
// Update all references to the model in the selection logic
model: 'gpt-4o-2024-11-20', // Change to your preferred model
```

### Option 3: Environment Variable Override

You can override the model using environment variables in your `.env.local`:

```env
# Override default OpenAI model
OPENAI_DEFAULT_MODEL=gpt-4o-2024-11-20
```

## Model Capabilities Comparison

| Model | Context Window | Output Tokens | Speed | Cost | Best For |
|-------|----------------|---------------|-------|------|----------|
| `gpt-4o-2024-11-20` | 128K | 16K | Fast | Medium | General content generation |
| `gpt-4o` | 128K | 16K | Fast | Medium | Latest features |
| `gpt-4o-mini` | 128K | 16K | Fastest | Low | Simple tasks, cost optimization |
| `google/gemini-2.5-pro-preview` | 1M+ | 65K | Medium | High | Large content, complex reasoning |

## Recommendations

### For Content Generation
- **Current Setup is Optimal**: `gpt-4o-2024-11-20` provides the best balance of quality, speed, and cost
- **For Large Content**: The system automatically switches to Gemini 2.5 Pro for content >100K tokens
- **For Cost Optimization**: Consider `gpt-4o-mini` for simpler content

### If You Want "Latest" Model
The current `gpt-4o-2024-11-20` **IS** the latest stable GPT-4 model available. OpenAI doesn't have a "GPT-4.1" model.

### Future Model Updates
When OpenAI releases newer models:
1. Add the new model to the `AIModel` type
2. Update the default configuration
3. Test the new model with your content
4. Update the admin interface options

## Testing New Models

Before switching models system-wide:

1. **Test via Admin Interface**: Change model for one provider and test
2. **Monitor Quality**: Check generated content quality
3. **Monitor Performance**: Check response times and error rates
4. **Monitor Costs**: Track API usage and costs

## Troubleshooting

### Model Not Found Error
If you get a "model not found" error:
1. Verify the model identifier is correct
2. Check your OpenAI API key has access to the model
3. Ensure the model is available in your region

### Performance Issues
If the new model is slower:
1. Check the model's documented response times
2. Consider adjusting timeout settings
3. Monitor token usage patterns

## Current Fix Applied

✅ **Fixed NaN Error**: The AI Config page now properly handles NaN values for number inputs
✅ **Improved Validation**: All numeric fields have proper fallback values
✅ **Better UX**: Users can safely edit model configurations without console errors

The system is now ready for model configuration changes through the admin interface at `/admin/content/ai-config`.
