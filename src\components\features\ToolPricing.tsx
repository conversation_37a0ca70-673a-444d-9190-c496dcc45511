'use client';

import React from 'react';
import { DollarSign, CreditCard, Gift } from 'lucide-react';
import { AITool } from '@/lib/types';

interface ToolPricingProps {
  pricing: NonNullable<AITool['pricing']>;
}

// Helper function to get pricing model display
const getPricingModel = (type: string) => {
  const normalizedType = type?.toLowerCase();
  switch (normalizedType) {
    case 'free':
      return 'Free';
    case 'freemium':
      return 'Freemium';
    case 'paid':
      return 'Paid';
    case 'subscription':
      return 'Subscription';
    case 'open source':
      return 'Open Source';
    default:
      return type || 'Unknown'; // Return original value if not recognized
  }
};

// Helper function to get starting price
const getStartingPrice = (pricing: NonNullable<AITool['pricing']>) => {
  if (!pricing.plans || pricing.plans.length === 0) {
    return pricing.type === 'free' ? 'Free' : 'Contact for pricing';
  }

  // Find the lowest price or free plan
  const freePlan = pricing.plans.find(plan => plan.price.toLowerCase().includes('free') || plan.price.includes('$0'));
  if (freePlan) return 'Free';

  // Extract numeric prices and find minimum
  const prices = pricing.plans
    .map(plan => {
      const match = plan.price.match(/\$(\d+(?:\.\d+)?)/);
      return match ? parseFloat(match[1]) : Infinity;
    })
    .filter(price => price !== Infinity);

  if (prices.length > 0) {
    const minPrice = Math.min(...prices);
    return `$${minPrice}/month`;
  }

  return pricing.plans[0].price;
};

// Helper function to get billing frequency
const getBillingFrequency = (pricing: NonNullable<AITool['pricing']>) => {
  if (pricing.type === 'free') return 'One-time';
  if (pricing.type === 'subscription') return 'Monthly';
  return 'Varies';
};

export function ToolPricing({ pricing }: ToolPricingProps) {
  const startingPrice = getStartingPrice(pricing);
  const billingFrequency = getBillingFrequency(pricing);

  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <h3 className="text-xl font-bold text-white mb-6">Pricing</h3>

      {/* Pricing Summary - Horizontal Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">

        {/* Pricing Model */}
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
            <Gift size={16} className="text-white" />
          </div>
          <div>
            <div className="text-gray-400 text-sm">Pricing model</div>
            <div className="text-white font-medium">{getPricingModel(pricing.type)}</div>
          </div>
        </div>

        {/* Starting Price */}
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
            <DollarSign size={16} className="text-white" />
          </div>
          <div>
            <div className="text-gray-400 text-sm">Paid options from</div>
            <div className="text-white font-medium">{startingPrice}</div>
          </div>
        </div>

        {/* Billing Frequency */}
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center flex-shrink-0">
            <CreditCard size={16} className="text-white" />
          </div>
          <div>
            <div className="text-gray-400 text-sm">Billing frequency</div>
            <div className="text-white font-medium">{billingFrequency}</div>
          </div>
        </div>

      </div>

      {/* Detailed Pricing Plans */}
      {pricing.plans && pricing.plans.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-white mb-4">Pricing Plans</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {pricing.plans.map((plan, index) => (
              <div
                key={index}
                className="bg-zinc-700 border border-zinc-600 rounded-lg p-4 hover:bg-zinc-600/50 transition-colors duration-200"
              >
                {/* Plan Header */}
                <div className="mb-4">
                  <h5 className="text-white font-semibold text-lg mb-1">{plan.name}</h5>
                  <div className="text-orange-400 font-bold text-xl">{plan.price}</div>
                </div>

                {/* Plan Features */}
                {plan.features && plan.features.length > 0 && (
                  <div>
                    <div className="text-gray-400 text-sm mb-2">Features included:</div>
                    <ul className="space-y-1">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="text-gray-300 text-sm flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pricing Details/Description */}
      {pricing.details && (
        <div className="mt-6 pt-6 border-t border-zinc-600">
          <h4 className="text-lg font-semibold text-white mb-3">Pricing Details</h4>
          <p className="text-gray-300 text-sm leading-relaxed">{pricing.details}</p>
        </div>
      )}
    </section>
  );
}
