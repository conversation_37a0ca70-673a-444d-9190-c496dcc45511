-- Migration 008: Fix job_type and status constraints for ai_generation_jobs
-- This migration updates both constraints to include all values used by the enhanced job queue system
-- Addresses Error 23514 for 'web_scraping' job type and 'retrying' status

-- =====================================================
-- UPDATE JOB TYPE CONSTRAINT
-- =====================================================

-- Drop existing job_type constraints (handles multiple constraint names)
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    -- Find and drop all job_type constraints
    FOR constraint_record IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'ai_generation_jobs'::regclass 
        AND conname LIKE '%job_type%'
    LOOP
        EXECUTE 'ALTER TABLE ai_generation_jobs DROP CONSTRAINT IF EXISTS ' || constraint_record.conname;
        RAISE NOTICE 'Dropped constraint: %', constraint_record.conname;
    END LOOP;
END $$;

-- Add comprehensive job_type constraint with all TypeScript enum values
ALTER TABLE ai_generation_jobs ADD CONSTRAINT ai_generation_jobs_job_type_check_v3 
CHECK (job_type IN (
    'tool_submission',
    'content_generation', 
    'web_scraping',
    'email_notification',
    'tool_processing',
    'screenshot_capture',
    'favicon_extraction',
    'bulk_processing',
    -- Legacy values for backward compatibility
    'scrape',
    'generate', 
    'bulk',
    'media_extraction'
));

-- =====================================================
-- UPDATE STATUS CONSTRAINT
-- =====================================================

-- Drop existing status constraints
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    -- Find and drop all status constraints
    FOR constraint_record IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'ai_generation_jobs'::regclass 
        AND conname LIKE '%status%'
    LOOP
        EXECUTE 'ALTER TABLE ai_generation_jobs DROP CONSTRAINT IF EXISTS ' || constraint_record.conname;
        RAISE NOTICE 'Dropped constraint: %', constraint_record.conname;
    END LOOP;
END $$;

-- Add comprehensive status constraint with all TypeScript enum values
ALTER TABLE ai_generation_jobs ADD CONSTRAINT ai_generation_jobs_status_check_v2 
CHECK (status IN (
    'pending',
    'processing',
    'completed',
    'failed',
    'retrying',
    'cancelled',
    'paused',
    'stopping',
    'stopped'
));

-- =====================================================
-- VERIFICATION TESTS
-- =====================================================

-- Test job_type constraint with problematic values
DO $$
DECLARE
    test_uuid UUID := gen_random_uuid();
BEGIN
    -- Test web_scraping job type
    INSERT INTO ai_generation_jobs (id, job_type, status, progress, attempts, max_attempts, priority) 
    VALUES (test_uuid, 'web_scraping', 'pending', 0, 0, 3, 1);
    
    RAISE NOTICE 'SUCCESS: web_scraping job type accepted';
    
    -- Clean up test record
    DELETE FROM ai_generation_jobs WHERE id = test_uuid;
    
EXCEPTION
    WHEN check_violation THEN
        RAISE NOTICE 'FAILED: web_scraping job type still blocked: %', SQLERRM;
    WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: Unexpected error testing web_scraping: %', SQLERRM;
END $$;

-- Test status constraint with problematic values
DO $$
DECLARE
    test_uuid UUID := gen_random_uuid();
BEGIN
    -- Create test job
    INSERT INTO ai_generation_jobs (id, job_type, status, progress, attempts, max_attempts, priority) 
    VALUES (test_uuid, 'scrape', 'pending', 0, 0, 3, 1);
    
    -- Test retrying status
    UPDATE ai_generation_jobs SET status = 'retrying' WHERE id = test_uuid;
    
    RAISE NOTICE 'SUCCESS: retrying status accepted';
    
    -- Test other new statuses
    UPDATE ai_generation_jobs SET status = 'paused' WHERE id = test_uuid;
    RAISE NOTICE 'SUCCESS: paused status accepted';
    
    UPDATE ai_generation_jobs SET status = 'stopping' WHERE id = test_uuid;
    RAISE NOTICE 'SUCCESS: stopping status accepted';
    
    UPDATE ai_generation_jobs SET status = 'stopped' WHERE id = test_uuid;
    RAISE NOTICE 'SUCCESS: stopped status accepted';
    
    -- Clean up test record
    DELETE FROM ai_generation_jobs WHERE id = test_uuid;
    
EXCEPTION
    WHEN check_violation THEN
        RAISE NOTICE 'FAILED: Status constraint still blocking: %', SQLERRM;
        -- Clean up on error
        DELETE FROM ai_generation_jobs WHERE id = test_uuid;
    WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: Unexpected error testing status: %', SQLERRM;
        -- Clean up on error
        DELETE FROM ai_generation_jobs WHERE id = test_uuid;
END $$;

-- =====================================================
-- CONSTRAINT DOCUMENTATION
-- =====================================================

COMMENT ON CONSTRAINT ai_generation_jobs_job_type_check_v3 ON ai_generation_jobs 
IS 'Ensures job_type matches TypeScript JobType enum values plus legacy compatibility values';

COMMENT ON CONSTRAINT ai_generation_jobs_status_check_v2 ON ai_generation_jobs 
IS 'Ensures status matches TypeScript JobStatus enum values for complete job lifecycle support';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Show updated constraints
SELECT
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conrelid = 'ai_generation_jobs'::regclass
AND (conname LIKE '%job_type%' OR conname LIKE '%status%')
ORDER BY conname;

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=== MIGRATION 008 COMPLETED ===';
    RAISE NOTICE 'Updated job_type constraint to include all TypeScript enum values:';
    RAISE NOTICE '  • tool_submission, content_generation, web_scraping, email_notification';
    RAISE NOTICE '  • tool_processing, screenshot_capture, favicon_extraction, bulk_processing';
    RAISE NOTICE '  • Legacy: scrape, generate, bulk, media_extraction';
    RAISE NOTICE '';
    RAISE NOTICE 'Updated status constraint to include all TypeScript enum values:';
    RAISE NOTICE '  • pending, processing, completed, failed, retrying';
    RAISE NOTICE '  • cancelled, paused, stopping, stopped';
    RAISE NOTICE '';
    RAISE NOTICE 'Enhanced job queue system can now use all defined job types and statuses';
    RAISE NOTICE 'Critical errors for web_scraping and retrying should now be resolved';
END $$;
