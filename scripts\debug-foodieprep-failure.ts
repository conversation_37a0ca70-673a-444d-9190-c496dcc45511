#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugFoodieprepFailure() {
  console.log('🔍 DEBUGGING FOODIEPREP FAILURE');
  console.log('=' .repeat(60));

  const failedToolId = '3aea56ef-30d3-4933-a05e-fc1274a6ba2a';

  try {
    // 1. Get the tool details
    console.log('\n1️⃣ Getting tool details...');
    const { data: tool, error: toolError } = await supabase
      .from('tools')
      .select('*')
      .eq('id', failedToolId)
      .single();

    if (toolError) {
      console.log('❌ Error fetching tool:', toolError.message);
      return;
    }

    console.log('✅ Tool details:');
    console.log(`   ID: ${tool.id}`);
    console.log(`   Name: ${tool.name}`);
    console.log(`   Website: ${tool.website}`);
    console.log(`   Category ID: ${tool.category_id}`);
    console.log(`   Content Status: ${tool.content_status}`);
    console.log(`   AI Generation Status: ${tool.ai_generation_status}`);

    // 2. Get AI generation jobs for this tool
    console.log('\n2️⃣ Getting AI generation jobs...');
    const { data: jobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .eq('tool_id', failedToolId)
      .order('created_at', { ascending: false });

    if (jobsError) {
      console.log('❌ Error fetching AI jobs:', jobsError.message);
    } else if (jobs && jobs.length > 0) {
      console.log(`✅ Found ${jobs.length} AI generation job(s):`);
      
      jobs.forEach((job, index) => {
        console.log(`\n   Job ${index + 1}:`);
        console.log(`     ID: ${job.id}`);
        console.log(`     Type: ${job.job_type}`);
        console.log(`     Status: ${job.status}`);
        console.log(`     Progress: ${job.progress}%`);
        console.log(`     Created: ${job.created_at}`);
        console.log(`     Started: ${job.started_at}`);
        console.log(`     Completed: ${job.completed_at}`);

        // Check AI responses for category_id
        if (job.ai_responses) {
          console.log(`     AI Responses:`);
          try {
            const responses = typeof job.ai_responses === 'string' 
              ? JSON.parse(job.ai_responses) 
              : job.ai_responses;
            
            if (responses.content && responses.content.category_id) {
              console.log(`       Generated Category ID: ${responses.content.category_id}`);
            } else if (responses.content && responses.content.category_primary) {
              console.log(`       Generated Category Primary: ${responses.content.category_primary}`);
            } else {
              console.log(`       No category_id found in AI response`);
            }

            // Show a sample of the generated content
            if (responses.content) {
              const contentKeys = Object.keys(responses.content);
              console.log(`       Generated fields: ${contentKeys.join(', ')}`);
            }
          } catch (parseError) {
            console.log(`       Error parsing AI responses: ${parseError}`);
          }
        }
      });
    } else {
      console.log('❌ No AI generation jobs found for this tool');
    }

    // 3. Check recent bulk processing jobs that might have included this tool
    console.log('\n3️⃣ Checking recent bulk processing jobs...');
    const { data: bulkJobs, error: bulkError } = await supabase
      .from('bulk_processing_jobs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5);

    if (bulkError) {
      console.log('❌ Error fetching bulk jobs:', bulkError.message);
    } else if (bulkJobs && bulkJobs.length > 0) {
      console.log(`✅ Found ${bulkJobs.length} recent bulk processing job(s):`);
      
      bulkJobs.forEach((job, index) => {
        console.log(`\n   Bulk Job ${index + 1}:`);
        console.log(`     ID: ${job.id}`);
        console.log(`     Status: ${job.status}`);
        console.log(`     Total Items: ${job.total_items}`);
        console.log(`     Processed: ${job.processed_items}`);
        console.log(`     Successful: ${job.successful_items}`);
        console.log(`     Failed: ${job.failed_items}`);
        console.log(`     Created: ${job.created_at}`);
      });
    }

    // 4. Check what categories exist
    console.log('\n4️⃣ Checking available categories...');
    const { data: categories, error: catError } = await supabase
      .from('categories')
      .select('id, title')
      .order('id');

    if (catError) {
      console.log('❌ Error fetching categories:', catError.message);
    } else {
      console.log(`✅ Available categories (${categories?.length || 0}):`);
      categories?.forEach(cat => {
        console.log(`     • ${cat.id} - ${cat.title}`);
      });
    }

    console.log('\n📋 Analysis Summary:');
    console.log('=' .repeat(60));
    console.log('🔍 The tool exists but content generation failed');
    console.log('🔍 Check AI responses above for invalid category_id values');
    console.log('✅ Our fix should prevent this issue in future runs');

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
if (require.main === module) {
  debugFoodieprepFailure().catch(console.error);
}

export { debugFoodieprepFailure };
