'use client';

import React, { useState, useEffect } from 'react';
import {
  Zap,
  Edit,
  Save,
  Copy,
  TestTube,
  Plus,
  Trash2,
  Eye,
  EyeOff,
  RotateCcw
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { PromptEditModal } from '@/components/admin/PromptEditModal';

interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: 'content' | 'description' | 'features' | 'pricing' | 'pros_cons' | 'partial' | 'seo' | 'faqs' | 'releases';
  promptType: 'system' | 'user'; // System prompts for validation/formatting, User prompts for content generation
  template: string;
  variables: string[];
  isActive: boolean;
  lastModified: string;
  usage: number;
  validationRules?: string[]; // For system prompts
  formatRequirements?: string; // For system prompts
}

export default function PromptManagementPage() {
  const [prompts, setPrompts] = useState<PromptTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingPrompt, setEditingPrompt] = useState<PromptTemplate | null>(null);
  const [showTemplate, setShowTemplate] = useState<Record<string, boolean>>({});
  const [testingPrompt, setTestingPrompt] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);

  // Load prompt templates
  useEffect(() => {
    const loadPrompts = async () => {
      try {
        setIsLoading(true);

        // Call real API
        const response = await fetch('/api/admin/prompts', {
          headers: {
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to load prompts');
        }

        const data = await response.json();

        if (data.success) {
          setPrompts(data.data);
        } else {
          throw new Error(data.error || 'Failed to load prompts');
        }

      } catch (err) {
        console.error('Error loading prompts:', err);
        // Set some default prompts if API fails
        setPrompts([
          {
            id: 'default-1',
            name: 'Main Content Generation',
            description: 'Primary template for generating tool descriptions',
            category: 'content',
            promptType: 'user',
            template: `You are ThePornDude, the irreverent and brutally honest reviewer of AI tools. Write a comprehensive review for {toolName} based on the following information:

URL: {toolUrl}
Scraped Content: {scrapedContent}

Write in your signature style:
- Be direct and no-bullshit
- Use humor and personality
- Focus on practical value
- Include both pros and cons
- Keep it engaging and readable

Structure:
1. Hook opening (2-3 sentences)
2. What it does (main features)
3. Why it matters (practical benefits)
4. The good stuff (pros)
5. The not-so-good (cons)
6. Bottom line verdict

Word count: 800-1200 words
Tone: Irreverent, honest, engaging`,
            variables: ['toolName', 'toolUrl', 'scrapedContent'],
            isActive: true,
            lastModified: new Date().toISOString(),
            usage: 0
          }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    loadPrompts();
  }, []);

  const toggleTemplateVisibility = (promptId: string) => {
    setShowTemplate(prev => ({
      ...prev,
      [promptId]: !prev[promptId]
    }));
  };

  const handleEditPrompt = (prompt: PromptTemplate) => {
    setEditingPrompt({ ...prompt });
  };

  const handleSavePrompt = async (prompt: PromptTemplate) => {
    setIsSaving(true);
    try {
      // Call real API to save prompt
      const response = await fetch('/api/admin/prompts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          action: prompt.id.startsWith('default-') ? 'create' : 'update',
          data: prompt
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save prompt');
      }

      const result = await response.json();

      if (result.success) {
        // Update local state with saved prompt
        setPrompts(prev => prev.map(p =>
          p.id === prompt.id
            ? { ...result.data, lastModified: new Date().toISOString() }
            : p
        ));
        setEditingPrompt(null);
      } else {
        throw new Error(result.error || 'Failed to save prompt');
      }

    } catch (err) {
      console.error('Failed to save prompt:', err);
      throw err; // Re-throw to let the modal handle the error
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestPrompt = async (prompt: PromptTemplate) => {
    setIsTesting(true);
    try {
      // Call real API to test prompt
      const response = await fetch('/api/admin/prompts/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          promptId: prompt.id,
          template: prompt.template
        })
      });

      if (!response.ok) {
        throw new Error('Failed to test prompt');
      }

      const result = await response.json();

      if (result.success) {
        // Show test results in a simple alert (could be enhanced with a modal)
        const testData = result.data;
        alert(`Prompt test successful!\n\nResponse time: ${testData.responseTime}ms\nModel: ${testData.metadata.model}\nTokens used: ${testData.metadata.tokensUsed}\n\nAI Response preview:\n${testData.aiResponse.substring(0, 200)}...`);

        // Update usage count in local state
        setPrompts(prev => prev.map(p =>
          p.id === prompt.id
            ? { ...p, usage: p.usage + 1 }
            : p
        ));
      } else {
        throw new Error(result.error || 'Prompt test failed');
      }

    } catch (err) {
      console.error('Prompt test failed:', err);
      throw err; // Re-throw to let the modal handle the error
    } finally {
      setIsTesting(false);
    }
  };

  const handleCopyPrompt = (template: string) => {
    navigator.clipboard.writeText(template);
    // Could add a toast notification here
  };

  const togglePromptStatus = (promptId: string) => {
    setPrompts(prev => prev.map(p => 
      p.id === promptId 
        ? { ...p, isActive: !p.isActive }
        : p
    ));
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'content':
        return 'bg-blue-700 text-blue-200';
      case 'features':
        return 'bg-green-700 text-green-200';
      case 'pros_cons':
        return 'bg-purple-700 text-purple-200';
      case 'description':
        return 'bg-yellow-700 text-yellow-200';
      case 'pricing':
        return 'bg-red-700 text-red-200';
      default:
        return 'bg-gray-700 text-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-400 mt-4">Loading prompt templates...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Prompt Management</h1>
          <p className="text-gray-400">Manage and optimize AI content generation prompts</p>
        </div>
        
        <button className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
          <Plus className="w-4 h-4" />
          <span>New Prompt</span>
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Prompts</p>
              <p className="text-xl font-bold text-white">{prompts.length}</p>
            </div>
            <Zap className="w-6 h-6 text-blue-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active</p>
              <p className="text-xl font-bold text-white">
                {prompts.filter(p => p.isActive).length}
              </p>
            </div>
            <Zap className="w-6 h-6 text-green-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Usage</p>
              <p className="text-xl font-bold text-white">
                {prompts.reduce((acc, p) => acc + p.usage, 0)}
              </p>
            </div>
            <TestTube className="w-6 h-6 text-purple-400" />
          </div>
        </div>
        
        <div className="bg-zinc-800 border border-black rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Categories</p>
              <p className="text-xl font-bold text-white">
                {new Set(prompts.map(p => p.category)).size}
              </p>
            </div>
            <Edit className="w-6 h-6 text-yellow-400" />
          </div>
        </div>
      </div>

      {/* Prompts List */}
      <div className="space-y-4">
        {prompts.map((prompt) => (
          <div key={prompt.id} className="bg-zinc-800 border border-black rounded-lg p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className="text-lg font-semibold text-white">{prompt.name}</h3>
                  <span className={`text-xs px-2 py-1 rounded ${getCategoryColor(prompt.category)}`}>
                    {prompt.category.replace('_', ' ')}
                  </span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    prompt.isActive ? 'bg-green-700 text-green-200' : 'bg-gray-700 text-gray-200'
                  }`}>
                    {prompt.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                
                <p className="text-gray-400 mb-3">{prompt.description}</p>
                
                <div className="flex items-center space-x-6 text-sm text-gray-400">
                  <span>Variables: {prompt.variables.join(', ')}</span>
                  <span>Usage: {prompt.usage}</span>
                  <span>Modified: {new Date(prompt.lastModified).toLocaleDateString()}</span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => toggleTemplateVisibility(prompt.id)}
                  className="text-gray-400 hover:text-white transition-colors p-2"
                  title={showTemplate[prompt.id] ? "Hide template" : "Show template"}
                >
                  {showTemplate[prompt.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
                
                <button
                  onClick={() => handleCopyPrompt(prompt.template)}
                  className="text-gray-400 hover:text-white transition-colors p-2"
                  title="Copy template"
                >
                  <Copy className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => handleTestPrompt(prompt.id)}
                  disabled={testingPrompt === prompt.id}
                  className="flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 disabled:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
                >
                  {testingPrompt === prompt.id ? (
                    <>
                      <LoadingSpinner size="sm" />
                      <span>Testing...</span>
                    </>
                  ) : (
                    <>
                      <TestTube className="w-4 h-4" />
                      <span>Test</span>
                    </>
                  )}
                </button>
                
                <button
                  onClick={() => handleEditPrompt(prompt)}
                  className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
                >
                  <Edit className="w-4 h-4" />
                  <span>Edit</span>
                </button>
                
                <button
                  onClick={() => togglePromptStatus(prompt.id)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm ${
                    prompt.isActive 
                      ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                      : 'bg-green-700 hover:bg-green-600 text-white'
                  }`}
                >
                  {prompt.isActive ? 'Deactivate' : 'Activate'}
                </button>
              </div>
            </div>
            
            {/* Template Display */}
            {showTemplate[prompt.id] && (
              <div className="mt-4 p-4 bg-zinc-700 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-300">Template</h4>
                  <button
                    onClick={() => handleCopyPrompt(prompt.template)}
                    className="text-gray-400 hover:text-white transition-colors"
                    title="Copy template"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
                <pre className="text-sm text-gray-300 whitespace-pre-wrap overflow-x-auto">
                  {prompt.template}
                </pre>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Enhanced Edit Modal */}
      {editingPrompt && (
        <PromptEditModal
          prompt={editingPrompt}
          onSave={handleSavePrompt}
          onClose={() => setEditingPrompt(null)}
          onTest={handleTestPrompt}
          isLoading={isSaving}
          isTesting={isTesting}
        />
      )}
    </div>
  );
}
