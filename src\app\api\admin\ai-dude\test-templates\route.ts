import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { PromptManager } from '@/lib/ai/prompt-manager';
import { AIContentGenerator } from '@/lib/ai/content-generator';

/**
 * POST /api/admin/ai-dude/test-templates
 * Test the new AI Dude template structure integration
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, testData } = body;

    const results: any = {
      action,
      timestamp: new Date().toISOString(),
      results: {}
    };

    switch (action) {
      case 'test_complete_generation_templates':
        // Test complete content generation template structure
        const testScrapedContent = testData?.scrapedContent || `# TestTool
A revolutionary AI tool for testing purposes.

## Features
- Advanced testing capabilities
- Real-time validation
- User-friendly interface

## Pricing
Free tier available with premium options.`;

        const testToolUrl = testData?.toolUrl || 'https://testtool.com';

        // Test system prompt generation
        const databaseSchema = await PromptManager.getAIDudeDatabaseSchema();
        const systemPrompt = await PromptManager.buildAIDudeSystemPrompt(databaseSchema);

        // Test user prompt generation
        const userPrompt = await PromptManager.buildAIDudeUserPrompt(testScrapedContent, testToolUrl);

        results.results.completeGeneration = {
          systemPromptLength: systemPrompt.length,
          userPromptLength: userPrompt.length,
          systemPromptContainsSchema: systemPrompt.includes('{DATABASE_SCHEMA}') ? 'REPLACED' : 'CONTAINS_PLACEHOLDER',
          userPromptContainsUrl: userPrompt.includes(testToolUrl),
          userPromptContainsContent: userPrompt.includes('TestTool'),
          systemPromptSample: systemPrompt.substring(0, 200) + '...',
          userPromptSample: userPrompt.substring(0, 200) + '...'
        };
        break;

      case 'test_partial_generation_templates':
        const sectionType = testData?.sectionType || 'features';
        const existingToolData = testData?.existingToolData || {
          name: 'TestTool',
          description: 'A test tool',
          features: ['Old feature 1', 'Old feature 2']
        };
        const scrapedContent = testData?.scrapedContent || 'New scraped content about features';
        const toolUrl = testData?.toolUrl || 'https://testtool.com';

        // Test partial system prompt generation
        const partialSystemPrompt = await PromptManager.buildPartialSystemPrompt(
          sectionType,
          PromptManager.getSectionRequirements(sectionType)
        );

        // Test partial user prompt generation
        const partialUserPrompt = await PromptManager.buildPartialUserPrompt(
          existingToolData,
          scrapedContent,
          toolUrl,
          sectionType
        );

        results.results.partialGeneration = {
          sectionType,
          systemPromptLength: partialSystemPrompt.length,
          userPromptLength: partialUserPrompt.length,
          systemPromptContainsSectionType: partialSystemPrompt.includes(sectionType),
          userPromptContainsExistingData: partialUserPrompt.includes('TestTool'),
          userPromptContainsScrapedContent: partialUserPrompt.includes(scrapedContent),
          systemPromptSample: partialSystemPrompt.substring(0, 200) + '...',
          userPromptSample: partialUserPrompt.substring(0, 200) + '...'
        };
        break;

      case 'test_template_database_loading':
        // Test that templates are properly loaded from database
        const templateTests = [];

        // Test complete system template
        try {
          const systemTemplate = await PromptManager.buildAIDudeSystemPrompt({test: 'schema'});
          templateTests.push({
            template: 'prompt_ai_dude_complete_system',
            loaded: systemTemplate.length > 100,
            containsSchema: systemTemplate.includes('{"test":"schema"}'),
            error: null
          });
        } catch (error) {
          templateTests.push({
            template: 'prompt_ai_dude_complete_system',
            loaded: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }

        // Test complete user template
        try {
          const userTemplate = await PromptManager.buildAIDudeUserPrompt('test content', 'https://test.com');
          templateTests.push({
            template: 'prompt_ai_dude_complete_user',
            loaded: userTemplate.length > 10,
            containsUrl: userTemplate.includes('https://test.com'),
            containsContent: userTemplate.includes('test content'),
            error: null
          });
        } catch (error) {
          templateTests.push({
            template: 'prompt_ai_dude_complete_user',
            loaded: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }

        // Test partial system template
        try {
          const partialTemplate = await PromptManager.buildPartialSystemPrompt('features', 'Test requirements');
          templateTests.push({
            template: 'prompt_ai_dude_partial_system',
            loaded: partialTemplate.length > 100,
            containsSectionType: partialTemplate.includes('features'),
            containsRequirements: partialTemplate.includes('Test requirements'),
            error: null
          });
        } catch (error) {
          templateTests.push({
            template: 'prompt_ai_dude_partial_system',
            loaded: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }

        results.results.templateDatabaseLoading = {
          totalTests: templateTests.length,
          passedTests: templateTests.filter(t => t.loaded).length,
          tests: templateTests
        };
        break;

      case 'test_content_generator_integration':
        // Test that ContentGenerator works with new template structure
        const generator = new AIContentGenerator();
        
        try {
          // Test complete generation (mock - don't actually call AI)
          const testContent = 'Test content for generation';
          const testUrl = 'https://testintegration.com';
          
          // This will test the prompt building but we'll catch before AI call
          const testDatabaseSchema = await PromptManager.getAIDudeDatabaseSchema();
          const systemPromptTest = await PromptManager.buildAIDudeSystemPrompt(testDatabaseSchema);
          const userPromptTest = await PromptManager.buildAIDudeUserPrompt(testContent, testUrl);

          results.results.contentGeneratorIntegration = {
            systemPromptReady: systemPromptTest.length > 100,
            userPromptReady: userPromptTest.length > 10,
            promptsProperlyFormatted: true,
            integrationWorking: true,
            error: null
          };
        } catch (error) {
          results.results.contentGeneratorIntegration = {
            integrationWorking: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
        break;

      default:
        throw new Error(`Unknown test action: ${action}`);
    }

    return NextResponse.json({
      success: true,
      data: results
    });

  } catch (error: any) {
    console.error('AI Dude template test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Template test failed' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/ai-dude/test-templates
 * Get available template tests
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const availableTests = [
      'test_complete_generation_templates',
      'test_partial_generation_templates', 
      'test_template_database_loading',
      'test_content_generator_integration'
    ];

    return NextResponse.json({
      success: true,
      data: {
        message: 'AI Dude template integration tests available',
        availableTests,
        usage: 'POST with { "action": "test_name", "testData": {...} }',
        templateStructure: {
          systemPrompts: [
            'prompt_ai_dude_complete_system',
            'prompt_ai_dude_partial_system',
            'prompt_ai_dude_validation'
          ],
          userPrompts: [
            'prompt_ai_dude_complete_user',
            'prompt_ai_dude_partial_context',
            'prompt_ai_dude_features',
            'prompt_ai_dude_pricing',
            'prompt_ai_dude_pros_cons',
            'prompt_ai_dude_seo',
            'prompt_ai_dude_faqs',
            'prompt_ai_dude_releases'
          ]
        }
      }
    });

  } catch (error: any) {
    console.error('AI Dude template test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to get test status' 
      },
      { status: 500 }
    );
  }
}
