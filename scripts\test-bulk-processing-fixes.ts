#!/usr/bin/env tsx

/**
 * Test Bulk Processing Fixes
 * 
 * This script tests both critical fixes:
 * 1. Job type constraint fix (tool_processing allowed)
 * 2. Version mismatch resolution in atomic functions
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function testJobTypeConstraint() {
  console.log('🔍 Testing Job Type Constraint Fix...');
  console.log('-'.repeat(50));

  try {
    // Test 1: Try to create a job with 'tool_processing' type
    console.log('1️⃣ Testing tool_processing job type creation...');
    
    const testJobId = generateUUID();
    const testJob = {
      id: testJobId,
      job_type: 'tool_processing',
      status: 'pending',
      progress: 0,
      attempts: 0,
      max_attempts: 3,
      priority: 1,
      tags: ['test', 'constraint-fix'],
      can_pause: true,
      can_resume: false,
      can_stop: true
    };

    const { error: insertError } = await supabase
      .from('ai_generation_jobs')
      .insert(testJob);

    if (insertError) {
      console.log('❌ Job creation failed:', insertError.message);
      if (insertError.message.includes('violates check constraint')) {
        console.log('   This indicates Migration 007 has not been applied yet.');
        console.log('   Please execute the migration SQL in Supabase first.');
        return false;
      }
      return false;
    }

    console.log('✅ Job with tool_processing type created successfully');

    // Test 2: Verify the job was stored correctly
    const { data: retrievedJob, error: fetchError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .eq('id', testJobId)
      .single();

    if (fetchError) {
      console.log('❌ Could not retrieve created job:', fetchError.message);
    } else {
      console.log('✅ Job retrieved successfully:');
      console.log(`   ID: ${retrievedJob.id}`);
      console.log(`   Type: ${retrievedJob.job_type}`);
      console.log(`   Status: ${retrievedJob.status}`);
      console.log(`   Priority: ${retrievedJob.priority}`);
    }

    // Test 3: Test other valid job types still work
    console.log('2️⃣ Testing other valid job types...');
    
    const otherJobTypes = ['scrape', 'generate', 'bulk', 'media_extraction'];
    
    for (const jobType of otherJobTypes) {
      const otherTestJobId = generateUUID();
      const { error } = await supabase
        .from('ai_generation_jobs')
        .insert({
          id: otherTestJobId,
          job_type: jobType,
          status: 'pending',
          progress: 0,
          attempts: 0,
          max_attempts: 3,
          priority: 1
        });

      if (error) {
        console.log(`❌ Job type '${jobType}' failed:`, error.message);
      } else {
        console.log(`✅ Job type '${jobType}' works correctly`);
        // Clean up
        await supabase.from('ai_generation_jobs').delete().eq('id', otherTestJobId);
      }
    }

    // Test 4: Test invalid job type is rejected
    console.log('3️⃣ Testing invalid job type rejection...');
    
    const invalidJobId = generateUUID();
    const { error: invalidError } = await supabase
      .from('ai_generation_jobs')
      .insert({
        id: invalidJobId,
        job_type: 'invalid_type',
        status: 'pending'
      });

    if (invalidError && invalidError.message.includes('violates check constraint')) {
      console.log('✅ Invalid job type correctly rejected');
    } else {
      console.log('⚠️  Invalid job type was not rejected as expected');
    }

    // Cleanup
    await supabase.from('ai_generation_jobs').delete().eq('id', testJobId);
    console.log('✅ Test jobs cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Job type constraint test failed:', error);
    return false;
  }
}

async function testVersionMismatchFix() {
  console.log('\n🔍 Testing Version Mismatch Fix...');
  console.log('-'.repeat(50));

  try {
    // Create a test bulk job
    console.log('1️⃣ Creating test bulk job...');
    
    const testBulkJobId = generateUUID();
    const testBulkJob = {
      id: testBulkJobId,
      job_type: 'manual_entry',
      status: 'pending',
      total_items: 2,
      processed_items: 0,
      successful_items: 0,
      failed_items: 0,
      version: 1,
      source_data: [
        { url: 'https://example.com/test1' },
        { url: 'https://example.com/test2' }
      ],
      processing_options: { test: true },
      created_by: 'version-mismatch-test',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: insertError } = await supabase
      .from('bulk_processing_jobs')
      .insert(testBulkJob);

    if (insertError) {
      console.log('❌ Failed to create test bulk job:', insertError.message);
      return false;
    }

    console.log(`✅ Created test bulk job: ${testBulkJobId}`);

    // Test atomic function calls
    console.log('2️⃣ Testing atomic function calls...');
    
    // Test status update
    const { data: statusResult, error: statusError } = await supabase.rpc('update_bulk_job_status_atomic', {
      p_job_id: testBulkJobId,
      p_new_status: 'processing',
      p_expected_version: 1
    });

    if (statusError) {
      console.log('❌ Status update failed:', statusError.message);
      return false;
    }

    if (statusResult && statusResult.success) {
      console.log('✅ Status update successful');
      console.log(`   New version: ${statusResult.new_version}`);
    } else {
      console.log('❌ Status update returned error:', statusResult?.error);
      return false;
    }

    // Test progress update with correct version
    const { data: progressResult, error: progressError } = await supabase.rpc('update_bulk_job_progress_atomic', {
      p_job_id: testBulkJobId,
      p_processed_items: 1,
      p_successful_items: 1,
      p_failed_items: 0,
      p_expected_version: statusResult.new_version
    });

    if (progressError) {
      console.log('❌ Progress update failed:', progressError.message);
      return false;
    }

    if (progressResult && progressResult.success) {
      console.log('✅ Progress update successful');
      console.log(`   New version: ${progressResult.new_version}`);
    } else {
      console.log('❌ Progress update returned error:', progressResult?.error);
      return false;
    }

    // Test intentional version mismatch
    console.log('3️⃣ Testing version mismatch detection...');
    
    const { data: mismatchResult } = await supabase.rpc('update_bulk_job_status_atomic', {
      p_job_id: testBulkJobId,
      p_new_status: 'completed',
      p_expected_version: 999 // Wrong version
    });

    if (mismatchResult && mismatchResult.error === 'version_mismatch') {
      console.log('✅ Version mismatch correctly detected');
      console.log(`   Expected: 999, Current: ${mismatchResult.current_version}`);
    } else {
      console.log('⚠️  Version mismatch detection may not be working correctly');
    }

    // Cleanup
    await supabase.from('bulk_processing_jobs').delete().eq('id', testBulkJobId);
    console.log('✅ Test bulk job cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Version mismatch test failed:', error);
    return false;
  }
}

async function testCompleteWorkflow() {
  console.log('\n🔍 Testing Complete Bulk Processing Workflow...');
  console.log('-'.repeat(50));

  try {
    console.log('1️⃣ Simulating bulk processing job creation...');
    
    // This would normally be done by the bulk processing engine
    const bulkJobId = generateUUID();
    const individualJobId = generateUUID();

    // Create bulk job
    const { error: bulkError } = await supabase
      .from('bulk_processing_jobs')
      .insert({
        id: bulkJobId,
        job_type: 'manual_entry',
        status: 'processing',
        total_items: 1,
        processed_items: 0,
        successful_items: 0,
        failed_items: 0,
        version: 1,
        source_data: [{ url: 'https://example.com/workflow-test' }],
        processing_options: { test: true },
        created_by: 'workflow-test'
      });

    if (bulkError) {
      console.log('❌ Bulk job creation failed:', bulkError.message);
      return false;
    }

    // Create individual AI generation job (this was failing before)
    const { error: individualError } = await supabase
      .from('ai_generation_jobs')
      .insert({
        id: individualJobId,
        job_type: 'tool_processing', // This should now work
        status: 'pending',
        progress: 0,
        attempts: 0,
        max_attempts: 3,
        priority: 1,
        job_data: {
          url: 'https://example.com/workflow-test',
          bulkJobId: bulkJobId
        },
        tags: ['bulk-processing', 'workflow-test']
      });

    if (individualError) {
      console.log('❌ Individual job creation failed:', individualError.message);
      return false;
    }

    console.log('✅ Both bulk and individual jobs created successfully');

    // Simulate processing progress
    console.log('2️⃣ Simulating processing progress...');
    
    // Update individual job to processing
    await supabase
      .from('ai_generation_jobs')
      .update({ status: 'processing', progress: 50 })
      .eq('id', individualJobId);

    // Update bulk job progress
    const { data: progressUpdate } = await supabase.rpc('update_bulk_job_progress_atomic', {
      p_job_id: bulkJobId,
      p_processed_items: 1,
      p_successful_items: 1,
      p_failed_items: 0,
      p_expected_version: 1
    });

    if (progressUpdate && progressUpdate.success) {
      console.log('✅ Bulk job progress updated successfully');
    } else {
      console.log('❌ Bulk job progress update failed:', progressUpdate?.error);
    }

    // Complete individual job
    await supabase
      .from('ai_generation_jobs')
      .update({ status: 'completed', progress: 100 })
      .eq('id', individualJobId);

    // Complete bulk job
    const { data: completion } = await supabase.rpc('complete_bulk_job_atomic', {
      p_job_id: bulkJobId,
      p_expected_version: progressUpdate?.new_version || 2
    });

    if (completion && completion.success) {
      console.log('✅ Bulk job completed successfully');
    } else {
      console.log('❌ Bulk job completion failed:', completion?.error);
    }

    // Cleanup
    await supabase.from('ai_generation_jobs').delete().eq('id', individualJobId);
    await supabase.from('bulk_processing_jobs').delete().eq('id', bulkJobId);
    console.log('✅ Workflow test jobs cleaned up');

    return true;

  } catch (error) {
    console.error('❌ Complete workflow test failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Testing Bulk Processing Critical Fixes...');
  console.log('=' .repeat(70));

  const results = {
    jobTypeConstraint: false,
    versionMismatch: false,
    completeWorkflow: false
  };

  // Run all tests
  results.jobTypeConstraint = await testJobTypeConstraint();
  results.versionMismatch = await testVersionMismatchFix();
  results.completeWorkflow = await testCompleteWorkflow();

  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('=' .repeat(70));
  console.log(`Job Type Constraint Fix:       ${results.jobTypeConstraint ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Version Mismatch Fix:          ${results.versionMismatch ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Complete Workflow Test:        ${results.completeWorkflow ? '✅ PASSED' : '❌ FAILED'}`);

  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 All critical fixes verified successfully!');
    console.log('   • Job type constraint allows "tool_processing"');
    console.log('   • Atomic functions work without version mismatch errors');
    console.log('   • Complete bulk processing workflow functional');
    console.log('');
    console.log('🚀 The bulk processing system is now fully operational!');
  } else {
    console.log('\n❌ Some critical issues remain:');
    if (!results.jobTypeConstraint) {
      console.log('   • Job type constraint fix not applied (run Migration 007)');
    }
    if (!results.versionMismatch) {
      console.log('   • Version mismatch errors persist (check function cleanup)');
    }
    if (!results.completeWorkflow) {
      console.log('   • Complete workflow still has issues');
    }
    console.log('');
    console.log('📋 Required actions:');
    console.log('   1. Execute Migration 007 SQL in Supabase');
    console.log('   2. Execute function cleanup SQL in Supabase');
    console.log('   3. Verify all database changes are applied correctly');
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

export { main as testBulkProcessingFixes };
