-- Migration 008: Add scraped_content table for storing web scraping results
-- This table stores the raw scraped content from websites for AI processing

CREATE TABLE IF NOT EXISTS scraped_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL,
    content TEXT NOT NULL,
    success BOOLEAN NOT NULL DEFAULT true,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    credits_used INTEGER DEFAULT 0,
    metadata JSONB,
    
    -- Additional useful fields
    content_length INTEGER GENERATED ALWAYS AS (length(content)) STORED,
    domain TEXT GENERATED ALWAYS AS (
        CASE 
            WHEN url ~ '^https?://([^/]+)' THEN 
                substring(url from '^https?://([^/]+)')
            ELSE 'unknown'
        END
    ) STORED,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_scraped_content_url ON scraped_content(url);
CREATE INDEX IF NOT EXISTS idx_scraped_content_domain ON scraped_content(domain);
CREATE INDEX IF NOT EXISTS idx_scraped_content_timestamp ON scraped_content(timestamp);
CREATE INDEX IF NOT EXISTS idx_scraped_content_success ON scraped_content(success);
CREATE INDEX IF NOT EXISTS idx_scraped_content_created_at ON scraped_content(created_at);

-- Create a partial index for successful scrapes only (most common query)
CREATE INDEX IF NOT EXISTS idx_scraped_content_successful ON scraped_content(url, timestamp) 
WHERE success = true;

-- Add RLS (Row Level Security) policies
ALTER TABLE scraped_content ENABLE ROW LEVEL SECURITY;

-- Policy to allow service role full access (this is the key fix)
CREATE POLICY "Service role can manage scraped content" ON scraped_content
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role' OR auth.role() = 'service_role');

-- Policy to allow authenticated users to read scraped content
CREATE POLICY "Authenticated users can read scraped content" ON scraped_content
    FOR SELECT USING (auth.role() = 'authenticated');

-- Alternative: Disable RLS for this table since it's for internal scraping data
-- ALTER TABLE scraped_content DISABLE ROW LEVEL SECURITY;

-- Add a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_scraped_content_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_scraped_content_updated_at
    BEFORE UPDATE ON scraped_content
    FOR EACH ROW
    EXECUTE FUNCTION update_scraped_content_updated_at();

-- Add comments for documentation
COMMENT ON TABLE scraped_content IS 'Stores raw scraped content from websites for AI processing';
COMMENT ON COLUMN scraped_content.url IS 'The URL that was scraped';
COMMENT ON COLUMN scraped_content.content IS 'The scraped content in markdown format';
COMMENT ON COLUMN scraped_content.success IS 'Whether the scraping operation was successful';
COMMENT ON COLUMN scraped_content.timestamp IS 'When the content was scraped';
COMMENT ON COLUMN scraped_content.credits_used IS 'Number of scraping credits consumed';
COMMENT ON COLUMN scraped_content.metadata IS 'Additional metadata about the scraping operation (JSON)';
COMMENT ON COLUMN scraped_content.content_length IS 'Length of the scraped content (auto-calculated)';
COMMENT ON COLUMN scraped_content.domain IS 'Domain extracted from the URL (auto-calculated)';
