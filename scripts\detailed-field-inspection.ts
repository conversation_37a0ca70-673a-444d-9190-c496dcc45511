import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function detailedFieldInspection() {
  console.log('🔍 DETAILED FIELD INSPECTION');
  console.log('=' .repeat(70));
  
  // Get the specific tool that was analyzed
  const { data: tool, error } = await supabaseAdmin
    .from('tools')
    .select('*')
    .eq('id', '84e9f98d-2d29-4cb5-8ac8-9ecc39c5d9ca')
    .single();
    
  if (error || !tool) {
    console.log('❌ Could not fetch tool for detailed inspection');
    return;
  }
  
  console.log(`\n📋 Inspecting Tool: ${tool.name}`);
  console.log(`   ID: ${tool.id}`);
  console.log(`   Website: ${tool.website}`);
  console.log(`   Submission Source: ${tool.submission_source}`);
  
  // Inspect the generated_content JSONB field
  console.log('\n🔍 GENERATED CONTENT ANALYSIS:');
  if (tool.generated_content) {
    console.log('✅ generated_content field exists');
    console.log('Raw generated_content structure:');
    console.log(JSON.stringify(tool.generated_content, null, 2));
    
    // Check if the content is stored in generated_content but not parsed to individual fields
    const contentInJsonb = tool.generated_content.content || tool.generated_content;
    
    console.log('\n🔍 CONTENT WITHIN GENERATED_CONTENT:');
    if (contentInJsonb) {
      console.log('Fields found in generated_content.content:');
      Object.keys(contentInJsonb).forEach(key => {
        const value = contentInJsonb[key];
        const type = Array.isArray(value) ? 'array' : typeof value;
        const preview = JSON.stringify(value)?.substring(0, 100) + '...';
        console.log(`   ${key}: ${type} - ${preview}`);
      });
    }
  } else {
    console.log('❌ generated_content field is missing');
  }
  
  // Check problematic fields specifically
  console.log('\n🔍 PROBLEMATIC FIELDS ANALYSIS:');
  
  const problematicFields = ['category_id', 'subcategory', 'tooltip'];
  
  problematicFields.forEach(field => {
    const value = tool[field];
    console.log(`\n📋 ${field}:`);
    console.log(`   Database value: ${JSON.stringify(value)}`);
    console.log(`   Type: ${value === null ? 'null' : typeof value}`);
    
    // Check if it exists in generated_content
    if (tool.generated_content?.content?.[field]) {
      console.log(`   ✅ Found in generated_content.content: ${JSON.stringify(tool.generated_content.content[field])}`);
      console.log(`   🔧 ISSUE: Field exists in JSONB but not parsed to individual column`);
    } else if (tool.generated_content?.[field]) {
      console.log(`   ✅ Found in generated_content root: ${JSON.stringify(tool.generated_content[field])}`);
      console.log(`   🔧 ISSUE: Field exists in JSONB but not parsed to individual column`);
    } else {
      // Check for alternative field names
      const alternativeNames = {
        category_id: ['main_category', 'category_primary', 'category', 'categoryId'],
        subcategory: ['sub_category', 'category_secondary', 'categorySecondary'],
        tooltip: ['shortDescription', 'tagline', 'brief']
      };
      
      const alternatives = alternativeNames[field as keyof typeof alternativeNames] || [];
      let found = false;
      
      alternatives.forEach(altName => {
        if (tool.generated_content?.content?.[altName]) {
          console.log(`   ⚠️ Found as '${altName}' in generated_content: ${JSON.stringify(tool.generated_content.content[altName])}`);
          console.log(`   🔧 ISSUE: Field mapping problem - '${altName}' not mapped to '${field}'`);
          found = true;
        }
      });
      
      if (!found) {
        console.log(`   ❌ Not found in generated_content either`);
        console.log(`   🔧 ISSUE: AI not generating this field or field completely missing`);
      }
    }
  });
  
  // Check successful fields to understand the pattern
  console.log('\n🔍 SUCCESSFUL FIELDS PATTERN:');
  const successfulFields = ['name', 'description', 'features', 'pricing', 'pros_and_cons'];
  
  successfulFields.forEach(field => {
    const value = tool[field];
    console.log(`\n📋 ${field}:`);
    console.log(`   Database value type: ${Array.isArray(value) ? 'array' : typeof value}`);
    console.log(`   Database value length: ${JSON.stringify(value)?.length || 0} chars`);
    
    if (tool.generated_content?.content?.[field]) {
      console.log(`   ✅ Also in generated_content.content`);
    }
  });
  
  // Analyze the content generation metadata
  console.log('\n🔍 GENERATION METADATA ANALYSIS:');
  if (tool.generated_content?.metadata) {
    console.log('Generation metadata:');
    console.log(JSON.stringify(tool.generated_content.metadata, null, 2));
  }
  
  if (tool.generated_content?.generated_content) {
    console.log('Nested generated_content metadata:');
    console.log(JSON.stringify(tool.generated_content.generated_content, null, 2));
  }
  
  // Check if there's a pattern in how fields are stored
  console.log('\n🔍 STORAGE PATTERN ANALYSIS:');
  console.log('All non-null fields in tool record:');
  Object.keys(tool).forEach(key => {
    if (tool[key] !== null && tool[key] !== undefined && tool[key] !== '') {
      const type = Array.isArray(tool[key]) ? 'array' : typeof tool[key];
      const length = JSON.stringify(tool[key])?.length || 0;
      console.log(`   ${key}: ${type} (${length} chars)`);
    }
  });
}

async function checkFieldMappingLogic() {
  console.log('\n🔧 FIELD MAPPING LOGIC ANALYSIS');
  console.log('=' .repeat(70));
  
  // This would require examining the actual PromptManager.processAIDudeResponse logic
  console.log('📋 Checking PromptManager.processAIDudeResponse mapping...');
  
  // Simulate the mapping logic to identify issues
  const mockAIResponse = {
    name: 'Test Tool',
    description: 'Test description',
    category_primary: 'AI Tools',
    category_secondary: 'Content Generation',
    tooltip: 'AI-powered content generator',
    features: ['Feature 1', 'Feature 2'],
    pricing: { type: 'Freemium', plans: [] },
    pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] }
  };
  
  console.log('\n📋 Mock AI Response:');
  console.log(JSON.stringify(mockAIResponse, null, 2));
  
  // Simulate the mapping
  const mapped = {
    name: mockAIResponse.name || '',
    description: mockAIResponse.description || '',
    category_id: mockAIResponse.main_category || mockAIResponse.category_primary || null,  // This mapping should work
    subcategory: mockAIResponse.sub_category || mockAIResponse.category_secondary || null, // This mapping should work
    tooltip: mockAIResponse.tooltip || '',  // This should work if AI generates it
    features: mockAIResponse.features || [],
    pricing: mockAIResponse.pricing || { type: 'unknown', plans: [] },
    pros_and_cons: mockAIResponse.pros_and_cons || { pros: [], cons: [] }
  };
  
  console.log('\n📋 Mapped Result:');
  console.log(JSON.stringify(mapped, null, 2));
  
  console.log('\n🔧 MAPPING ANALYSIS:');
  console.log('✅ category_id mapping: main_category → category_id (should work)');
  console.log('✅ subcategory mapping: sub_category → subcategory (should work)');
  console.log('✅ tooltip mapping: tooltip → tooltip (should work if AI generates it)');

  console.log('\n🎯 LIKELY ISSUES:');
  console.log('1. AI might not be generating main_category/sub_category fields');
  console.log('2. AI might not be generating tooltip field');
  console.log('3. Field mapping logic might have bugs');
  console.log('4. Content might be stored in generated_content but not parsed to individual columns');
}

async function runDetailedInspection() {
  try {
    await detailedFieldInspection();
    await checkFieldMappingLogic();
    
    console.log('\n🎯 DETAILED INSPECTION COMPLETE');
    console.log('=' .repeat(70));
    console.log('🔧 KEY FINDINGS:');
    console.log('1. Check if problematic fields exist in generated_content JSONB');
    console.log('2. Verify AI prompt templates include all required fields');
    console.log('3. Review PromptManager.processAIDudeResponse mapping logic');
    console.log('4. Test if content generation pipeline parses JSONB to individual columns');
    
  } catch (error) {
    console.error('❌ Detailed inspection failed:', error);
  }
}

runDetailedInspection().catch(console.error);
