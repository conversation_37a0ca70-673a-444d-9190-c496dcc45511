/**
 * Content Validation System
 * 
 * Validates generated content against database schema requirements,
 * content standards, and quality guidelines.
 */

import { ValidationResult, ValidationError, ContentStandards } from '../types';

export interface ValidationRule {
  field: string;
  type: 'required' | 'length' | 'format' | 'enum' | 'array' | 'custom';
  params?: any;
  message: string;
  severity: 'error' | 'warning';
}

export interface SchemaValidationConfig {
  requiredFields: string[];
  fieldValidations: ValidationRule[];
  contentStandards: ContentStandards;
}

export class ContentValidator {
  private validationRules: ValidationRule[];
  private contentStandards: ContentStandards;

  constructor() {
    this.contentStandards = {
      minDescriptionLength: 150,
      maxDescriptionLength: 2000,
      minFeaturesCount: 3,
      maxFeaturesCount: 8,
      minProsConsCount: 3,
      maxProsConsCount: 10,
      requiredFields: [
        'detailed_description',
        'features',
        'pricing',
        'pros_and_cons'
      ],
      bannedWords: [
        'placeholder',
        'lorem ipsum',
        'test content',
        'coming soon'
      ],
      validPricingTypes: ['Free', 'Paid', 'Freemium', 'Open Source']
    };

    this.validationRules = this.buildValidationRules();
  }

  /**
   * Main content validation method
   */
  async validateContent(
    content: any,
    originalContent: string,
    toolUrl: string
  ): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: string[] = [];
    
    try {
      // Schema validation
      const schemaErrors = await this.validateSchema(content);
      errors.push(...schemaErrors);

      // Content quality validation
      const qualityWarnings = await this.validateContentQuality(content);
      warnings.push(...qualityWarnings);

      // Format validation
      const formatErrors = await this.validateFormat(content);
      errors.push(...formatErrors);

      // Business rules validation
      const businessErrors = await this.validateBusinessRules(content, originalContent);
      errors.push(...businessErrors);

      // Calculate overall score
      const score = this.calculateValidationScore(errors, warnings);

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        score,
        contentLength: JSON.stringify(content).length,
        url: toolUrl,
        validatedAt: new Date().toISOString()
      };

    } catch (error: any) {
      return {
        isValid: false,
        errors: [{
          field: 'validation_system',
          message: `Validation failed: ${error.message}`,
          severity: 'error',
          value: content
        }],
        warnings: [],
        score: 0,
        contentLength: 0,
        url: toolUrl,
        validatedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Validate content against database schema
   */
  private async validateSchema(content: any): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];

    // Check required fields
    for (const field of this.contentStandards.requiredFields) {
      if (!content[field]) {
        errors.push({
          field,
          message: `Required field '${field}' is missing`,
          severity: 'error',
          value: undefined
        });
      }
    }

    // Validate detailed_description
    if (content.detailed_description) {
      const desc = content.detailed_description;
      if (typeof desc !== 'string') {
        errors.push({
          field: 'detailed_description',
          message: 'Detailed description must be a string',
          severity: 'error',
          value: desc
        });
      } else {
        if (desc.length < this.contentStandards.minDescriptionLength) {
          errors.push({
            field: 'detailed_description',
            message: `Description too short (${desc.length} chars, minimum ${this.contentStandards.minDescriptionLength})`,
            severity: 'error',
            value: desc.length
          });
        }
        if (desc.length > this.contentStandards.maxDescriptionLength) {
          errors.push({
            field: 'detailed_description',
            message: `Description too long (${desc.length} chars, maximum ${this.contentStandards.maxDescriptionLength})`,
            severity: 'error',
            value: desc.length
          });
        }
      }
    }

    // Validate features array
    if (content.features) {
      if (!Array.isArray(content.features)) {
        errors.push({
          field: 'features',
          message: 'Features must be an array',
          severity: 'error',
          value: typeof content.features
        });
      } else {
        if (content.features.length < this.contentStandards.minFeaturesCount) {
          errors.push({
            field: 'features',
            message: `Too few features (${content.features.length}, minimum ${this.contentStandards.minFeaturesCount})`,
            severity: 'error',
            value: content.features.length
          });
        }
        if (content.features.length > this.contentStandards.maxFeaturesCount) {
          errors.push({
            field: 'features',
            message: `Too many features (${content.features.length}, maximum ${this.contentStandards.maxFeaturesCount})`,
            severity: 'error',
            value: content.features.length
          });
        }
      }
    }

    // Validate pricing structure
    if (content.pricing) {
      if (!content.pricing.type) {
        errors.push({
          field: 'pricing.type',
          message: 'Pricing type is required',
          severity: 'error',
          value: undefined
        });
      } else if (!this.contentStandards.validPricingTypes.includes(content.pricing.type)) {
        errors.push({
          field: 'pricing.type',
          message: `Invalid pricing type '${content.pricing.type}'. Must be one of: ${this.contentStandards.validPricingTypes.join(', ')}`,
          severity: 'error',
          value: content.pricing.type
        });
      }
    }

    // Validate pros_and_cons structure
    if (content.pros_and_cons) {
      const prosAndCons = content.pros_and_cons;
      
      if (!prosAndCons.pros || !Array.isArray(prosAndCons.pros)) {
        errors.push({
          field: 'pros_and_cons.pros',
          message: 'Pros must be an array',
          severity: 'error',
          value: prosAndCons.pros
        });
      } else if (prosAndCons.pros.length < this.contentStandards.minProsConsCount) {
        errors.push({
          field: 'pros_and_cons.pros',
          message: `Too few pros (${prosAndCons.pros.length}, minimum ${this.contentStandards.minProsConsCount})`,
          severity: 'error',
          value: prosAndCons.pros.length
        });
      }

      if (!prosAndCons.cons || !Array.isArray(prosAndCons.cons)) {
        errors.push({
          field: 'pros_and_cons.cons',
          message: 'Cons must be an array',
          severity: 'error',
          value: prosAndCons.cons
        });
      } else if (prosAndCons.cons.length < this.contentStandards.minProsConsCount) {
        errors.push({
          field: 'pros_and_cons.cons',
          message: `Too few cons (${prosAndCons.cons.length}, minimum ${this.contentStandards.minProsConsCount})`,
          severity: 'error',
          value: prosAndCons.cons.length
        });
      }
    }

    return errors;
  }

  /**
   * Validate content quality and style
   */
  private async validateContentQuality(content: any): Promise<string[]> {
    const warnings: string[] = [];

    // Check for banned words
    const contentText = JSON.stringify(content).toLowerCase();
    for (const bannedWord of this.contentStandards.bannedWords) {
      if (contentText.includes(bannedWord.toLowerCase())) {
        warnings.push(`Content contains banned word: '${bannedWord}'`);
      }
    }

    // Check for placeholder content
    if (content.detailed_description) {
      const desc = content.detailed_description.toLowerCase();
      if (desc.includes('placeholder') || desc.includes('lorem') || desc.includes('ipsum')) {
        warnings.push('Description appears to contain placeholder text');
      }
    }

    // Check for repetitive content
    if (content.features && Array.isArray(content.features)) {
      const uniqueFeatures = new Set(content.features.map((f: string) => f.toLowerCase()));
      if (uniqueFeatures.size < content.features.length) {
        warnings.push('Features list contains duplicate items');
      }
    }

    // Check haiku structure if present
    if (content.haiku && content.haiku.lines) {
      if (!Array.isArray(content.haiku.lines) || content.haiku.lines.length !== 3) {
        warnings.push('Haiku must have exactly 3 lines');
      }
    }

    return warnings;
  }

  /**
   * Validate content format and structure
   */
  private async validateFormat(content: any): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];

    // Validate JSON structure
    try {
      JSON.stringify(content);
    } catch (error) {
      errors.push({
        field: 'content',
        message: 'Content is not valid JSON',
        severity: 'error',
        value: content
      });
      return errors;
    }

    // Validate social_links format if present
    if (content.social_links) {
      const socialLinks = content.social_links;
      const validSocialPlatforms = ['twitter', 'linkedin', 'github'];
      
      for (const platform of validSocialPlatforms) {
        if (socialLinks[platform] && socialLinks[platform] !== null) {
          if (typeof socialLinks[platform] !== 'string' || !this.isValidUrl(socialLinks[platform])) {
            errors.push({
              field: `social_links.${platform}`,
              message: `Invalid URL format for ${platform}`,
              severity: 'error',
              value: socialLinks[platform]
            });
          }
        }
      }
    }

    return errors;
  }

  /**
   * Validate business rules and content accuracy
   */
  private async validateBusinessRules(content: any, originalContent: string): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];

    // Check if content is relevant to the original scraped content
    if (content.detailed_description && originalContent) {
      const relevanceScore = this.calculateRelevanceScore(content.detailed_description, originalContent);
      if (relevanceScore < 0.3) {
        errors.push({
          field: 'detailed_description',
          message: 'Generated content appears unrelated to source material',
          severity: 'error',
          value: relevanceScore
        });
      }
    }

    return errors;
  }

  /**
   * Calculate overall validation score
   */
  private calculateValidationScore(errors: ValidationError[], warnings: string[]): number {
    let score = 100;
    
    // Deduct points for errors
    score -= errors.length * 15;
    
    // Deduct points for warnings
    score -= warnings.length * 5;
    
    return Math.max(0, score);
  }

  /**
   * Build validation rules configuration
   */
  private buildValidationRules(): ValidationRule[] {
    return [
      {
        field: 'detailed_description',
        type: 'required',
        message: 'Detailed description is required',
        severity: 'error'
      },
      {
        field: 'detailed_description',
        type: 'length',
        params: { min: 150, max: 2000 },
        message: 'Detailed description must be between 150-2000 characters',
        severity: 'error'
      },
      {
        field: 'features',
        type: 'array',
        params: { min: 3, max: 8 },
        message: 'Features must be an array with 3-8 items',
        severity: 'error'
      },
      {
        field: 'pricing.type',
        type: 'enum',
        params: { values: ['Free', 'Paid', 'Freemium', 'Open Source'] },
        message: 'Invalid pricing type',
        severity: 'error'
      }
    ];
  }

  /**
   * Helper method to validate URLs
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Calculate relevance score between generated and original content
   */
  private calculateRelevanceScore(generated: string, original: string): number {
    // Enhanced relevance calculation for AI-generated content

    // 1. Extract key concepts and entities
    const generatedConcepts = this.extractKeyConcepts(generated);
    const originalConcepts = this.extractKeyConcepts(original);

    // 2. Calculate concept overlap (more meaningful than word overlap)
    const conceptOverlap = this.calculateConceptOverlap(generatedConcepts, originalConcepts);

    // 3. Check for domain relevance (AI tools, technology, etc.)
    const domainRelevance = this.calculateDomainRelevance(generated, original);

    // 4. Check for structural similarity (features, pricing, etc.)
    const structuralSimilarity = this.calculateStructuralSimilarity(generated, original);

    // 5. Weighted combination of scores
    const finalScore = (
      conceptOverlap * 0.4 +
      domainRelevance * 0.3 +
      structuralSimilarity * 0.3
    );

    console.log(`📊 Relevance Score Breakdown:
      Concept Overlap: ${conceptOverlap.toFixed(2)}
      Domain Relevance: ${domainRelevance.toFixed(2)}
      Structural Similarity: ${structuralSimilarity.toFixed(2)}
      Final Score: ${finalScore.toFixed(2)}`);

    return finalScore;
  }

  /**
   * Extract key concepts from content
   */
  private extractKeyConcepts(content: string): Set<string> {
    const concepts = new Set<string>();
    const text = content.toLowerCase();

    // Technology concepts
    const techTerms = ['ai', 'artificial intelligence', 'machine learning', 'automation', 'api', 'software', 'tool', 'platform', 'service', 'technology', 'digital', 'online', 'web', 'app', 'application'];
    techTerms.forEach(term => {
      if (text.includes(term)) concepts.add(term);
    });

    // Business concepts
    const businessTerms = ['pricing', 'free', 'paid', 'subscription', 'plan', 'feature', 'benefit', 'solution', 'business', 'enterprise', 'professional'];
    businessTerms.forEach(term => {
      if (text.includes(term)) concepts.add(term);
    });

    // Extract proper nouns (likely to be product names, companies, etc.)
    const properNouns = text.match(/\b[A-Z][a-z]+\b/g) || [];
    properNouns.forEach(noun => concepts.add(noun.toLowerCase()));

    return concepts;
  }

  /**
   * Calculate concept overlap between two sets
   */
  private calculateConceptOverlap(concepts1: Set<string>, concepts2: Set<string>): number {
    if (concepts1.size === 0 || concepts2.size === 0) return 0;

    const intersection = new Set([...concepts1].filter(x => concepts2.has(x)));
    const union = new Set([...concepts1, ...concepts2]);

    return intersection.size / union.size;
  }

  /**
   * Calculate domain relevance (how well content fits AI/tech domain)
   */
  private calculateDomainRelevance(generated: string, original: string): number {
    const generatedText = generated.toLowerCase();
    const originalText = original.toLowerCase();

    // Check if both mention similar domain concepts
    const aiTerms = ['ai', 'artificial intelligence', 'machine learning', 'neural', 'algorithm', 'automation', 'intelligent'];
    const techTerms = ['software', 'platform', 'tool', 'api', 'service', 'technology', 'digital'];

    const generatedAI = aiTerms.some(term => generatedText.includes(term));
    const originalAI = aiTerms.some(term => originalText.includes(term));
    const generatedTech = techTerms.some(term => generatedText.includes(term));
    const originalTech = techTerms.some(term => originalText.includes(term));

    // High relevance if both are in same domain
    if ((generatedAI && originalAI) || (generatedTech && originalTech)) {
      return 0.9;
    }

    // Medium relevance if at least one is tech-related
    if (generatedAI || generatedTech || originalAI || originalTech) {
      return 0.6;
    }

    return 0.3;
  }

  /**
   * Calculate structural similarity (features, pricing, etc.)
   */
  private calculateStructuralSimilarity(generated: string, original: string): number {
    const generatedText = generated.toLowerCase();
    const originalText = original.toLowerCase();

    let score = 0;
    let checks = 0;

    // Check for pricing information
    const pricingTerms = ['price', 'cost', 'free', 'paid', 'subscription', 'plan'];
    const generatedHasPricing = pricingTerms.some(term => generatedText.includes(term));
    const originalHasPricing = pricingTerms.some(term => originalText.includes(term));
    if (generatedHasPricing === originalHasPricing) score += 1;
    checks += 1;

    // Check for feature information
    const featureTerms = ['feature', 'capability', 'function', 'benefit', 'advantage'];
    const generatedHasFeatures = featureTerms.some(term => generatedText.includes(term));
    const originalHasFeatures = featureTerms.some(term => originalText.includes(term));
    if (generatedHasFeatures === originalHasFeatures) score += 1;
    checks += 1;

    // Check for use case information
    const useCaseTerms = ['use', 'application', 'purpose', 'goal', 'objective'];
    const generatedHasUseCases = useCaseTerms.some(term => generatedText.includes(term));
    const originalHasUseCases = useCaseTerms.some(term => originalText.includes(term));
    if (generatedHasUseCases === originalHasUseCases) score += 1;
    checks += 1;

    return checks > 0 ? score / checks : 0.5;
  }
}
