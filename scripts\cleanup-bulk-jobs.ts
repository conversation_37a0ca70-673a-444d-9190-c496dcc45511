#!/usr/bin/env tsx

/**
 * Bulk Job Cleanup Utility
 * Manual script for cleaning up orphaned and stale bulk processing jobs
 */

import { getJobCleanupService } from '../src/lib/bulk-processing/job-cleanup-service';

interface CliOptions {
  dryRun: boolean;
  maxAge: number;
  includeStuck: boolean;
  includeAbandoned: boolean;
  batchSize: number;
  verbose: boolean;
}

function parseArgs(): CliOptions {
  const args = process.argv.slice(2);
  const options: CliOptions = {
    dryRun: false,
    maxAge: 24,
    includeStuck: true,
    includeAbandoned: true,
    batchSize: 10,
    verbose: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--max-age':
        options.maxAge = parseInt(args[++i]) || 24;
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[++i]) || 10;
        break;
      case '--no-stuck':
        options.includeStuck = false;
        break;
      case '--no-abandoned':
        options.includeAbandoned = false;
        break;
      case '--verbose':
        options.verbose = true;
        break;
      case '--help':
        printHelp();
        process.exit(0);
        break;
    }
  }

  return options;
}

function printHelp(): void {
  console.log(`
Bulk Job Cleanup Utility

Usage: tsx scripts/cleanup-bulk-jobs.ts [options]

Options:
  --dry-run              Show what would be cleaned without actually doing it
  --max-age <hours>      Maximum age for jobs to be considered stale (default: 24)
  --batch-size <number>  Number of jobs to process in each batch (default: 10)
  --no-stuck             Don't include stuck jobs in cleanup
  --no-abandoned         Don't include abandoned jobs in cleanup
  --verbose              Show detailed output
  --help                 Show this help message

Examples:
  tsx scripts/cleanup-bulk-jobs.ts --dry-run
  tsx scripts/cleanup-bulk-jobs.ts --max-age 48 --batch-size 5
  tsx scripts/cleanup-bulk-jobs.ts --no-stuck --verbose
`);
}

async function main(): Promise<void> {
  const options = parseArgs();
  
  console.log('🧹 Bulk Job Cleanup Utility');
  console.log('============================');
  
  if (options.dryRun) {
    console.log('🧪 DRY RUN MODE - No changes will be made');
  }
  
  console.log(`📊 Configuration:`);
  console.log(`   Max age: ${options.maxAge} hours`);
  console.log(`   Batch size: ${options.batchSize}`);
  console.log(`   Include stuck jobs: ${options.includeStuck}`);
  console.log(`   Include abandoned jobs: ${options.includeAbandoned}`);
  console.log(`   Verbose: ${options.verbose}`);
  console.log('');

  try {
    const cleanupService = getJobCleanupService();

    // Step 1: Detect orphaned jobs
    console.log('🔍 Step 1: Detecting orphaned jobs...');
    const orphanedJobs = await cleanupService.detectOrphanedJobs({
      maxAgeHours: options.maxAge,
      includeStuckJobs: options.includeStuck,
      includeAbandonedJobs: options.includeAbandoned,
      dryRun: options.dryRun
    });

    if (orphanedJobs.length === 0) {
      console.log('✅ No orphaned jobs found!');
      return;
    }

    // Show breakdown by reason
    const byReason = orphanedJobs.reduce((acc, job) => {
      acc[job.reason] = (acc[job.reason] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log(`📊 Found ${orphanedJobs.length} orphaned jobs:`);
    Object.entries(byReason).forEach(([reason, count]) => {
      console.log(`   ${reason}: ${count} jobs`);
    });

    if (options.verbose) {
      console.log('\n📋 Detailed job list:');
      orphanedJobs.forEach(job => {
        console.log(`   ${job.id} - ${job.reason} (${job.age_hours.toFixed(1)}h old, status: ${job.status})`);
      });
    }

    if (options.dryRun) {
      console.log('\n🧪 DRY RUN: Would clean up these jobs');
      return;
    }

    // Step 2: Confirm cleanup
    console.log('\n⚠️  This will permanently clean up the orphaned jobs.');
    console.log('   Press Ctrl+C to cancel, or wait 5 seconds to continue...');
    
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Step 3: Perform cleanup
    console.log('\n🧹 Step 2: Cleaning up orphaned jobs...');
    const stats = await cleanupService.cleanupOrphanedJobs(orphanedJobs, {
      batchSize: options.batchSize,
      dryRun: false
    });

    // Step 4: Report results
    console.log('\n📊 Cleanup Results:');
    console.log(`   Jobs scanned: ${stats.totalJobsScanned}`);
    console.log(`   Jobs cleaned: ${stats.jobsCleaned}`);
    console.log(`   Resources freed: ${stats.resourcesFreed}`);
    console.log(`   Memory freed: ${stats.memoryFreedMB}MB`);
    
    if (stats.errors.length > 0) {
      console.log(`   Errors: ${stats.errors.length}`);
      if (options.verbose) {
        stats.errors.forEach(error => {
          console.log(`     ❌ ${error}`);
        });
      }
    }

    if (stats.jobsCleaned === stats.totalJobsScanned) {
      console.log('\n✅ All orphaned jobs cleaned successfully!');
    } else {
      console.log(`\n⚠️  ${stats.totalJobsScanned - stats.jobsCleaned} jobs could not be cleaned`);
    }

  } catch (error) {
    console.error('\n❌ Cleanup failed:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Cleanup cancelled by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Cleanup terminated');
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}
