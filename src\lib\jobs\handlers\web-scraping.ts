import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, WebScrapingJobData } from '../types';
import { contentProcessor } from '@/lib/scraping/content-processor';
import { EnhancedScrapeRequest, EnhancedScrapeResult, MediaAsset } from '@/lib/scraping/types';
import { CommonErrorHandlers, ErrorHandlingUtils } from '../../error-handling';

interface JobResult {
  success: boolean;
  data: LegacyScrapedData;
  screenshot: string | null;
  scrapedAt: string;
  metadata?: {
    creditsUsed: number;
    optimizationStrategy: string;
    contentAnalysis: string;
    qualityScore: number;
  };
}

interface LegacyScrapedData {
  title: string;
  url: string;
  meta: Record<string, string>;
  text: string;
  headings: Array<{ level: string; text: string }>;
  links: Array<{ href: string; text: string }>;
  images: Array<{ src: string; alt: string }>;
  favicon: string | null;
  pricing: Array<{ text: string; tag: string }>;
  faq: Array<{ text: string; tag: string }>;
}

export class WebScrapingHandler implements JobHandler {
  async handle(job: Job): Promise<JobResult> {
    const data = job.data as WebScrapingJobData;

    // Use enhanced error handling for web scraping operations
    return await CommonErrorHandlers.scraping.handleWithRetry(
      async () => {
        return await this.executeWebScraping(job, data);
      },
      ErrorHandlingUtils.createErrorContext({
        operation: 'web_scraping_job',
        provider: 'scrape.do',
        metadata: {
          url: data.url,
          jobId: job.id,
          options: data.options
        }
      })
    );
  }

  /**
   * Execute web scraping with enhanced error handling
   */
  private async executeWebScraping(job: Job, data: WebScrapingJobData): Promise<JobResult> {
    try {
      const toolInfo = data.toolId ? ` (Tool ID: ${data.toolId})` : '';
      console.log(`🕷️ Enhanced web scraping job started for: ${data.url}${toolInfo}`);

      // Convert job data to enhanced scrape request with proper timeout hierarchy
      const enhancedRequest: EnhancedScrapeRequest = {
        url: data.url,
        options: {
          timeout: data.options?.timeout || 90000, // Universal job-level timeout (increased to 90s)
          waitForSelector: data.options?.waitForSelector,
          outputFormat: 'markdown',
          enableJSRendering: false, // Start with basic scraping, fallback manager will handle enhanced
          blockResources: true,
          deviceType: 'desktop'
        },
        costOptimization: true, // Enable cost optimization
        mediaCollection: data.options?.extractImages !== false, // Default true
        multiPageConfig: {
          enabled: false, // Disable multi-page for job queue to keep it simple
          mode: 'conditional',
          maxPagesPerTool: 2,
          creditThreshold: 50,
          pageTypes: {
            pricing: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false },
            faq: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false },
            features: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false },
            about: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false }
          },
          fallbackStrategy: { searchInMainPage: true, useNavigation: false, useSitemap: false }
        }
      };

      // Process enhanced scraping with fallback to basic scraping
      let result;
      let usedFallback = false;

      try {
        console.log(`🚀 Attempting enhanced scraping for: ${data.url}`);
        result = await CommonErrorHandlers.scraping.handleWithRetry(
          async () => {
            return await contentProcessor.processEnhancedScrape(enhancedRequest);
          },
          ErrorHandlingUtils.createErrorContext({
            operation: 'enhanced_scraping',
            provider: 'scrape.do',
            metadata: {
              url: data.url,
              requestOptions: enhancedRequest.options
            }
          })
        );

        if (!result.success) {
          throw new Error(result.error || 'Enhanced scraping failed');
        }
      } catch (enhancedError: any) {
        console.warn(`⚠️ Enhanced scraping failed for ${data.url}: ${enhancedError.message}`);
        console.log(`🔄 Falling back to basic scraping...`);

        // Fallback to basic scraping
        result = await this.performBasicScrapingFallback(data.url, data.options);
        usedFallback = true;
      }

      // Convert result to legacy job format for backward compatibility
      const scrapedData = this.convertToLegacyFormat(result, data.options);

      const scrapingMethod = usedFallback ? 'basic (fallback)' : 'enhanced';
      console.log(`✅ Web scraping completed for: ${data.url} using ${scrapingMethod} (${result.costAnalysis?.creditsUsed || 0} credits)`);

      return {
        success: true,
        data: scrapedData,
        screenshot: result.mediaAssets?.screenshot?.screenshot || null,
        scrapedAt: new Date().toISOString(),
        // Enhanced metadata
        metadata: {
          creditsUsed: result.costAnalysis?.creditsUsed || 0,
          optimizationStrategy: result.costAnalysis?.optimizationStrategy || 'unknown',
          contentAnalysis: result.contentAnalysis?.scenario || 'unknown',
          qualityScore: (result.metadata as Record<string, unknown>)?.qualityScore as number || 0,
          usedFallback: usedFallback,
          scrapingMethod: scrapingMethod
        }
      };
      // Check if fallback result indicates failure
      if (!result.success) {
        throw new Error(result.error || 'Both enhanced and basic scraping failed');
      }

    } catch (error: any) {
      console.error('Web scraping completely failed:', error);

      // Check if this is a timeout error and provide helpful context
      const isTimeout = error.message?.includes('timeout') || error.message?.includes('Request timeout');
      if (isTimeout) {
        console.warn(`⏰ Timeout occurred for ${data.url} - this website may be slow or unresponsive`);
        console.warn(`💡 Consider checking if the website is accessible manually`);
      }

      // Log error with enhanced context for monitoring
      console.error('Enhanced error context:', ErrorHandlingUtils.formatErrorForLogging(error, {
        operation: 'web_scraping_job',
        provider: 'scrape.do',
        metadata: {
          url: data.url,
          jobId: job.id,
          isTimeout: isTimeout,
          triedFallback: true
        }
      }));

      throw new Error(`Web scraping failed (tried enhanced + basic fallback): ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Perform basic scraping as fallback when enhanced scraping fails
   */
  private async performBasicScrapingFallback(url: string, options?: any): Promise<any> {
    try {
      console.log(`🔧 Performing basic scraping fallback for: ${url}`);

      // Import the basic scraping client
      const { ScrapeDoClient } = await import('@/lib/scraping/scrape-do-client');
      const scrapeClient = new ScrapeDoClient();

      // Use basic scraping options with shorter timeout
      const basicOptions = {
        useResidentialProxy: false,
        enableJSRendering: false,
        outputFormat: 'markdown',
        blockResources: true,
        timeout: 30000, // Shorter timeout for basic scraping
        deviceType: 'desktop',
        waitCondition: 'domcontentloaded'
      };

      console.log(`📡 Making basic scrape.do API call...`);
      const basicResult = await scrapeClient.scrape(url, basicOptions);

      if (!basicResult.success) {
        throw new Error(basicResult.error || 'Basic scraping also failed');
      }

      // Create a minimal enhanced result structure for compatibility
      const fallbackResult = {
        success: true,
        content: basicResult.content,
        contentAnalysis: {
          scenario: 'basic_fallback',
          hasMetaTags: true,
          hasLoadingIndicators: false,
          hasSubstantialContent: basicResult.content.length > 500,
          needsEnhancedScraping: false
        },
        costAnalysis: {
          creditsUsed: 1, // Basic scraping uses 1 credit
          optimizationStrategy: 'basic_fallback',
          savings: 0
        },
        mediaAssets: {
          favicon: [],
          ogImages: [],
          screenshot: null
        },
        metadata: {
          scrapingMethod: 'basic_fallback',
          timestamp: new Date().toISOString(),
          qualityScore: 0.7 // Reasonable score for basic content
        }
      };

      console.log(`✅ Basic scraping fallback successful for: ${url}`);
      return fallbackResult;

    } catch (error: any) {
      console.error(`❌ Basic scraping fallback also failed for ${url}:`, error);

      // Create a minimal result with error information
      return {
        success: false,
        error: `Both enhanced and basic scraping failed: ${error.message}`,
        content: '',
        contentAnalysis: { scenario: 'failed' },
        costAnalysis: { creditsUsed: 0, optimizationStrategy: 'failed' },
        mediaAssets: { favicon: [], ogImages: [], screenshot: null },
        metadata: { scrapingMethod: 'failed', timestamp: new Date().toISOString() }
      };
    }
  }

  /**
   * Convert enhanced scrape result to legacy format for backward compatibility
   */
  private convertToLegacyFormat(result: EnhancedScrapeResult, options: WebScrapingJobData['options']): LegacyScrapedData {
    // Extract title and meta information from markdown content
    const title = this.extractTitleFromMarkdown(result.content);
    const headings = this.extractHeadingsFromMarkdown(result.content);
    const text = result.content; // Keep full content for AI processing

    return {
      title: title || 'Untitled',
      url: result.url || '',
      meta: {
        'og:title': title,
        'og:description': this.extractDescriptionFromMarkdown(result.content),
        description: this.extractDescriptionFromMarkdown(result.content)
      },
      text,
      headings,
      links: options?.extractLinks ? this.extractLinksFromMarkdown(result.content) : [],
      images: result.mediaAssets?.ogImages?.map((img: MediaAsset) => ({
        src: img.url,
        alt: ''
      })) || [],
      favicon: result.mediaAssets?.favicon?.[0] || null,
      pricing: this.extractSectionContent(result.content, ['price', 'pricing', 'cost', 'plan']),
      faq: this.extractSectionContent(result.content, ['faq', 'question', 'help', 'support'])
    };
  }

  private extractTitleFromMarkdown(content: string): string {
    const titleMatch = content.match(/^#\s+(.+)$/m);
    return titleMatch ? titleMatch[1].trim() : '';
  }

  private extractDescriptionFromMarkdown(content: string): string {
    const lines = content.split('\n');
    let foundHeading = false;

    for (const line of lines) {
      if (line.startsWith('#')) {
        foundHeading = true;
        continue;
      }
      if (foundHeading && line.trim() && !line.startsWith('#')) {
        return line.trim().substring(0, 200);
      }
    }

    return '';
  }

  private extractHeadingsFromMarkdown(content: string): Array<{ level: string; text: string }> {
    const headings: Array<{ level: string; text: string }> = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      if (match) {
        const level = `h${match[1].length}`;
        const text = match[2].trim();
        headings.push({ level, text });
      }
    }

    return headings;
  }

  private extractLinksFromMarkdown(content: string): Array<{ href: string; text: string }> {
    const links: Array<{ href: string; text: string }> = [];
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    let match;

    while ((match = linkRegex.exec(content)) !== null) {
      links.push({
        text: match[1],
        href: match[2]
      });
    }

    return links;
  }

  private extractSectionContent(content: string, keywords: string[]): Array<{ text: string; tag: string }> {
    const sections: Array<{ text: string; tag: string }> = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const lowerLine = line.toLowerCase();
      if (keywords.some(keyword => lowerLine.includes(keyword))) {
        sections.push({
          text: line.trim(),
          tag: line.startsWith('#') ? 'heading' : 'text'
        });
      }
    }

    return sections.slice(0, 10); // Limit to 10 items
  }
}
