#!/usr/bin/env tsx

/**
 * Fix Database Storage Issues
 * 
 * This script:
 * 1. Tests the Supabase connection
 * 2. Creates the missing scraped_content table if needed
 * 3. Tests database storage functionality
 * 4. Provides detailed debugging information
 */

import { createClient } from '@supabase/supabase-js';
import { promises as fs } from 'fs';
import { join } from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
}

class DatabaseStorageFixer {
  private supabase: any;

  constructor() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase environment variables');
    }

    this.supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
  }

  async runDiagnostics(): Promise<void> {
    console.log('🔍 Running Database Storage Diagnostics...\n');

    const tests = [
      { name: 'Environment Variables', test: () => this.testEnvironmentVariables() },
      { name: 'Database Connection', test: () => this.testDatabaseConnection() },
      { name: 'Table Existence', test: () => this.testTableExistence() },
      { name: 'Table Schema', test: () => this.testTableSchema() },
      { name: 'Insert Operation', test: () => this.testInsertOperation() },
      { name: 'Cleanup Test Data', test: () => this.cleanupTestData() }
    ];

    for (const { name, test } of tests) {
      console.log(`🧪 Testing: ${name}`);
      try {
        const result = await test();
        if (result.success) {
          console.log(`✅ ${name}: ${result.message}`);
          if (result.details) {
            console.log(`   Details:`, result.details);
          }
        } else {
          console.log(`❌ ${name}: ${result.message}`);
          if (result.details) {
            console.log(`   Details:`, result.details);
          }
        }
      } catch (error) {
        console.log(`💥 ${name}: Exception thrown`);
        console.log(`   Error:`, error instanceof Error ? error.message : String(error));
      }
      console.log('');
    }
  }

  private async testEnvironmentVariables(): Promise<TestResult> {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl) {
      return { success: false, message: 'NEXT_PUBLIC_SUPABASE_URL is missing' };
    }

    if (!supabaseServiceKey) {
      return { success: false, message: 'SUPABASE_SERVICE_ROLE_KEY is missing' };
    }

    return {
      success: true,
      message: 'Environment variables are present',
      details: {
        supabaseUrl: supabaseUrl.substring(0, 30) + '...',
        serviceKeyPresent: true,
        serviceKeyLength: supabaseServiceKey.length
      }
    };
  }

  private async testDatabaseConnection(): Promise<TestResult> {
    try {
      const { data, error } = await this.supabase.auth.getSession();
      
      if (error) {
        return { success: false, message: 'Auth connection failed', details: error };
      }

      // Test a simple query
      const { data: testData, error: testError } = await this.supabase
        .from('tools')
        .select('count')
        .limit(1);

      if (testError) {
        return { success: false, message: 'Database query failed', details: testError };
      }

      return { success: true, message: 'Database connection successful' };
    } catch (error) {
      return { success: false, message: 'Connection exception', details: error };
    }
  }

  private async testTableExistence(): Promise<TestResult> {
    try {
      const { data, error } = await this.supabase
        .from('scraped_content')
        .select('count')
        .limit(1);

      if (error) {
        if (error.code === '42P01' || error.message?.includes('does not exist')) {
          return { success: false, message: 'scraped_content table does not exist', details: error };
        }
        return { success: false, message: 'Table check failed', details: error };
      }

      return { success: true, message: 'scraped_content table exists' };
    } catch (error) {
      return { success: false, message: 'Table existence check exception', details: error };
    }
  }

  private async testTableSchema(): Promise<TestResult> {
    try {
      const { data, error } = await this.supabase
        .rpc('get_table_schema', { table_name: 'scraped_content' });

      if (error) {
        return { success: false, message: 'Schema check failed', details: error };
      }

      return { success: true, message: 'Table schema retrieved', details: data };
    } catch (error) {
      // Fallback: try to describe the table structure
      try {
        const { data, error: descError } = await this.supabase
          .from('scraped_content')
          .select('*')
          .limit(0);

        if (descError) {
          return { success: false, message: 'Schema check failed', details: descError };
        }

        return { success: true, message: 'Table structure accessible' };
      } catch (fallbackError) {
        return { success: false, message: 'Schema check exception', details: fallbackError };
      }
    }
  }

  private async testInsertOperation(): Promise<TestResult> {
    const testData = {
      url: 'https://test-database-storage.example.com',
      content: 'Test content for database storage validation',
      success: true,
      timestamp: new Date().toISOString(),
      credits_used: 0,
      metadata: { test: true, timestamp: Date.now() }
    };

    try {
      const { data, error } = await this.supabase
        .from('scraped_content')
        .insert(testData)
        .select()
        .single();

      if (error) {
        return { success: false, message: 'Insert operation failed', details: error };
      }

      return { 
        success: true, 
        message: 'Insert operation successful', 
        details: { insertedId: data.id, insertedUrl: data.url }
      };
    } catch (error) {
      return { success: false, message: 'Insert operation exception', details: error };
    }
  }

  private async cleanupTestData(): Promise<TestResult> {
    try {
      const { error } = await this.supabase
        .from('scraped_content')
        .delete()
        .eq('url', 'https://test-database-storage.example.com');

      if (error) {
        return { success: false, message: 'Cleanup failed', details: error };
      }

      return { success: true, message: 'Test data cleaned up successfully' };
    } catch (error) {
      return { success: false, message: 'Cleanup exception', details: error };
    }
  }

  async createMissingTable(): Promise<void> {
    console.log('🔧 Creating missing scraped_content table...\n');

    try {
      const migrationPath = join(process.cwd(), 'supabase', 'migrations', '008_add_scraped_content_table.sql');
      const migrationSQL = await fs.readFile(migrationPath, 'utf-8');

      console.log('📄 Executing migration SQL...');
      
      // Split the SQL into individual statements and execute them
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.trim()) {
          const { error } = await this.supabase.rpc('exec_sql', { sql: statement });
          if (error) {
            console.error('❌ SQL execution failed:', error);
            throw error;
          }
        }
      }

      console.log('✅ Migration executed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const shouldCreateTable = args.includes('--create-table');

  try {
    const fixer = new DatabaseStorageFixer();

    if (shouldCreateTable) {
      await fixer.createMissingTable();
      console.log('');
    }

    await fixer.runDiagnostics();

    console.log('🎯 Diagnosis complete!');
    console.log('');
    console.log('💡 Next steps:');
    console.log('   1. If table is missing, run: npx tsx scripts/fix-database-storage.ts --create-table');
    console.log('   2. If connection fails, check your .env.local file');
    console.log('   3. If schema issues, verify the migration was applied correctly');

  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { DatabaseStorageFixer };
