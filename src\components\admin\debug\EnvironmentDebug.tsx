'use client';

import { useState } from 'react';

/**
 * Debug component to check environment variables in the browser
 * Only shows in development or when explicitly enabled
 */
export function EnvironmentDebug() {
  const [isVisible, setIsVisible] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const isDevelopment = process.env.NODE_ENV === 'development';

  if (!isDevelopment && !isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-red-600 text-white px-3 py-1 rounded text-xs"
        >
          Debug Env
        </button>
      </div>
    );
  }

  const testAuthentication = async () => {
    setIsLoading(true);
    try {
      const adminApiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY;
      
      const response = await fetch('/api/admin/bulk-processing', {
        method: 'GET',
        headers: {
          'x-admin-api-key': adminApiKey || '',
        },
      });

      const result = {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        adminApiKey: adminApiKey ? `${adminApiKey.substring(0, 10)}...` : 'NOT_SET',
        timestamp: new Date().toISOString(),
      };

      if (response.ok) {
        const data = await response.json();
        result.data = data;
      } else {
        const errorText = await response.text();
        result.error = errorText;
      }

      setTestResult(result);
    } catch (error) {
      setTestResult({
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const envVars = {
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_ADMIN_API_KEY: process.env.NEXT_PUBLIC_ADMIN_API_KEY ? 
      `${process.env.NEXT_PUBLIC_ADMIN_API_KEY.substring(0, 10)}...` : 'NOT_SET',
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT_SET',
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'NOT_SET',
    NEXT_PUBLIC_VERCEL_URL: process.env.NEXT_PUBLIC_VERCEL_URL || 'NOT_SET',
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-gray-900 border border-gray-700 rounded-lg p-4 max-w-md max-h-96 overflow-auto">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-sm font-bold text-white">Environment Debug</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </button>
      </div>

      <div className="space-y-3">
        <div>
          <h4 className="text-xs font-semibold text-gray-300 mb-1">Client Environment Variables:</h4>
          <div className="text-xs space-y-1">
            {Object.entries(envVars).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="text-gray-400">{key}:</span>
                <span className={value === 'NOT_SET' ? 'text-red-400' : 'text-green-400'}>
                  {value}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div>
          <button
            onClick={testAuthentication}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-3 py-1 rounded text-xs"
          >
            {isLoading ? 'Testing...' : 'Test Authentication'}
          </button>
        </div>

        {testResult && (
          <div>
            <h4 className="text-xs font-semibold text-gray-300 mb-1">Test Result:</h4>
            <pre className="text-xs bg-gray-800 p-2 rounded overflow-auto">
              {JSON.stringify(testResult, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
