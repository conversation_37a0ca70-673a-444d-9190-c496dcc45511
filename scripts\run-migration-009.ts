#!/usr/bin/env tsx

/**
 * Migration 009: Add media_collection job type to database constraint
 * 
 * This script adds the 'media_collection' job type to the ai_generation_jobs
 * table constraint to fix the database constraint violation error.
 */

import { config } from 'dotenv';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
config({ path: '.env.local' });

async function runMigration009() {
  console.log('🚀 MIGRATION 009: Add media_collection job type');
  console.log('=' .repeat(60));
  console.log('📋 Issue: media_collection job type violates database constraint');
  console.log('🎯 Solution: Update ai_generation_jobs_job_type_check_v3 constraint');
  console.log('');

  try {
    // Step 1: Load migration SQL
    console.log('1️⃣ Loading migration SQL...');
    
    const migrationPath = join(process.cwd(), 'src/lib/database/migrations/009_add_media_collection_job_type.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    console.log('✅ Migration SQL loaded successfully');
    console.log(`   File: ${migrationPath}`);
    console.log(`   Size: ${migrationSQL.length} characters`);
    console.log('');

    // Step 2: Display the SQL for manual execution
    console.log('2️⃣ MANUAL SQL EXECUTION REQUIRED');
    console.log('⚠️  Please copy and paste the following SQL into your Supabase SQL Editor:');
    console.log('');
    console.log('🔗 Supabase SQL Editor: https://supabase.com/dashboard/project/[your-project]/sql');
    console.log('');
    console.log('--- COPY AND PASTE THE FOLLOWING SQL ---');
    console.log(migrationSQL);
    console.log('--- END OF SQL ---');
    console.log('');

    // Step 3: Wait for user confirmation
    console.log('3️⃣ After executing the SQL in Supabase, press Enter to continue...');
    await new Promise(resolve => {
      process.stdin.once('data', () => resolve(undefined));
    });

    console.log('');
    console.log('4️⃣ Verifying migration...');
    
    // Step 4: Test the fix
    console.log('✅ Migration 009 should now be applied');
    console.log('');
    console.log('📊 EXPECTED RESULTS:');
    console.log('   ✅ ai_generation_jobs_job_type_check_v4 constraint created');
    console.log('   ✅ media_collection job type now allowed');
    console.log('   ✅ MediaCollectionJob can create database records');
    console.log('   ✅ No more constraint violation errors');
    console.log('');
    
    console.log('🧪 TESTING INSTRUCTIONS:');
    console.log('   1. Try creating a media_collection job again');
    console.log('   2. Check that no constraint violation errors occur');
    console.log('   3. Verify job appears in admin interface');
    console.log('');
    
    console.log('📋 CONSTRAINT DETAILS:');
    console.log('   Old constraint: ai_generation_jobs_job_type_check_v3');
    console.log('   New constraint: ai_generation_jobs_job_type_check_v4');
    console.log('   Added job type: media_collection');
    console.log('');
    
    console.log('✅ All allowed job types after migration:');
    const allowedTypes = [
      'tool_submission',
      'content_generation', 
      'web_scraping',
      'email_notification',
      'tool_processing',
      'screenshot_capture',
      'favicon_extraction',
      'bulk_processing',
      'media_collection',  // NEW
      'scrape',
      'generate', 
      'bulk',
      'media_extraction'
    ];
    
    allowedTypes.forEach((type, index) => {
      const isNew = type === 'media_collection';
      const prefix = isNew ? '   🆕' : '   ✅';
      console.log(`${prefix} ${type}`);
    });
    
    console.log('');
    console.log('🎉 Migration 009 completed successfully!');
    console.log('');
    console.log('📋 NEXT STEPS:');
    console.log('   1. Test media collection job creation');
    console.log('   2. Verify admin interface shows jobs');
    console.log('   3. Check that metascraper functionality works');
    console.log('   4. Monitor for any remaining constraint issues');
    
  } catch (error) {
    console.error('❌ Migration 009 failed:', error);
    console.log('');
    console.log('🔧 TROUBLESHOOTING:');
    console.log('   1. Check that migration file exists');
    console.log('   2. Verify Supabase connection');
    console.log('   3. Ensure you have admin privileges');
    console.log('   4. Check for syntax errors in SQL');
    process.exit(1);
  }
}

// CLI interface
if (require.main === module) {
  runMigration009().catch(console.error);
}

export { runMigration009 };
