#!/usr/bin/env tsx

/**
 * Test Fallback with Keycon.space
 * 
 * This script simulates the fallback mechanism with the problematic
 * keycon.space URL to verify the fallback works correctly.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

async function testFallbackWithKeycon() {
  console.log('🧪 TESTING FALLBACK WITH KEYCON.SPACE\n');

  try {
    // Import the scraping client for basic fallback test
    const { ScrapeDoClient } = await import('../src/lib/scraping/scrape-do-client');
    
    const testUrl = 'https://keycon.space/';
    console.log(`🎯 Testing URL: ${testUrl}`);

    console.log('\n1. 🔧 Testing Basic Scraping (Fallback Method)...');
    
    const scrapeClient = new ScrapeDoClient();
    
    // Use the same basic options as the fallback
    const basicOptions = {
      useResidentialProxy: false,
      enableJSRendering: false,
      outputFormat: 'markdown',
      blockResources: true,
      timeout: 30000, // 30 second timeout
      deviceType: 'desktop',
      waitCondition: 'domcontentloaded'
    };

    console.log('📡 Making basic scrape.do API call...');
    console.log('⏱️ Timeout: 30 seconds');
    console.log('🚫 JS Rendering: Disabled');
    console.log('📄 Output: Markdown');

    const startTime = Date.now();
    
    try {
      const result = await scrapeClient.scrape(testUrl, basicOptions);
      const duration = Date.now() - startTime;
      
      if (result.success) {
        console.log(`\n✅ Basic scraping successful!`);
        console.log(`⏱️ Duration: ${duration}ms`);
        console.log(`📊 Content length: ${result.content.length} characters`);
        console.log(`📄 Content preview: ${result.content.substring(0, 200)}...`);
        
        console.log('\n🎯 FALLBACK MECHANISM VALIDATION:');
        console.log('   ✅ Basic scraping works for keycon.space');
        console.log('   ✅ Completes within 30-second timeout');
        console.log('   ✅ Returns usable content');
        console.log('   ✅ Fallback mechanism should work!');
        
      } else {
        console.log(`\n❌ Basic scraping failed: ${result.error}`);
        console.log('\n⚠️ FALLBACK MECHANISM CONCERN:');
        console.log('   • Basic scraping also fails for this URL');
        console.log('   • Both enhanced and basic would fail');
        console.log('   • Job would still fail completely');
        console.log('   • May need to investigate URL accessibility');
      }
      
    } catch (error: any) {
      const duration = Date.now() - startTime;
      console.log(`\n❌ Basic scraping threw error after ${duration}ms:`);
      console.log(`   Error: ${error.message}`);
      
      const isTimeout = error.message?.includes('timeout');
      if (isTimeout) {
        console.log('\n⏰ TIMEOUT ANALYSIS:');
        console.log('   • Basic scraping (30s) also times out');
        console.log('   • Website is extremely slow or unresponsive');
        console.log('   • May need longer timeout or different approach');
      }
    }

    console.log('\n2. 📊 FALLBACK MECHANISM SUMMARY:');
    console.log('');
    console.log('🔄 How it works:');
    console.log('   1. Try enhanced scraping (60s timeout, JS enabled)');
    console.log('   2. If enhanced fails → Try basic scraping (30s timeout, no JS)');
    console.log('   3. If basic succeeds → Continue with basic content');
    console.log('   4. If basic fails → Job fails with "tried both" message');
    console.log('');
    console.log('💰 Cost optimization:');
    console.log('   • Enhanced failure → Basic success = 1 credit (vs 5+ credits)');
    console.log('   • Better than complete job failure');
    console.log('');
    console.log('📊 Success rate improvement:');
    console.log('   • Enhanced OR Basic success = Higher overall success rate');
    console.log('   • Graceful degradation instead of hard failure');

    console.log('\n3. 🚀 NEXT STEPS:');
    console.log('   1. Process keycon.space through bulk processing');
    console.log('   2. Monitor logs for fallback activation');
    console.log('   3. Check if basic scraping succeeds where enhanced failed');
    console.log('   4. Verify content generation continues with basic data');

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
testFallbackWithKeycon().catch(console.error);
