'use client';

import React, { useState, useEffect } from 'react';
import { 
  Save, 
  Plus, 
  Trash2, 
  Alert<PERSON>ircle, 
  CheckCircle, 
  RotateCcw,
  Settings,
  Info
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface ValidationRule {
  id: string;
  field: string;
  type: 'required' | 'length' | 'array_length' | 'word_count' | 'enum' | 'format';
  params: {
    min?: number;
    max?: number;
    values?: string[];
    pattern?: string;
  };
  message: string;
  severity: 'error' | 'warning';
  enabled: boolean;
}

interface ValidationRulesConfig {
  contentStandards: {
    minDescriptionLength: number;
    maxDescriptionLength: number;
    minDetailedDescriptionWords: number;
    maxDetailedDescriptionWords: number;
    minFeaturesCount: number;
    maxFeaturesCount: number;
    minProsCount: number;
    maxProsCount: number;
    minConsCount: number;
    maxConsCount: number;
    minFaqsCount: number;
    maxFaqsCount: number;
    minHashtagsCount: number;
    maxHashtagsCount: number;
    maxMetaTitleLength: number;
    minMetaDescriptionLength: number;
    maxMetaDescriptionLength: number;
    maxTooltipLength: number;
  };
  validationRules: ValidationRule[];
  bannedWords: string[];
  requiredFields: string[];
}

export function ValidationRulesEditor() {
  const [config, setConfig] = useState<ValidationRulesConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState<'standards' | 'rules' | 'words' | 'fields'>('standards');

  // Load current validation configuration
  useEffect(() => {
    loadValidationConfig();
  }, []);

  const loadValidationConfig = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/admin/validation-rules', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load validation configuration');
      }

      const data = await response.json();

      // Ensure all required fields have default values
      const configData = data.data;
      if (configData && configData.contentStandards) {
        // Fill in any missing numeric fields with defaults
        const defaults = {
          minDescriptionLength: 50,
          maxDescriptionLength: 500,
          minDetailedDescriptionWords: 50,
          maxDetailedDescriptionWords: 300,
          minFeaturesCount: 3,
          maxFeaturesCount: 8,
          minProsCount: 3,
          maxProsCount: 10,
          minConsCount: 3,
          maxConsCount: 10,
          minFaqsCount: 3,
          maxFaqsCount: 8,
          minHashtagsCount: 5,
          maxHashtagsCount: 10,
          maxMetaTitleLength: 60,
          minMetaDescriptionLength: 150,
          maxMetaDescriptionLength: 160,
          maxTooltipLength: 100
        };

        // Merge defaults with loaded config
        configData.contentStandards = {
          ...defaults,
          ...configData.contentStandards
        };

        // Ensure arrays exist
        configData.validationRules = configData.validationRules || [];
        configData.bannedWords = configData.bannedWords || [];
        configData.requiredFields = configData.requiredFields || [];
      }

      setConfig(configData);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const saveValidationConfig = async () => {
    if (!config) return;

    try {
      setIsSaving(true);
      setError(null);

      const response = await fetch('/api/admin/validation-rules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          action: 'update',
          config
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save validation configuration');
      }

      setHasChanges(false);
      // Could add success toast here
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefaults = async () => {
    if (confirm('Are you sure you want to reset all validation rules to defaults? This cannot be undone.')) {
      try {
        setIsLoading(true);
        
        const response = await fetch('/api/admin/validation-rules', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          },
          body: JSON.stringify({
            action: 'reset'
          })
        });

        if (!response.ok) {
          throw new Error('Failed to reset validation configuration');
        }

        const data = await response.json();
        setConfig(data.data);
        setHasChanges(false);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const updateContentStandards = (field: keyof ValidationRulesConfig['contentStandards'], value: number) => {
    if (!config) return;

    // Handle NaN values by using 0 as default
    const safeValue = isNaN(value) ? 0 : value;

    setConfig(prev => ({
      ...prev!,
      contentStandards: {
        ...prev!.contentStandards,
        [field]: safeValue
      }
    }));
    setHasChanges(true);
  };

  const addValidationRule = () => {
    if (!config) return;

    const newRule: ValidationRule = {
      id: `rule_${Date.now()}`,
      field: 'new_field',
      type: 'required',
      params: {},
      message: 'New validation rule',
      severity: 'error',
      enabled: true
    };

    setConfig(prev => ({
      ...prev!,
      validationRules: [...prev!.validationRules, newRule]
    }));
    setHasChanges(true);
  };

  const updateValidationRule = (id: string, updates: Partial<ValidationRule>) => {
    if (!config) return;

    setConfig(prev => ({
      ...prev!,
      validationRules: prev!.validationRules.map(rule =>
        rule.id === id ? { ...rule, ...updates } : rule
      )
    }));
    setHasChanges(true);
  };

  const removeValidationRule = (id: string) => {
    if (!config) return;

    setConfig(prev => ({
      ...prev!,
      validationRules: prev!.validationRules.filter(rule => rule.id !== id)
    }));
    setHasChanges(true);
  };

  const updateBannedWords = (words: string) => {
    if (!config) return;

    setConfig(prev => ({
      ...prev!,
      bannedWords: words.split(',').map(w => w.trim()).filter(w => w.length > 0)
    }));
    setHasChanges(true);
  };

  const updateRequiredFields = (fields: string) => {
    if (!config) return;

    setConfig(prev => ({
      ...prev!,
      requiredFields: fields.split(',').map(f => f.trim()).filter(f => f.length > 0)
    }));
    setHasChanges(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-400 mt-4">Loading validation configuration...</p>
        </div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">Configuration Not Found</h3>
        <p className="text-gray-400 mb-4">Unable to load validation configuration.</p>
        <button
          onClick={loadValidationConfig}
          className="bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Validation Rules Configuration</h1>
          <p className="text-gray-400">Configure content validation standards and rules</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {hasChanges && (
            <div className="flex items-center space-x-1 text-yellow-400">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">Unsaved changes</span>
            </div>
          )}
          
          <button
            onClick={resetToDefaults}
            className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Reset to Defaults</span>
          </button>
          
          <button
            onClick={saveValidationConfig}
            disabled={isSaving || !hasChanges}
            className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            {isSaving ? (
              <>
                <LoadingSpinner size="sm" />
                <span>Saving...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Save Changes</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <span className="text-red-400 font-medium">Error</span>
          </div>
          <p className="text-red-300 mt-1">{error}</p>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-zinc-700">
        <nav className="flex space-x-8">
          {[
            { id: 'standards', label: 'Content Standards', icon: Settings },
            { id: 'rules', label: 'Validation Rules', icon: CheckCircle },
            { id: 'words', label: 'Banned Words', icon: AlertCircle },
            { id: 'fields', label: 'Required Fields', icon: Info }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        {activeTab === 'standards' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white mb-4">Content Length Standards</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Description Standards */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-300">Description Lengths</h4>
                
                <div>
                  <label className="block text-sm text-gray-400 mb-1">Min Description Length</label>
                  <input
                    type="number"
                    value={config.contentStandards.minDescriptionLength || 0}
                    onChange={(e) => updateContentStandards('minDescriptionLength', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Max Description Length</label>
                  <input
                    type="number"
                    value={config.contentStandards.maxDescriptionLength || 0}
                    onChange={(e) => updateContentStandards('maxDescriptionLength', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Min Detailed Description (words)</label>
                  <input
                    type="number"
                    value={config.contentStandards.minDetailedDescriptionWords || 0}
                    onChange={(e) => updateContentStandards('minDetailedDescriptionWords', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Max Detailed Description (words)</label>
                  <input
                    type="number"
                    value={config.contentStandards.maxDetailedDescriptionWords || 0}
                    onChange={(e) => updateContentStandards('maxDetailedDescriptionWords', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>
              </div>

              {/* Array Standards */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-300">Array Counts</h4>
                
                <div>
                  <label className="block text-sm text-gray-400 mb-1">Min Features Count</label>
                  <input
                    type="number"
                    value={config.contentStandards.minFeaturesCount || 0}
                    onChange={(e) => updateContentStandards('minFeaturesCount', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Max Features Count</label>
                  <input
                    type="number"
                    value={config.contentStandards.maxFeaturesCount || 0}
                    onChange={(e) => updateContentStandards('maxFeaturesCount', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Min Pros Count</label>
                  <input
                    type="number"
                    value={config.contentStandards.minProsCount || 0}
                    onChange={(e) => updateContentStandards('minProsCount', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Max Pros Count</label>
                  <input
                    type="number"
                    value={config.contentStandards.maxProsCount || 0}
                    onChange={(e) => updateContentStandards('maxProsCount', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Min Cons Count</label>
                  <input
                    type="number"
                    value={config.contentStandards.minConsCount || 0}
                    onChange={(e) => updateContentStandards('minConsCount', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Max Cons Count</label>
                  <input
                    type="number"
                    value={config.contentStandards.maxConsCount || 0}
                    onChange={(e) => updateContentStandards('maxConsCount', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>
              </div>

              {/* SEO Standards */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-300">SEO & Meta Standards</h4>
                
                <div>
                  <label className="block text-sm text-gray-400 mb-1">Max Meta Title Length</label>
                  <input
                    type="number"
                    value={config.contentStandards.maxMetaTitleLength || 0}
                    onChange={(e) => updateContentStandards('maxMetaTitleLength', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Min Meta Description Length</label>
                  <input
                    type="number"
                    value={config.contentStandards.minMetaDescriptionLength || 0}
                    onChange={(e) => updateContentStandards('minMetaDescriptionLength', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Max Meta Description Length</label>
                  <input
                    type="number"
                    value={config.contentStandards.maxMetaDescriptionLength || 0}
                    onChange={(e) => updateContentStandards('maxMetaDescriptionLength', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Min Hashtags Count</label>
                  <input
                    type="number"
                    value={config.contentStandards.minHashtagsCount || 0}
                    onChange={(e) => updateContentStandards('minHashtagsCount', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Max Hashtags Count</label>
                  <input
                    type="number"
                    value={config.contentStandards.maxHashtagsCount || 0}
                    onChange={(e) => updateContentStandards('maxHashtagsCount', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400 mb-1">Max Tooltip Length</label>
                  <input
                    type="number"
                    value={config.contentStandards.maxTooltipLength || 0}
                    onChange={(e) => updateContentStandards('maxTooltipLength', parseInt(e.target.value) || 0)}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded px-3 py-2 text-white"
                    min="0"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Other tabs would be implemented here */}
        {activeTab !== 'standards' && (
          <div className="text-center py-12">
            <Info className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">{activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Configuration</h3>
            <p className="text-gray-400">This section is under development.</p>
          </div>
        )}
      </div>
    </div>
  );
}
