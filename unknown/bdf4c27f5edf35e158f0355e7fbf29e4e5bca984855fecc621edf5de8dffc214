'use client';

import React, { useState, useEffect } from 'react';
import { Editor } from '@monaco-editor/react';
import { 
  Eye, 
  EyeOff, 
  Code, 
  FileText, 
  Copy, 
  RotateCcw,
  AlertCircle,
  CheckCircle,
  Maximize2,
  Minimize2
} from 'lucide-react';

interface PromptEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  height?: string;
  readOnly?: boolean;
  showPreview?: boolean;
  variables?: string[];
}

export function PromptEditor({
  value,
  onChange,
  placeholder = "Enter your prompt template here...",
  height = "400px",
  readOnly = false,
  showPreview = true,
  variables = []
}: PromptEditorProps) {
  const [mode, setMode] = useState<'edit' | 'preview' | 'split'>('edit');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [parsedContent, setParsedContent] = useState('');
  const [parseError, setParseError] = useState<string | null>(null);

  // Parse escape sequences for preview
  useEffect(() => {
    try {
      // Handle escape sequences properly
      let parsed = value
        .replace(/\\n/g, '\n')
        .replace(/\\t/g, '\t')
        .replace(/\\"/g, '"')
        .replace(/\\\\/g, '\\')
        .replace(/\\r/g, '\r');
      
      setParsedContent(parsed);
      setParseError(null);
    } catch (error) {
      setParseError(error instanceof Error ? error.message : 'Parse error');
      setParsedContent(value);
    }
  }, [value]);

  // Handle editor change with proper escaping
  const handleEditorChange = (newValue: string | undefined) => {
    if (newValue !== undefined) {
      // When saving, we need to escape the content properly for JSON storage
      const escaped = newValue
        .replace(/\\/g, '\\\\')
        .replace(/"/g, '\\"')
        .replace(/\n/g, '\\n')
        .replace(/\t/g, '\\t')
        .replace(/\r/g, '\\r');
      
      onChange(escaped);
    }
  };

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content);
    // Could add toast notification here
  };

  const resetContent = () => {
    onChange('');
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const containerClasses = isFullscreen 
    ? "fixed inset-0 z-50 bg-zinc-900 p-4"
    : "relative";

  const editorHeight = isFullscreen ? "calc(100vh - 120px)" : height;

  return (
    <div className={containerClasses}>
      <div className="bg-zinc-800 border border-zinc-600 rounded-lg overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-zinc-600 bg-zinc-700">
          <div className="flex items-center space-x-2">
            <h3 className="text-sm font-medium text-white">Prompt Template Editor</h3>
            {parseError && (
              <div className="flex items-center space-x-1 text-red-400">
                <AlertCircle className="w-4 h-4" />
                <span className="text-xs">Parse Error</span>
              </div>
            )}
            {!parseError && (
              <div className="flex items-center space-x-1 text-green-400">
                <CheckCircle className="w-4 h-4" />
                <span className="text-xs">Valid</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Mode Toggle */}
            {showPreview && (
              <div className="flex bg-zinc-600 rounded-lg p-1">
                <button
                  onClick={() => setMode('edit')}
                  className={`px-3 py-1 text-xs rounded transition-colors ${
                    mode === 'edit' 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-300 hover:text-white'
                  }`}
                >
                  <Code className="w-3 h-3 inline mr-1" />
                  Edit
                </button>
                <button
                  onClick={() => setMode('preview')}
                  className={`px-3 py-1 text-xs rounded transition-colors ${
                    mode === 'preview' 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-300 hover:text-white'
                  }`}
                >
                  <Eye className="w-3 h-3 inline mr-1" />
                  Preview
                </button>
                <button
                  onClick={() => setMode('split')}
                  className={`px-3 py-1 text-xs rounded transition-colors ${
                    mode === 'split' 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-300 hover:text-white'
                  }`}
                >
                  <FileText className="w-3 h-3 inline mr-1" />
                  Split
                </button>
              </div>
            )}
            
            {/* Actions */}
            <button
              onClick={() => copyToClipboard(mode === 'preview' ? parsedContent : value)}
              className="text-gray-400 hover:text-white transition-colors"
              title="Copy content"
            >
              <Copy className="w-4 h-4" />
            </button>
            
            <button
              onClick={resetContent}
              className="text-gray-400 hover:text-white transition-colors"
              title="Reset content"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
            
            <button
              onClick={toggleFullscreen}
              className="text-gray-400 hover:text-white transition-colors"
              title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>
          </div>
        </div>

        {/* Variables Info */}
        {variables.length > 0 && (
          <div className="p-3 bg-zinc-750 border-b border-zinc-600">
            <div className="text-xs text-gray-400 mb-1">Available Variables:</div>
            <div className="flex flex-wrap gap-1">
              {variables.map((variable, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-900/30 text-blue-300 text-xs rounded cursor-pointer hover:bg-blue-900/50"
                  onClick={() => {
                    const newValue = value + `{${variable}}`;
                    onChange(newValue);
                  }}
                  title={`Click to insert {${variable}}`}
                >
                  {`{${variable}}`}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Editor Content */}
        <div className="relative">
          {mode === 'edit' && (
            <Editor
              height={editorHeight}
              defaultLanguage="text"
              theme="vs-dark"
              value={parsedContent} // Show parsed content for editing
              onChange={handleEditorChange}
              options={{
                readOnly,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                fontSize: 14,
                lineNumbers: 'on',
                wordWrap: 'on',
                automaticLayout: true,
                folding: true,
                renderWhitespace: 'selection',
                tabSize: 2,
                insertSpaces: true,
                bracketPairColorization: { enabled: true },
                suggest: {
                  showKeywords: false,
                  showSnippets: false,
                  showFunctions: false
                }
              }}
            />
          )}

          {mode === 'preview' && (
            <div 
              className="p-4 bg-zinc-900 text-gray-300 overflow-auto font-mono text-sm whitespace-pre-wrap"
              style={{ height: editorHeight }}
            >
              {parseError ? (
                <div className="text-red-400">
                  <div className="font-bold mb-2">Parse Error:</div>
                  <div>{parseError}</div>
                  <div className="mt-4 text-gray-400">Raw content:</div>
                  <div className="mt-2">{value}</div>
                </div>
              ) : (
                <div>
                  {parsedContent || (
                    <div className="text-gray-500 italic">{placeholder}</div>
                  )}
                </div>
              )}
            </div>
          )}

          {mode === 'split' && (
            <div className="flex" style={{ height: editorHeight }}>
              <div className="flex-1 border-r border-zinc-600">
                <Editor
                  height="100%"
                  defaultLanguage="text"
                  theme="vs-dark"
                  value={parsedContent}
                  onChange={handleEditorChange}
                  options={{
                    readOnly,
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 12,
                    lineNumbers: 'on',
                    wordWrap: 'on',
                    automaticLayout: true,
                    folding: true,
                    renderWhitespace: 'selection'
                  }}
                />
              </div>
              <div className="flex-1 p-4 bg-zinc-900 text-gray-300 overflow-auto font-mono text-xs whitespace-pre-wrap">
                {parseError ? (
                  <div className="text-red-400">
                    <div className="font-bold mb-2">Parse Error:</div>
                    <div>{parseError}</div>
                  </div>
                ) : (
                  parsedContent || (
                    <div className="text-gray-500 italic">{placeholder}</div>
                  )
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-2 border-t border-zinc-600 bg-zinc-750 text-xs text-gray-400 flex justify-between">
          <div>
            Lines: {parsedContent.split('\n').length} | 
            Characters: {parsedContent.length} |
            Words: {parsedContent.split(/\s+/).filter(w => w.length > 0).length}
          </div>
          <div>
            {mode === 'edit' ? 'Editing Mode' : mode === 'preview' ? 'Preview Mode' : 'Split Mode'}
          </div>
        </div>
      </div>
    </div>
  );
}
