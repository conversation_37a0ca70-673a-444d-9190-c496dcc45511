#!/usr/bin/env tsx

/**
 * Test UUID Generation and Function Fixes
 * 
 * This script tests both critical fixes:
 * 1. UUID generation in enhanced job queue
 * 2. Database function cleanup (UUID vs TEXT parameters)
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { getEnhancedJobQueue } from '../src/lib/jobs/enhanced-queue';
import { getBulkProcessingEngine } from '../src/lib/bulk-processing/bulk-engine';

// Load environment variables
config({ path: '.env.local' });

// Also try .env as fallback
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

function isValidUUID(str: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

async function testUUIDGeneration() {
  console.log('🔍 Testing UUID Generation Fix...');
  console.log('-'.repeat(50));

  try {
    const jobQueue = getEnhancedJobQueue();
    
    // Create a test job to verify UUID generation
    console.log('1️⃣ Creating test job with enhanced queue...');
    
    const testJob = await jobQueue.add('test', {
      testData: 'uuid-generation-test',
      timestamp: new Date().toISOString()
    }, {
      priority: 1,
      maxAttempts: 1
    });

    console.log(`   Job ID: ${testJob.id}`);
    console.log(`   Is Valid UUID: ${isValidUUID(testJob.id) ? '✅ YES' : '❌ NO'}`);
    
    if (!isValidUUID(testJob.id)) {
      console.log('❌ UUID generation fix failed - job ID is not a valid UUID');
      return false;
    }

    // Try to retrieve the job from database
    console.log('2️⃣ Retrieving job from database...');
    
    const retrievedJob = await jobQueue.getJob(testJob.id);
    if (retrievedJob) {
      console.log('✅ Job successfully stored and retrieved from database');
      console.log(`   Retrieved ID: ${retrievedJob.id}`);
      console.log(`   Status: ${retrievedJob.status}`);
    } else {
      console.log('❌ Could not retrieve job from database');
      return false;
    }

    // Clean up test job
    await supabase
      .from('ai_generation_jobs')
      .delete()
      .eq('id', testJob.id);

    console.log('✅ UUID generation test passed!');
    return true;

  } catch (error) {
    console.error('❌ UUID generation test failed:', error);
    return false;
  }
}

async function testDatabaseFunctions() {
  console.log('\n🔍 Testing Database Function Fixes...');
  console.log('-'.repeat(50));

  try {
    // Test 1: Check function parameter types
    console.log('1️⃣ Checking function parameter types...');
    
    const { data: functionParams, error: paramsError } = await supabase
      .from('information_schema.parameters')
      .select('specific_name, parameter_name, data_type')
      .eq('specific_schema', 'public')
      .like('specific_name', '%bulk%')
      .eq('parameter_name', 'p_job_id');

    if (paramsError) {
      console.log('⚠️  Could not check function parameters:', paramsError.message);
    } else if (functionParams) {
      console.log('   Function parameter types:');
      functionParams.forEach(param => {
        const status = param.data_type === 'uuid' ? '✅' : '❌';
        console.log(`     ${status} ${param.specific_name}: ${param.data_type}`);
      });
      
      const allUuid = functionParams.every(param => param.data_type === 'uuid');
      if (!allUuid) {
        console.log('❌ Some functions still have non-UUID parameters');
        return false;
      }
    }

    // Test 2: Test function calls with UUID
    console.log('2️⃣ Testing function calls with UUID parameters...');
    
    const testUuid = '00000000-0000-0000-0000-000000000000';
    
    const functionTests = [
      {
        name: 'update_bulk_job_status_atomic',
        params: { p_job_id: testUuid, p_new_status: 'processing' }
      },
      {
        name: 'update_bulk_job_progress_atomic',
        params: { 
          p_job_id: testUuid, 
          p_processed_items: 1, 
          p_successful_items: 1, 
          p_failed_items: 0 
        }
      },
      {
        name: 'complete_bulk_job_atomic',
        params: { p_job_id: testUuid }
      }
    ];

    for (const test of functionTests) {
      try {
        const { data: result, error } = await supabase.rpc(test.name, test.params);
        
        if (error) {
          console.log(`   ❌ ${test.name}: ${error.message}`);
          return false;
        } else if (result && result.error === 'job_not_found') {
          console.log(`   ✅ ${test.name}: Working correctly (expected job_not_found)`);
        } else {
          console.log(`   ⚠️  ${test.name}: Unexpected result:`, result);
        }
      } catch (funcError) {
        console.log(`   ❌ ${test.name}: Exception:`, funcError);
        return false;
      }
    }

    console.log('✅ Database function tests passed!');
    return true;

  } catch (error) {
    console.error('❌ Database function test failed:', error);
    return false;
  }
}

async function testBulkProcessingIntegration() {
  console.log('\n🔍 Testing Bulk Processing Integration...');
  console.log('-'.repeat(50));

  try {
    const bulkEngine = getBulkProcessingEngine();
    
    // Create a minimal test bulk job
    console.log('1️⃣ Creating test bulk processing job...');
    
    const testUrls = ['https://example.com/test-uuid-fix'];
    const bulkToolData = testUrls.map(url => ({
      url,
      providedData: { name: `Test Tool for UUID Fix` },
      needsGeneration: {
        description: false, // Disable to avoid external API calls
        features: false,
        pricing: false,
        prosAndCons: false,
        haiku: false,
        hashtags: false,
      },
    }));

    const bulkJob = await bulkEngine.createBulkJob(
      bulkToolData,
      {
        batchSize: 1,
        delayBetweenBatches: 500,
        enableRetry: true,
        maxRetries: 1,
        enableAIGeneration: false, // Disable to avoid API calls
        enableMediaCollection: false, // Disable to avoid external calls
      },
      {
        jobType: 'test',
        submittedBy: 'uuid-fix-test',
        metadata: {
          testMode: true,
          uuidFix: true
        }
      }
    );

    console.log(`   Bulk Job ID: ${bulkJob.id}`);
    console.log(`   Is Valid UUID: ${isValidUUID(bulkJob.id) ? '✅ YES' : '❌ NO'}`);
    console.log(`   Total Items: ${bulkJob.totalItems}`);

    // Monitor for a short time
    console.log('2️⃣ Monitoring bulk job progress...');
    
    let attempts = 0;
    const maxAttempts = 5;
    
    while (attempts < maxAttempts) {
      const jobStatus = await bulkEngine.getBulkJobStatus(bulkJob.id);
      
      console.log(`   Attempt ${attempts + 1}: Status = ${jobStatus.status}, Progress = ${jobStatus.processedItems}/${jobStatus.totalItems}`);
      
      if (jobStatus.status === 'completed' || jobStatus.status === 'failed') {
        break;
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;
    }

    // Clean up
    await supabase
      .from('bulk_processing_jobs')
      .delete()
      .eq('id', bulkJob.id);

    console.log('✅ Bulk processing integration test passed!');
    return true;

  } catch (error) {
    console.error('❌ Bulk processing integration test failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Testing UUID Generation and Database Function Fixes...');
  console.log('=' .repeat(70));

  const results = {
    uuidGeneration: false,
    databaseFunctions: false,
    bulkProcessingIntegration: false
  };

  // Run all tests
  results.uuidGeneration = await testUUIDGeneration();
  results.databaseFunctions = await testDatabaseFunctions();
  results.bulkProcessingIntegration = await testBulkProcessingIntegration();

  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('=' .repeat(70));
  console.log(`UUID Generation Fix:           ${results.uuidGeneration ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Database Function Fix:         ${results.databaseFunctions ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Bulk Processing Integration:   ${results.bulkProcessingIntegration ? '✅ PASSED' : '❌ FAILED'}`);

  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! Both critical issues have been resolved.');
    console.log('   • Enhanced job queue now generates proper UUIDs');
    console.log('   • Database functions use correct UUID parameter types');
    console.log('   • Bulk processing workflow works end-to-end');
  } else {
    console.log('\n❌ Some tests failed. Please review the issues above.');
    console.log('   • Check that Migration 006 was applied correctly');
    console.log('   • Ensure function cleanup SQL was executed in Supabase');
    console.log('   • Verify environment variables are configured properly');
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

export { main as testUuidAndFunctionsFix };
