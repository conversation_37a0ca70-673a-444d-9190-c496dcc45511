#!/usr/bin/env tsx

/**
 * Check Failed Scraping Jobs
 * 
 * This script checks for failed web scraping jobs and provides
 * retry options with the new timeout configuration.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function checkFailedScrapingJobs() {
  console.log('🔍 CHECKING FAILED SCRAPING JOBS\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. Find failed web scraping jobs
    console.log('1. 🔍 Finding failed web scraping jobs...');
    const { data: failedJobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .eq('job_type', 'web_scraping')
      .eq('status', 'failed')
      .order('created_at', { ascending: false })
      .limit(10);

    if (jobsError) {
      throw new Error(`Failed to fetch jobs: ${jobsError.message}`);
    }

    if (!failedJobs || failedJobs.length === 0) {
      console.log('✅ No failed web scraping jobs found');
      return;
    }

    console.log(`❌ Found ${failedJobs.length} failed web scraping job(s):\n`);

    // 2. Analyze each failed job
    failedJobs.forEach((job, index) => {
      console.log(`   Job ${index + 1}: ${job.id}`);
      console.log(`      📋 Type: ${job.job_type}`);
      console.log(`      📊 Status: ${job.status}`);
      console.log(`      🔗 Tool ID: ${job.tool_id}`);
      console.log(`      🔄 Attempts: ${job.attempts}/${job.max_attempts}`);
      console.log(`      📅 Created: ${job.created_at}`);
      console.log(`      📅 Updated: ${job.updated_at}`);
      
      if (job.error_logs) {
        const errorLogs = typeof job.error_logs === 'string' 
          ? job.error_logs 
          : JSON.stringify(job.error_logs);
        
        const isTimeout = errorLogs.includes('timeout') || errorLogs.includes('Request timeout');
        console.log(`      ❌ Error: ${errorLogs.substring(0, 100)}...`);
        console.log(`      ⏰ Timeout Error: ${isTimeout ? 'YES' : 'NO'}`);
        
        if (isTimeout) {
          console.log(`      💡 Should benefit from timeout fix`);
        }
      }
      console.log('');
    });

    // 3. Check related tools
    console.log('2. 🔍 Checking related tools...');
    const toolIds = failedJobs.map(job => job.tool_id).filter(Boolean);
    
    if (toolIds.length > 0) {
      const { data: tools, error: toolsError } = await supabase
        .from('tools')
        .select('*')
        .in('id', toolIds);

      if (toolsError) {
        console.log(`⚠️ Could not fetch related tools: ${toolsError.message}`);
      } else if (tools && tools.length > 0) {
        console.log(`✅ Found ${tools.length} related tool(s):\n`);
        
        tools.forEach((tool, index) => {
          console.log(`   Tool ${index + 1}: ${tool.name} (${tool.id})`);
          console.log(`      🌐 Website: ${tool.website}`);
          console.log(`      📊 Content Status: ${tool.content_status}`);
          console.log(`      🤖 AI Status: ${tool.ai_generation_status}`);
          console.log('');
        });
      }
    }

    // 4. Provide retry recommendations
    console.log('3. 🔧 RETRY RECOMMENDATIONS\n');
    
    const timeoutJobs = failedJobs.filter(job => {
      const errorLogs = typeof job.error_logs === 'string' 
        ? job.error_logs 
        : JSON.stringify(job.error_logs || '');
      return errorLogs.includes('timeout') || errorLogs.includes('Request timeout');
    });

    if (timeoutJobs.length > 0) {
      console.log(`⏰ ${timeoutJobs.length} job(s) failed due to timeout:`);
      timeoutJobs.forEach(job => {
        console.log(`   • ${job.id} - Should retry with new 90s timeout`);
      });
      console.log('');
      console.log('🚀 To retry these jobs:');
      console.log('   1. Visit http://localhost:3000/admin/jobs');
      console.log('   2. Find the failed jobs and click "Retry"');
      console.log('   3. Jobs will use the new 90-second timeout');
      console.log('   4. Monitor for successful completion');
    }

    const otherJobs = failedJobs.filter(job => {
      const errorLogs = typeof job.error_logs === 'string' 
        ? job.error_logs 
        : JSON.stringify(job.error_logs || '');
      return !errorLogs.includes('timeout') && !errorLogs.includes('Request timeout');
    });

    if (otherJobs.length > 0) {
      console.log(`🔧 ${otherJobs.length} job(s) failed for other reasons:`);
      otherJobs.forEach(job => {
        console.log(`   • ${job.id} - May need different troubleshooting`);
      });
    }

    console.log('\n🎯 Summary:');
    console.log(`   📊 Total failed jobs: ${failedJobs.length}`);
    console.log(`   ⏰ Timeout-related: ${timeoutJobs.length}`);
    console.log(`   🔧 Other issues: ${otherJobs.length}`);
    console.log(`   ✅ Timeout fixes should resolve ${timeoutJobs.length} job(s)`);

  } catch (error) {
    console.error('💥 Check failed:', error);
  }
}

// Run the check
checkFailedScrapingJobs().catch(console.error);
