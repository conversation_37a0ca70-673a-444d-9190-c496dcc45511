#!/usr/bin/env tsx

/**
 * Debug Submission Source Issue
 * 
 * This script investigates why the submission source detection is failing
 * for bulk processing tools.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function debugSubmissionSourceIssue() {
  console.log('🔍 DEBUGGING SUBMISSION SOURCE ISSUE\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. Check the specific tool from the logs
    const toolId = '34f7391e-76bb-4ac8-9a48-496a9b6a3d1e';
    
    console.log(`1. 🔍 Checking tool ${toolId}...`);
    const { data: tool, error: toolError } = await supabase
      .from('tools')
      .select('*')
      .eq('id', toolId)
      .single();

    if (toolError) {
      console.log(`❌ Error fetching tool: ${toolError.message}`);
    } else if (tool) {
      console.log(`✅ Tool found: ${tool.name}`);
      console.log(`   🌐 Website: ${tool.website}`);
      console.log(`   📊 Content Status: ${tool.content_status}`);
      console.log(`   🤖 AI Status: ${tool.ai_generation_status}`);
      console.log(`   📝 Submission Type: ${tool.submission_type}`);
      console.log(`   📋 Submission Source: ${tool.submission_source}`);
      console.log(`   📅 Created: ${tool.created_at}`);
      console.log(`   📅 Updated: ${tool.updated_at}`);
      
      if (tool.submission_source !== 'bulk_processing') {
        console.log(`   ❌ ISSUE: Tool has submission source "${tool.submission_source}" instead of "bulk_processing"`);
        
        // Fix it
        console.log(`   🔧 Fixing submission source...`);
        const { error: updateError } = await supabase
          .from('tools')
          .update({
            submission_source: 'bulk_processing',
            submission_type: 'admin',
            updated_at: new Date().toISOString()
          })
          .eq('id', toolId);

        if (updateError) {
          console.log(`   ❌ Failed to update: ${updateError.message}`);
        } else {
          console.log(`   ✅ Updated tool to have submission_source = 'bulk_processing'`);
        }
      } else {
        console.log(`   ✅ Tool has correct submission_source`);
      }
    } else {
      console.log('❌ Tool not found');
    }

    // 2. Check all FoodiePrep tools
    console.log('\n2. 🔍 Checking all FoodiePrep tools...');
    const { data: foodieTools, error: foodieError } = await supabase
      .from('tools')
      .select('*')
      .or('name.ilike.%foodieprep%,website.ilike.%foodieprep%')
      .order('created_at', { ascending: false });

    if (foodieError) {
      console.log(`❌ Error fetching FoodiePrep tools: ${foodieError.message}`);
    } else if (foodieTools && foodieTools.length > 0) {
      console.log(`✅ Found ${foodieTools.length} FoodiePrep tool(s):\n`);
      
      foodieTools.forEach((tool, index) => {
        console.log(`   Tool ${index + 1}: ${tool.name} (${tool.id})`);
        console.log(`      🌐 Website: ${tool.website}`);
        console.log(`      📊 Content Status: ${tool.content_status}`);
        console.log(`      🤖 AI Status: ${tool.ai_generation_status}`);
        console.log(`      📝 Submission Type: ${tool.submission_type}`);
        console.log(`      📋 Submission Source: ${tool.submission_source}`);
        console.log(`      📅 Created: ${tool.created_at}`);
        
        if (tool.submission_source !== 'bulk_processing') {
          console.log(`      ❌ NEEDS FIX: submission_source is "${tool.submission_source}"`);
        } else {
          console.log(`      ✅ Correct submission_source`);
        }
        console.log('');
      });
    }

    // 3. Check recent AI generation jobs
    console.log('3. 🔍 Checking recent AI generation jobs...');
    const { data: jobs, error: jobError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (jobError) {
      console.log(`❌ Error fetching jobs: ${jobError.message}`);
    } else if (jobs && jobs.length > 0) {
      console.log(`✅ Found ${jobs.length} recent job(s):\n`);
      
      jobs.forEach((job, index) => {
        console.log(`   Job ${index + 1}: ${job.id}`);
        console.log(`      📋 Type: ${job.job_type}`);
        console.log(`      📊 Status: ${job.status}`);
        console.log(`      🔗 Tool ID: ${job.tool_id}`);
        console.log(`      📅 Created: ${job.created_at}`);
        console.log('');
      });
    }

    // 4. Check bulk processing jobs
    console.log('4. 🔍 Checking bulk processing jobs...');
    const { data: bulkJobs, error: bulkError } = await supabase
      .from('bulk_processing_jobs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5);

    if (bulkError) {
      console.log(`❌ Error fetching bulk jobs: ${bulkError.message}`);
    } else if (bulkJobs && bulkJobs.length > 0) {
      console.log(`✅ Found ${bulkJobs.length} bulk processing job(s):\n`);
      
      bulkJobs.forEach((job, index) => {
        console.log(`   Bulk Job ${index + 1}: ${job.id}`);
        console.log(`      📊 Status: ${job.status}`);
        console.log(`      📋 Total Items: ${job.total_items}`);
        console.log(`      ✅ Processed: ${job.processed_items}`);
        console.log(`      📅 Created: ${job.created_at}`);
        console.log('');
      });
    }

    console.log('🎯 Summary:');
    console.log('   The issue is that tools created through bulk processing');
    console.log('   are not getting submission_source = "bulk_processing" set correctly.');
    console.log('   This causes the pipeline to default to user submission workflow.');
    console.log('');
    console.log('🔧 Root Cause Analysis Needed:');
    console.log('   1. Check tool creation in bulk processing workflow');
    console.log('   2. Verify submission_source is set during tool creation');
    console.log('   3. Check if existing tools are being updated properly');

  } catch (error) {
    console.error('💥 Debug failed:', error);
  }
}

// Run the debug
debugSubmissionSourceIssue().catch(console.error);
