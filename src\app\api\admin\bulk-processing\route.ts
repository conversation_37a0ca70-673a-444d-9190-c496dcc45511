/**
 * Bulk Processing API Routes
 * Handles bulk processing operations including file uploads and job management
 */

import { NextRequest, NextResponse } from 'next/server';
import { getBulkProcessingEngine } from '@/lib/bulk-processing/bulk-engine';
import { TextFileProcessor, JSONFileProcessor, ManualEntryProcessor } from '@/lib/bulk-processing/file-processors';
import { BulkProcessingOptions } from '@/lib/bulk-processing/bulk-engine';

// Admin API key validation
function validateAdminApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('x-admin-api-key');
  const expectedKey = process.env.ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
  return apiKey === expectedKey;
}

// Processing options validation
function validateProcessingOptions(options: any): {
  isValid: boolean;
  errors: string[];
  sanitized: BulkProcessingOptions;
} {
  const errors: string[] = [];
  const sanitized: BulkProcessingOptions = {
    batchSize: 5,
    delayBetweenBatches: 2000,
    retryAttempts: 3,
    aiProvider: 'openai',
    skipExisting: false,
    scrapeOnly: false,
    generateContent: true,
    resumeGeneration: false,
    autoPublish: false,
    priority: 'normal',
  };

  // Validate batchSize
  if (options.batchSize !== undefined) {
    const batchSize = parseInt(options.batchSize);
    if (isNaN(batchSize) || batchSize < 1 || batchSize > 20) {
      errors.push('batchSize must be a number between 1 and 20');
    } else {
      sanitized.batchSize = batchSize;
    }
  }

  // Validate delayBetweenBatches
  if (options.delayBetweenBatches !== undefined) {
    const delay = parseInt(options.delayBetweenBatches);
    if (isNaN(delay) || delay < 0 || delay > 60000) {
      errors.push('delayBetweenBatches must be a number between 0 and 60000');
    } else {
      sanitized.delayBetweenBatches = delay;
    }
  }

  // Validate retryAttempts
  if (options.retryAttempts !== undefined) {
    const retries = parseInt(options.retryAttempts);
    if (isNaN(retries) || retries < 0 || retries > 10) {
      errors.push('retryAttempts must be a number between 0 and 10');
    } else {
      sanitized.retryAttempts = retries;
    }
  }

  // Validate aiProvider
  if (options.aiProvider !== undefined) {
    if (!['openai', 'openrouter'].includes(options.aiProvider)) {
      errors.push('aiProvider must be either "openai" or "openrouter"');
    } else {
      sanitized.aiProvider = options.aiProvider;
    }
  }

  // Validate priority
  if (options.priority !== undefined) {
    if (!['low', 'normal', 'high'].includes(options.priority)) {
      errors.push('priority must be "low", "normal", or "high"');
    } else {
      sanitized.priority = options.priority;
    }
  }

  // Validate boolean options
  const booleanOptions = ['skipExisting', 'scrapeOnly', 'generateContent', 'resumeGeneration', 'autoPublish'];
  booleanOptions.forEach(option => {
    if (options[option] !== undefined) {
      if (typeof options[option] !== 'boolean') {
        errors.push(`${option} must be a boolean`);
      } else {
        sanitized[option as keyof BulkProcessingOptions] = options[option];
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    sanitized,
  };
}

/**
 * GET /api/admin/bulk-processing
 * Get bulk processing jobs with filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin access
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    const bulkEngine = getBulkProcessingEngine();
    const jobs = await bulkEngine.getBulkJobs({
      status: status as any,
      limit,
      offset,
    });

    // Convert camelCase fields to snake_case for frontend compatibility
    const convertedJobs = jobs.map(job => ({
      id: job.id,
      job_type: job.jobType,
      status: job.status,
      total_items: job.totalItems,
      processed_items: job.processedItems,
      successful_items: job.successfulItems,
      failed_items: job.failedItems,
      source_data: job.sourceData,
      processing_options: job.processingOptions,
      results: job.results,
      progress_log: job.progressLog,
      created_by: job.createdBy,
      created_at: job.createdAt,
      updated_at: job.updatedAt,
      started_at: job.startedAt,
      completed_at: job.completedAt,
    }));

    console.log(`📊 API returning ${convertedJobs.length} bulk processing jobs with snake_case fields`);

    return NextResponse.json({
      success: true,
      data: {
        jobs: convertedJobs,
        pagination: {
          limit,
          offset,
          total: jobs.length, // TODO: Get actual total count
        },
      },
    });
  } catch (error) {
    console.error('Failed to get bulk processing jobs:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get bulk processing jobs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/bulk-processing
 * Create new bulk processing job
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin access
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const contentType = request.headers.get('content-type') || '';

    // Handle file upload (multipart/form-data)
    if (contentType.includes('multipart/form-data')) {
      return await handleFileUpload(request);
    }

    // Handle JSON data (manual entry or API submission)
    if (contentType.includes('application/json')) {
      return await handleJSONSubmission(request);
    }

    return NextResponse.json(
      { success: false, error: 'Unsupported content type' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Failed to create bulk processing job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create bulk processing job' },
      { status: 500 }
    );
  }
}

/**
 * Handle file upload processing
 */
async function handleFileUpload(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const optionsJson = formData.get('options') as string;

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Parse processing options
    let options: BulkProcessingOptions;
    try {
      options = JSON.parse(optionsJson || '{}');
    } catch {
      return NextResponse.json(
        { success: false, error: 'Invalid options JSON' },
        { status: 400 }
      );
    }

    // Set default options
    const processingOptions: BulkProcessingOptions = {
      batchSize: options.batchSize || 5,
      delayBetweenBatches: options.delayBetweenBatches || 2000,
      retryAttempts: options.retryAttempts || 3,
      aiProvider: options.aiProvider || 'openai',
      skipExisting: options.skipExisting || false,
      scrapeOnly: options.scrapeOnly || false,
      generateContent: options.generateContent !== false, // Default to true
      resumeGeneration: options.resumeGeneration || false,
      autoPublish: options.autoPublish || false,
      priority: options.priority || 'normal',
    };

    // Determine file type and process
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    let processor;
    let jobType: 'text_file' | 'json_file';

    if (fileExtension === '.txt') {
      processor = new TextFileProcessor();
      jobType = 'text_file';
    } else if (fileExtension === '.json') {
      processor = new JSONFileProcessor();
      jobType = 'json_file';
    } else {
      return NextResponse.json(
        { success: false, error: `Unsupported file type: ${fileExtension}` },
        { status: 400 }
      );
    }

    // Process file
    const processingResult = await processor.processFile(file);

    if (!processingResult.success || !processingResult.data) {
      return NextResponse.json(
        { 
          success: false, 
          error: processingResult.error || 'File processing failed',
          validation: processingResult.validation,
        },
        { status: 400 }
      );
    }

    // Create bulk processing job
    const bulkEngine = getBulkProcessingEngine();
    const bulkJob = await bulkEngine.createBulkJob(
      processingResult.data.validItems,
      processingOptions,
      {
        jobType,
        filename: file.name,
        submittedBy: 'admin', // TODO: Get actual user from auth
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        job: bulkJob,
        fileProcessing: {
          totalItems: processingResult.data.totalItems,
          validItems: processingResult.data.validItems.length,
          invalidItems: processingResult.data.invalidItems.length,
          duplicatesRemoved: processingResult.data.duplicatesRemoved,
        },
        validation: processingResult.validation,
      },
    });
  } catch (error) {
    console.error('File upload processing failed:', error);
    return NextResponse.json(
      { success: false, error: 'File upload processing failed' },
      { status: 500 }
    );
  }
}

/**
 * Handle JSON submission (manual entry or API)
 */
async function handleJSONSubmission(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, data, options } = body;

    if (!type || !data) {
      console.error('❌ JSON submission validation failed: Missing required fields', { type, dataType: typeof data });
      return NextResponse.json(
        { success: false, error: 'Missing required fields: type, data' },
        { status: 400 }
      );
    }

    // Validate and sanitize options
    const optionsValidation = validateProcessingOptions(options || {});
    if (!optionsValidation.isValid) {
      console.error('❌ Processing options validation failed:', optionsValidation.errors);
      return NextResponse.json(
        { success: false, error: `Invalid processing options: ${optionsValidation.errors.join(', ')}` },
        { status: 400 }
      );
    }

    // Set validated processing options
    const processingOptions: BulkProcessingOptions = optionsValidation.sanitized;

    let processingResult;

    if (type === 'manual_entry') {
      // Handle manual URL entry
      console.log('🔄 Processing manual entry data');
      const processor = new ManualEntryProcessor();
      const inputData = data.urls || data;

      if (typeof inputData !== 'string') {
        console.error('❌ Manual entry data validation failed: Expected string input', { inputType: typeof inputData });
        return NextResponse.json(
          { success: false, error: 'Manual entry data must be a string' },
          { status: 400 }
        );
      }

      processingResult = processor.processInput(inputData);
    } else if (type === 'json_data') {
      // Handle JSON data submission
      console.log('🔄 Processing JSON data submission');
      const processor = new JSONFileProcessor();
      // Create a mock file object for processing
      const jsonString = JSON.stringify(data);
      const mockFile = new File([jsonString], 'data.json', { type: 'application/json' });
      processingResult = await processor.processFile(mockFile);
    } else if (type === 'processed_data') {
      // Handle pre-processed data from dashboard
      console.log('🔄 Processing pre-processed data from dashboard');

      // Validate data structure
      if (!Array.isArray(data)) {
        console.error('❌ Processed data validation failed: Expected array', { dataType: typeof data, isArray: Array.isArray(data) });
        return NextResponse.json(
          { success: false, error: 'Processed data must be an array' },
          { status: 400 }
        );
      }

      if (data.length === 0) {
        console.error('❌ Processed data validation failed: Empty array');
        return NextResponse.json(
          { success: false, error: 'Processed data array cannot be empty' },
          { status: 400 }
        );
      }

      const processor = new ManualEntryProcessor();
      const urls = data.map((item: any) => item.url || item).filter(Boolean);

      if (urls.length === 0) {
        console.error('❌ URL extraction failed: No valid URLs found in processed data', { dataLength: data.length });
        return NextResponse.json(
          { success: false, error: 'No valid URLs found in processed data' },
          { status: 400 }
        );
      }

      console.log(`📋 Extracted ${urls.length} URLs from processed data`);

      // Fix: Convert URL array to newline-separated string
      processingResult = processor.processInput(urls.join('\n'));
    } else {
      console.error('❌ Unsupported submission type:', type);
      return NextResponse.json(
        { success: false, error: `Unsupported submission type: ${type}` },
        { status: 400 }
      );
    }

    if (!processingResult.success || !processingResult.data) {
      console.error('❌ Data processing failed:', {
        success: processingResult.success,
        error: processingResult.error,
        hasData: !!processingResult.data,
        validation: processingResult.validation,
        type
      });
      return NextResponse.json(
        {
          success: false,
          error: processingResult.error || 'Data processing failed',
          validation: processingResult.validation,
        },
        { status: 400 }
      );
    }

    console.log(`✅ Data processing successful: ${processingResult.data.validItems.length} valid items, ${processingResult.data.invalidItems.length} invalid items`);

    // Create bulk processing job
    const bulkEngine = getBulkProcessingEngine();
    const bulkJob = await bulkEngine.createBulkJob(
      processingResult.data.validItems,
      processingOptions,
      {
        jobType: 'manual_entry',
        submittedBy: 'admin', // TODO: Get actual user from auth
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        job: bulkJob,
        processing: {
          totalItems: processingResult.data.totalItems,
          validItems: processingResult.data.validItems.length,
          invalidItems: processingResult.data.invalidItems.length,
          duplicatesRemoved: processingResult.data.duplicatesRemoved,
        },
        validation: processingResult.validation,
      },
    });
  } catch (error) {
    console.error('❌ JSON submission processing failed:', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'JSON submission processing failed'
      },
      { status: 500 }
    );
  }
}
