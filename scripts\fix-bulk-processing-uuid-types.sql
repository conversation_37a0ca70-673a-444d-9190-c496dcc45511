-- =====================================================
-- BULK PROCESSING UUID TYPE FIX
-- =====================================================
-- Execute this SQL in your Supabase SQL Editor to fix
-- the UUID/TEXT parameter type mismatch in bulk processing functions
-- =====================================================

-- Update atomic job status update function with UUID parameter
CREATE OR REPLACE FUNCTION update_bulk_job_status_atomic(
    p_job_id UUID,
    p_new_status TEXT,
    p_expected_version INTEGER DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_current_version INTEGER;
    v_current_status TEXT;
    v_updated_at TIMESTAMP;
BEGIN
    -- Get current version and status
    SELECT version, status INTO v_current_version, v_current_status
    FROM bulk_processing_jobs
    WHERE id = p_job_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'job_not_found',
            'message', 'Job not found: ' || p_job_id::text
        );
    END IF;
    
    -- Check version if provided (optimistic locking)
    IF p_expected_version IS NOT NULL AND v_current_version != p_expected_version THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'version_mismatch',
            'message', 'Job was modified by another process',
            'current_version', v_current_version,
            'expected_version', p_expected_version
        );
    END IF;
    
    -- Validate status transition
    IF NOT is_valid_status_transition(v_current_status, p_new_status) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'invalid_transition',
            'message', 'Invalid status transition from ' || v_current_status || ' to ' || p_new_status
        );
    END IF;
    
    -- Update with version increment
    v_updated_at := NOW();
    UPDATE bulk_processing_jobs 
    SET 
        status = p_new_status,
        version = version + 1,
        updated_at = v_updated_at
    WHERE id = p_job_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'job_id', p_job_id::text,
        'old_status', v_current_status,
        'new_status', p_new_status,
        'old_version', v_current_version,
        'new_version', v_current_version + 1,
        'updated_at', v_updated_at
    );
END;
$$;

-- Update atomic progress update function with UUID parameter
CREATE OR REPLACE FUNCTION update_bulk_job_progress_atomic(
    p_job_id UUID,
    p_processed_items INTEGER,
    p_successful_items INTEGER,
    p_failed_items INTEGER,
    p_expected_version INTEGER DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_current_version INTEGER;
    v_updated_at TIMESTAMP;
BEGIN
    -- Get current version
    SELECT version INTO v_current_version
    FROM bulk_processing_jobs
    WHERE id = p_job_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'job_not_found'
        );
    END IF;
    
    -- Check version if provided
    IF p_expected_version IS NOT NULL AND v_current_version != p_expected_version THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'version_mismatch',
            'current_version', v_current_version
        );
    END IF;
    
    -- Update progress atomically
    v_updated_at := NOW();
    UPDATE bulk_processing_jobs 
    SET 
        processed_items = p_processed_items,
        successful_items = p_successful_items,
        failed_items = p_failed_items,
        version = version + 1,
        updated_at = v_updated_at
    WHERE id = p_job_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'job_id', p_job_id::text,
        'new_version', v_current_version + 1,
        'updated_at', v_updated_at
    );
END;
$$;

-- Update function to append to progress log atomically with UUID parameter
CREATE OR REPLACE FUNCTION append_bulk_job_log_atomic(
    p_job_id UUID,
    p_log_entry JSONB,
    p_expected_version INTEGER DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_current_version INTEGER;
    v_current_log JSONB;
    v_updated_at TIMESTAMP;
BEGIN
    -- Get current version and log
    SELECT version, progress_log INTO v_current_version, v_current_log
    FROM bulk_processing_jobs
    WHERE id = p_job_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'job_not_found'
        );
    END IF;
    
    -- Check version if provided
    IF p_expected_version IS NOT NULL AND v_current_version != p_expected_version THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'version_mismatch',
            'current_version', v_current_version
        );
    END IF;
    
    -- Initialize log if null
    IF v_current_log IS NULL THEN
        v_current_log := '[]'::jsonb;
    END IF;
    
    -- Append new entry to log
    v_updated_at := NOW();
    UPDATE bulk_processing_jobs 
    SET 
        progress_log = v_current_log || jsonb_build_array(p_log_entry),
        version = version + 1,
        updated_at = v_updated_at
    WHERE id = p_job_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'job_id', p_job_id::text,
        'new_version', v_current_version + 1,
        'updated_at', v_updated_at
    );
END;
$$;

-- Update function to complete job atomically with UUID parameter
CREATE OR REPLACE FUNCTION complete_bulk_job_atomic(
    p_job_id UUID,
    p_expected_version INTEGER DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_current_version INTEGER;
    v_current_status TEXT;
    v_completed_at TIMESTAMP;
    v_updated_at TIMESTAMP;
BEGIN
    -- Get current version and status
    SELECT version, status INTO v_current_version, v_current_status
    FROM bulk_processing_jobs
    WHERE id = p_job_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'job_not_found'
        );
    END IF;
    
    -- Check version if provided
    IF p_expected_version IS NOT NULL AND v_current_version != p_expected_version THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'version_mismatch',
            'current_version', v_current_version
        );
    END IF;
    
    -- Validate current status
    IF v_current_status NOT IN ('processing', 'paused') THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'invalid_status',
            'message', 'Cannot complete job with status: ' || v_current_status
        );
    END IF;
    
    -- Complete job atomically
    v_completed_at := NOW();
    v_updated_at := v_completed_at;
    
    UPDATE bulk_processing_jobs 
    SET 
        status = 'completed',
        completed_at = v_completed_at,
        version = version + 1,
        updated_at = v_updated_at
    WHERE id = p_job_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'job_id', p_job_id::text,
        'old_status', v_current_status,
        'new_status', 'completed',
        'new_version', v_current_version + 1,
        'completed_at', v_completed_at
    );
END;
$$;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these to verify the functions were updated successfully

-- Check function signatures to confirm UUID parameter types
SELECT 
    routine_name,
    parameter_name,
    data_type,
    ordinal_position
FROM information_schema.parameters 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%bulk%'
AND parameter_name = 'p_job_id'
ORDER BY routine_name, ordinal_position;

-- Test the status update function with a UUID (should return job_not_found)
SELECT update_bulk_job_status_atomic('00000000-0000-0000-0000-000000000000'::uuid, 'processing');

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
-- If all queries executed successfully, the bulk processing
-- functions now use correct UUID parameter types!
-- =====================================================
