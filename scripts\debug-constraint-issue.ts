#!/usr/bin/env tsx

/**
 * Debug Constraint Issue
 * 
 * This script specifically debugs why the job_type constraint is still
 * failing even though the constraint definition shows 'tool_processing'
 * is included.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function debugConstraintIssue() {
  console.log('🔍 Debugging Constraint Issue...');
  console.log('=' .repeat(60));

  try {
    // Step 1: Check all constraints on the table
    console.log('1️⃣ Checking all constraints on ai_generation_jobs table...');
    
    const { data: allConstraints, error: constraintError } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            conname as constraint_name,
            pg_get_constraintdef(oid) as constraint_definition,
            contype as constraint_type
          FROM pg_constraint 
          WHERE conrelid = 'ai_generation_jobs'::regclass
          ORDER BY conname
        `
      });

    if (constraintError) {
      console.log('❌ Could not check constraints:', constraintError.message);
      
      // Fallback method
      console.log('   Trying fallback method...');
      const { data: fallbackConstraints } = await supabase
        .from('information_schema.table_constraints')
        .select('constraint_name, constraint_type')
        .eq('table_name', 'ai_generation_jobs')
        .eq('table_schema', 'public');
      
      if (fallbackConstraints) {
        console.log('   📋 Constraints found (fallback):');
        fallbackConstraints.forEach(constraint => {
          console.log(`     • ${constraint.constraint_name} (${constraint.constraint_type})`);
        });
      }
    } else if (allConstraints) {
      console.log('   📋 All constraints on ai_generation_jobs:');
      allConstraints.forEach(constraint => {
        console.log(`     • ${constraint.constraint_name} (${constraint.constraint_type})`);
        console.log(`       Definition: ${constraint.constraint_definition}`);
        
        if (constraint.constraint_name.includes('job_type')) {
          if (constraint.constraint_definition.includes('tool_processing')) {
            console.log('       ✅ Contains "tool_processing"');
          } else {
            console.log('       ❌ Does NOT contain "tool_processing"');
          }
        }
      });
    }

    // Step 2: Check table schema
    console.log('\n2️⃣ Checking table schema...');
    
    const { data: columns, error: columnError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_name', 'ai_generation_jobs')
      .eq('table_schema', 'public')
      .order('ordinal_position');

    if (columnError) {
      console.log('❌ Could not check table schema:', columnError.message);
    } else if (columns) {
      console.log('   📋 Table columns:');
      columns.forEach(column => {
        console.log(`     • ${column.column_name}: ${column.data_type} ${column.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
    }

    // Step 3: Try inserting with minimal data
    console.log('\n3️⃣ Testing minimal job insertion...');
    
    const testJobId = generateUUID();
    
    // Try with absolute minimal required fields
    const minimalJob = {
      id: testJobId,
      job_type: 'tool_processing',
      status: 'pending'
    };

    console.log('   Attempting to insert minimal job:', JSON.stringify(minimalJob, null, 2));
    
    const { error: insertError } = await supabase
      .from('ai_generation_jobs')
      .insert(minimalJob);

    if (insertError) {
      console.log('❌ Minimal job insertion failed:', insertError.message);
      console.log('   Error code:', insertError.code);
      console.log('   Error details:', insertError.details);
      console.log('   Error hint:', insertError.hint);
      
      // Try with different job types to isolate the issue
      console.log('\n   Testing other job types...');
      
      const otherJobTypes = ['scrape', 'generate', 'bulk', 'media_extraction'];
      
      for (const jobType of otherJobTypes) {
        const otherTestJobId = generateUUID();
        const { error } = await supabase
          .from('ai_generation_jobs')
          .insert({
            id: otherTestJobId,
            job_type: jobType,
            status: 'pending'
          });

        if (error) {
          console.log(`   ❌ Job type '${jobType}' also failed:`, error.message);
        } else {
          console.log(`   ✅ Job type '${jobType}' works`);
          // Clean up
          await supabase.from('ai_generation_jobs').delete().eq('id', otherTestJobId);
        }
      }
    } else {
      console.log('✅ Minimal job insertion successful!');
      
      // Verify the job was stored
      const { data: retrievedJob } = await supabase
        .from('ai_generation_jobs')
        .select('*')
        .eq('id', testJobId)
        .single();

      if (retrievedJob) {
        console.log('✅ Job retrieved successfully:');
        console.log(`   ID: ${retrievedJob.id}`);
        console.log(`   Type: ${retrievedJob.job_type}`);
        console.log(`   Status: ${retrievedJob.status}`);
      }

      // Clean up
      await supabase.from('ai_generation_jobs').delete().eq('id', testJobId);
      console.log('✅ Test job cleaned up');
    }

    // Step 4: Check for any triggers or additional constraints
    console.log('\n4️⃣ Checking for triggers and additional constraints...');
    
    const { data: triggers } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            trigger_name,
            event_manipulation,
            action_statement
          FROM information_schema.triggers 
          WHERE event_object_table = 'ai_generation_jobs'
        `
      });

    if (triggers && triggers.length > 0) {
      console.log('   📋 Triggers found:');
      triggers.forEach(trigger => {
        console.log(`     • ${trigger.trigger_name} (${trigger.event_manipulation})`);
      });
    } else {
      console.log('   ✅ No triggers found on ai_generation_jobs table');
    }

    // Step 5: Test the exact constraint
    console.log('\n5️⃣ Testing constraint directly...');
    
    const { data: constraintTest } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            'tool_processing'::text = ANY ((ARRAY['scrape'::character varying, 'generate'::character varying, 'bulk'::character varying, 'media_extraction'::character varying, 'tool_processing'::character varying])::text[]) as constraint_passes
        `
      });

    if (constraintTest && constraintTest.length > 0) {
      console.log(`   Constraint test result: ${constraintTest[0].constraint_passes ? '✅ PASSES' : '❌ FAILS'}`);
    }

    console.log('\n📊 Debug Summary:');
    console.log('=' .repeat(60));
    console.log('✅ Constraint definition includes "tool_processing"');
    console.log('✅ Version mismatch functions work correctly');
    console.log('❓ Job insertion still fails - investigating root cause');

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
if (require.main === module) {
  debugConstraintIssue().catch(console.error);
}

export { debugConstraintIssue };
