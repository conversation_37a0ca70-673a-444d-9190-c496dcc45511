import { 
  ModelSelectionCriteria, 
  ModelConfig, 
  AIProvider,
  AIModel,
  OPENAI_CONFIG,
  OPENROUTER_CONFIG 
} from './types';

export class ModelSelector {

  /**
   * Select model for a specific provider (respects user choice)
   */
  static selectModelForProvider(provider: 'openai' | 'openrouter', criteria: ModelSelectionCriteria): ModelConfig {
    const { contentSize, complexity, priority } = criteria;

    if (provider === 'openai') {
      return {
        provider: 'openai',
        model: 'gpt-4o-2024-11-20',
        maxTokens: OPENAI_CONFIG.maxOutputTokens,
        reasoning: `User selected OpenAI provider - using GPT-4o with ${OPENAI_CONFIG.maxOutputTokens} output tokens`
      };
    } else {
      return {
        provider: 'openrouter',
        model: 'google/gemini-2.5-pro-preview',
        maxTokens: OPENROUTER_CONFIG.maxOutputTokens,
        reasoning: `User selected OpenRouter provider - using Gemini 2.5 Pro with ${OPENROUTER_CONFIG.maxOutputTokens} output tokens`
      };
    }
  }

  /**
   * Select the optimal AI model based on content characteristics and requirements
   * Implements quality-first optimization principle - never compromise on AI content generation quality
   */
  static selectOptimalModel(criteria: ModelSelectionCriteria): ModelConfig {
    const { 
      contentSize, 
      complexity, 
      priority, 
      scrapingCost = 0, 
      contentQuality = 70 
    } = criteria;

    // Quality-First Strategy: Always use optimal models for AI processing
    // While we optimize scraping costs aggressively, AI processing maintains high standards

    // Strategy 1: High-cost scraping deserves premium AI processing
    if (scrapingCost > 3) {
      return {
        provider: 'openrouter',
        model: 'google/gemini-2.5-pro-preview',
        maxTokens: OPENROUTER_CONFIG.maxOutputTokens,
        reasoning: 'High-quality AI processing to justify expensive scraping cost - using Gemini 2.5 Pro with 65K output tokens'
      };
    }

    // Strategy 2: Large content or complex tasks need maximum context window
    if (contentSize > 100000 || complexity === 'complex') {
      return {
        provider: 'openrouter',
        model: 'google/gemini-2.5-pro-preview',
        maxTokens: OPENROUTER_CONFIG.maxOutputTokens,
        reasoning: 'Large context window required (1M+ tokens) with advanced reasoning capabilities and 65K output limit'
      };
    }

    // Strategy 3: Speed-critical tasks with good quality content
    if (priority === 'speed' && contentSize < 50000 && contentQuality > 70) {
      return {
        provider: 'openai',
        model: 'gpt-4o-2024-11-20',
        maxTokens: OPENAI_CONFIG.maxOutputTokens,
        reasoning: 'Optimized for speed with high-quality content - GPT-4o latest model with 16K output tokens'
      };
    }

    // Strategy 4: Medium content with quality focus
    if (contentSize > 50000 && contentSize <= 100000) {
      return {
        provider: 'openrouter',
        model: 'google/gemini-2.5-pro-preview',
        maxTokens: OPENROUTER_CONFIG.maxOutputTokens,
        reasoning: 'Medium-large content benefits from Gemini\'s large context window and superior output capacity'
      };
    }

    // Strategy 5: Cost-conscious but quality-maintained processing
    if (priority === 'cost' && contentSize < 30000) {
      return {
        provider: 'openai',
        model: 'gpt-4o-2024-11-20',
        maxTokens: OPENAI_CONFIG.maxOutputTokens,
        reasoning: 'Cost-effective for smaller content while maintaining high quality standards'
      };
    }

    // Default Strategy: OpenRouter for comprehensive processing
    // Implicit caching, large context window, and superior output limits
    return {
      provider: 'openrouter',
      model: 'google/gemini-2.5-pro-preview',
      maxTokens: OPENROUTER_CONFIG.maxOutputTokens,
      reasoning: 'Default to Gemini 2.5 Pro Preview for optimal quality - large context window, 65K output tokens, and implicit caching'
    };
  }

  /**
   * Get fallback model configuration when primary model fails
   */
  static getFallbackModel(primaryModel: ModelConfig, reason: string): ModelConfig {
    // If primary was OpenRouter, fallback to OpenAI
    if (primaryModel.provider === 'openrouter') {
      return {
        provider: 'openai',
        model: 'gpt-4o-2024-11-20',
        maxTokens: OPENAI_CONFIG.maxOutputTokens,
        reasoning: `Fallback to OpenAI due to: ${reason}`
      };
    }

    // If primary was OpenAI, fallback to OpenRouter
    if (primaryModel.provider === 'openai') {
      return {
        provider: 'openrouter',
        model: 'google/gemini-2.5-pro-preview',
        maxTokens: OPENROUTER_CONFIG.maxOutputTokens,
        reasoning: `Fallback to OpenRouter due to: ${reason}`
      };
    }

    // Default fallback
    return {
      provider: 'openai',
      model: 'gpt-4o-2024-11-20',
      maxTokens: OPENAI_CONFIG.maxOutputTokens,
      reasoning: `Emergency fallback due to: ${reason}`
    };
  }

  /**
   * Validate if a model can handle the given content size
   */
  static canModelHandleContent(model: ModelConfig, contentSize: number): boolean {
    const config = model.provider === 'openai' ? OPENAI_CONFIG : OPENROUTER_CONFIG;
    
    // Reserve 20% of context window for response and formatting
    const usableTokens = Math.floor(config.maxInputTokens * 0.8);
    
    return contentSize <= usableTokens;
  }

  /**
   * Get recommended models for different use cases
   */
  static getRecommendedModels(): {
    speed: ModelConfig;
    quality: ModelConfig;
    cost: ModelConfig;
    largeContent: ModelConfig;
  } {
    return {
      speed: {
        provider: 'openai',
        model: 'gpt-4o-2024-11-20',
        maxTokens: OPENAI_CONFIG.maxOutputTokens,
        reasoning: 'Fastest processing with reliable quality'
      },
      quality: {
        provider: 'openrouter',
        model: 'google/gemini-2.5-pro-preview',
        maxTokens: OPENROUTER_CONFIG.maxOutputTokens,
        reasoning: 'Maximum quality with 65K output tokens and advanced reasoning'
      },
      cost: {
        provider: 'openai',
        model: 'gpt-4o-2024-11-20',
        maxTokens: OPENAI_CONFIG.maxOutputTokens,
        reasoning: 'Cost-effective while maintaining quality standards'
      },
      largeContent: {
        provider: 'openrouter',
        model: 'google/gemini-2.5-pro-preview',
        maxTokens: OPENROUTER_CONFIG.maxOutputTokens,
        reasoning: 'Large context window (1M+ tokens) for comprehensive content processing'
      }
    };
  }

  /**
   * Calculate estimated cost for model usage (approximate)
   */
  static estimateCost(model: ModelConfig, inputTokens: number, outputTokens: number): number {
    // Approximate pricing (these would need to be updated with actual rates)
    const pricing = {
      'gpt-4o-2024-11-20': { input: 0.0025, output: 0.01 }, // per 1K tokens
      'google/gemini-2.5-pro-preview': { input: 0.00125, output: 0.005 } // per 1K tokens
    };

    const modelPricing = pricing[model.model as keyof typeof pricing];
    if (!modelPricing) {
      return 0; // Unknown model
    }

    const inputCost = (inputTokens / 1000) * modelPricing.input;
    const outputCost = (outputTokens / 1000) * modelPricing.output;

    return inputCost + outputCost;
  }

  /**
   * Get model capabilities and limitations
   */
  static getModelCapabilities(model: AIModel): {
    maxInputTokens: number;
    maxOutputTokens: number;
    supportsStructuredOutput: boolean;
    supportsFunctionCalling: boolean;
    supportsMultiModal: boolean;
    implicitCaching: boolean;
  } {
    const capabilities = {
      'gpt-4o-2024-11-20': {
        maxInputTokens: 128000,
        maxOutputTokens: 16384,
        supportsStructuredOutput: true,
        supportsFunctionCalling: true,
        supportsMultiModal: true,
        implicitCaching: false
      },
      'google/gemini-2.5-pro-preview': {
        maxInputTokens: 1048576,
        maxOutputTokens: 65536,
        supportsStructuredOutput: true,
        supportsFunctionCalling: true,
        supportsMultiModal: true,
        implicitCaching: true
      }
    };

    return capabilities[model as keyof typeof capabilities] || capabilities['gpt-4o-2024-11-20'];
  }
}
