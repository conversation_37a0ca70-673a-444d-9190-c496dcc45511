#!/usr/bin/env tsx

/**
 * Test Script: Verify short_description length fix
 * 
 * This script tests that the short_description field can now accept
 * longer content without constraint violations.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testShortDescriptionFix() {
  console.log('🧪 Testing short_description length fix');
  console.log('=' .repeat(60));
  
  let testToolId: string | null = null;
  
  try {
    // Step 1: Test with 150+ character short_description (previously failing)
    console.log('1️⃣ Testing 150+ character short_description...');
    
    const longShortDescription = 'This is a comprehensive AI-powered social media assistant that helps users create engaging content, schedule posts across multiple platforms, analyze performance metrics, and optimize their social media strategy with advanced analytics and insights.';
    
    console.log(`   Length: ${longShortDescription.length} characters`);
    
    const testId = 'test-short-desc-' + Date.now();

    const { data: createResult, error: createError } = await supabase
      .from('tools')
      .insert({
        id: testId,
        name: 'Short Description Length Test Tool',
        slug: 'short-desc-test-' + Date.now(),
        short_description: longShortDescription,
        description: 'Test tool for verifying short_description length fix',
        link: '/tools/short-desc-test-' + Date.now()
      })
      .select('id')
      .single();
    
    if (createError) {
      console.error('❌ Failed to create test tool:', createError.message);
      return false;
    }
    
    testToolId = createResult.id;
    console.log(`✅ Test tool created with long short_description (${longShortDescription.length} chars)`);
    
    // Step 2: Test with 300 character short_description (new limit)
    console.log('2️⃣ Testing 300 character short_description...');
    
    const maxLengthDescription = 'A'.repeat(300);
    
    const { error: updateError } = await supabase
      .from('tools')
      .update({ 
        short_description: maxLengthDescription
      })
      .eq('id', testToolId);
    
    if (updateError) {
      console.error('❌ Failed to update with 300 char description:', updateError.message);
      return false;
    }
    
    console.log('✅ Successfully updated with 300 character short_description');
    
    // Step 3: Test with 301 characters (should be truncated by validation)
    console.log('3️⃣ Testing content generation pipeline validation...');
    
    // Simulate what the content generation pipeline does
    const tooLongDescription = 'A'.repeat(350);
    let processedDescription = tooLongDescription;
    
    // Apply the same validation logic as in the pipeline
    if (processedDescription.length > 300) {
      processedDescription = processedDescription.substring(0, 297) + '...';
      console.log(`⚠️ Truncated description from ${tooLongDescription.length} to 300 characters`);
    }
    
    const { error: validationError } = await supabase
      .from('tools')
      .update({ 
        short_description: processedDescription
      })
      .eq('id', testToolId);
    
    if (validationError) {
      console.error('❌ Failed validation test:', validationError.message);
      return false;
    }
    
    console.log('✅ Validation and truncation working correctly');
    
    // Step 4: Verify the final state
    console.log('4️⃣ Verifying final state...');
    
    const { data: verifyResult, error: verifyError } = await supabase
      .from('tools')
      .select('short_description')
      .eq('id', testToolId)
      .single();
    
    if (verifyError) {
      console.error('❌ Failed to verify final state:', verifyError.message);
      return false;
    }
    
    console.log(`✅ Final short_description length: ${verifyResult.short_description.length} characters`);
    
    if (verifyResult.short_description.length <= 300) {
      console.log('✅ Length constraint satisfied');
    } else {
      console.log('❌ Length constraint violated');
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
    
  } finally {
    // Cleanup: Delete test tool
    if (testToolId) {
      console.log('🧹 Cleaning up test tool...');
      const { error: deleteError } = await supabase
        .from('tools')
        .delete()
        .eq('id', testToolId);
      
      if (deleteError) {
        console.warn('⚠️ Failed to cleanup test tool:', deleteError.message);
      } else {
        console.log('✅ Test tool cleaned up');
      }
    }
  }
}

async function main() {
  const success = await testShortDescriptionFix();
  
  console.log('');
  console.log('=' .repeat(60));
  
  if (success) {
    console.log('🎉 All tests passed!');
    console.log('✅ Short description length fix is working correctly');
    console.log('✅ Content generation pipeline should no longer encounter constraint violations');
    console.log('✅ Field supports up to 300 characters with automatic truncation for longer content');
  } else {
    console.log('❌ Tests failed!');
    console.log('❌ There may still be issues with the short_description length fix');
  }
  
  process.exit(success ? 0 : 1);
}

main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
