#!/usr/bin/env tsx

/**
 * Analyze Job Timing Race Condition
 * 
 * This script investigates the timing issue where content generation jobs
 * run before tools are created, causing "tool not found" errors.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function analyzeJobTimingRaceCondition() {
  console.log('🕐 ANALYZING JOB TIMING RACE CONDITION\n');
  console.log('=' .repeat(70) + '\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    const problemToolId = 'd6ed72da-bbbb-4940-bd1e-df97c3c61ad8';

    console.log(`🎯 Investigating tool ID: ${problemToolId}`);
    console.log('');

    // 1. Check if tool exists now
    console.log('1. 🔍 CURRENT TOOL STATUS:');
    const { data: currentTool, error: currentError } = await supabase
      .from('tools')
      .select('*')
      .eq('id', problemToolId)
      .single();

    if (currentError) {
      console.log(`   ❌ Tool still not found: ${currentError.message}`);
    } else if (currentTool) {
      console.log(`   ✅ Tool exists now: ${currentTool.name}`);
      console.log(`      Website: ${currentTool.website}`);
      console.log(`      Submission Source: ${currentTool.submission_source}`);
      console.log(`      Created: ${currentTool.created_at}`);
      console.log(`      Updated: ${currentTool.updated_at}`);
    }
    console.log('');

    // 2. Analyze job execution timeline
    console.log('2. ⏱️ JOB EXECUTION TIMELINE:');
    const { data: jobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .eq('tool_id', problemToolId)
      .order('created_at', { ascending: true });

    if (jobsError) {
      console.log(`   ❌ Error fetching jobs: ${jobsError.message}`);
    } else if (jobs && jobs.length > 0) {
      console.log(`   ✅ Found ${jobs.length} job(s) for this tool:\n`);
      
      jobs.forEach((job, index) => {
        const createdAt = new Date(job.created_at);
        const updatedAt = new Date(job.updated_at);
        const duration = updatedAt.getTime() - createdAt.getTime();
        
        console.log(`      Job ${index + 1}: ${job.job_type.toUpperCase()}`);
        console.log(`         ID: ${job.id}`);
        console.log(`         Status: ${job.status}`);
        console.log(`         Created: ${job.created_at}`);
        console.log(`         Updated: ${job.updated_at}`);
        console.log(`         Duration: ${duration}ms`);
        
        if (job.error_logs) {
          const errorLogs = typeof job.error_logs === 'string' 
            ? job.error_logs 
            : JSON.stringify(job.error_logs);
          console.log(`         Error: ${errorLogs.substring(0, 100)}...`);
        }
        console.log('');
      });

      // Analyze timing gaps
      if (jobs.length > 1) {
        console.log('   📊 TIMING ANALYSIS:');
        for (let i = 1; i < jobs.length; i++) {
          const prevJob = jobs[i - 1];
          const currentJob = jobs[i];
          const gap = new Date(currentJob.created_at).getTime() - new Date(prevJob.created_at).getTime();
          
          console.log(`      Gap between ${prevJob.job_type} and ${currentJob.job_type}: ${gap}ms`);
        }
        console.log('');
      }
    }

    // 3. Check bulk processing job that should create the tool
    console.log('3. 🔍 BULK PROCESSING JOB ANALYSIS:');
    const { data: bulkJobs, error: bulkError } = await supabase
      .from('bulk_processing_jobs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5);

    if (bulkError) {
      console.log(`   ❌ Error fetching bulk jobs: ${bulkError.message}`);
    } else if (bulkJobs && bulkJobs.length > 0) {
      console.log(`   ✅ Found ${bulkJobs.length} recent bulk job(s):\n`);
      
      bulkJobs.forEach((bulkJob, index) => {
        console.log(`      Bulk Job ${index + 1}: ${bulkJob.id}`);
        console.log(`         Status: ${bulkJob.status}`);
        console.log(`         Total Items: ${bulkJob.total_items}`);
        console.log(`         Processed: ${bulkJob.processed_items}`);
        console.log(`         Created: ${bulkJob.created_at}`);
        console.log(`         Updated: ${bulkJob.updated_at}`);
        console.log('');
      });
    }

    // 4. Identify the race condition
    console.log('4. 🏁 RACE CONDITION ANALYSIS:');
    console.log('');
    console.log('   🎯 IDENTIFIED ISSUE: Job Creation vs Tool Creation Timing');
    console.log('');
    console.log('   📋 Current Bulk Processing Flow:');
    console.log('      1. Bulk job starts');
    console.log('      2. Creates individual jobs (web_scraping, content_generation, tool_processing)');
    console.log('      3. Jobs are queued and start executing');
    console.log('      4. Tool creation happens in parallel/after job creation');
    console.log('      5. content_generation job runs before tool exists → "not found"');
    console.log('      6. tool_processing job runs after tool exists → "found"');
    console.log('');
    console.log('   ❌ PROBLEM: Jobs reference tool_id before tool is created');
    console.log('');

    // 5. Recommendations
    console.log('5. 💡 SOLUTION RECOMMENDATIONS:');
    console.log('');
    console.log('   ✅ OPTION 1: Fix Job Creation Order');
    console.log('      • Create tool FIRST in bulk processing');
    console.log('      • Then create jobs with valid tool_id');
    console.log('      • Ensures tool exists before any job runs');
    console.log('');
    console.log('   ✅ OPTION 2: Add Tool Creation Verification');
    console.log('      • Add tool existence check before job execution');
    console.log('      • Retry mechanism if tool not found');
    console.log('      • Delay job execution until tool exists');
    console.log('');
    console.log('   ✅ OPTION 3: Synchronous Tool Creation');
    console.log('      • Make tool creation synchronous in bulk processing');
    console.log('      • Wait for tool creation before job creation');
    console.log('      • Use database transactions for consistency');
    console.log('');

    console.log('6. 🧪 TESTING STRATEGY:');
    console.log('');
    console.log('   📋 Test Cases:');
    console.log('      1. Monitor job creation timestamps vs tool creation');
    console.log('      2. Verify tool exists before content_generation job runs');
    console.log('      3. Test with multiple concurrent bulk processing jobs');
    console.log('      4. Verify database transaction boundaries');
    console.log('');
    console.log('   📊 Success Criteria:');
    console.log('      • Tool exists before any job references it');
    console.log('      • Consistent submission source detection across all jobs');
    console.log('      • No more "tool not found" errors');
    console.log('      • Single workflow path per tool');

    console.log('\n🎯 CONCLUSION:');
    console.log('');
    console.log('   The issue is NOT with our database query fixes (those are working).');
    console.log('   The issue IS with the job creation timing in bulk processing.');
    console.log('');
    console.log('   ❌ Current: Jobs created → Tool created → Jobs run (race condition)');
    console.log('   ✅ Needed: Tool created → Jobs created → Jobs run (proper order)');

  } catch (error) {
    console.error('💥 Analysis failed:', error);
  }
}

// Run the analysis
analyzeJobTimingRaceCondition().catch(console.error);
