#!/usr/bin/env tsx

/**
 * Test Job Constraint Fixes
 * 
 * This script comprehensively tests both job_type and status constraint fixes
 * to ensure all TypeScript enum values work correctly.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function testJobTypeConstraints() {
  console.log('🎯 Testing Job Type Constraints...');
  console.log('-'.repeat(50));

  try {
    // Import TypeScript enums
    const { JobType } = await import('../src/lib/jobs/types');
    const jobTypes = Object.values(JobType);

    console.log(`Testing ${jobTypes.length} job types from TypeScript enum...`);

    const results = {
      passed: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const jobType of jobTypes) {
      const testJobId = generateUUID();
      
      try {
        console.log(`   Testing job_type: '${jobType}'...`);
        
        const { error } = await supabase
          .from('ai_generation_jobs')
          .insert({
            id: testJobId,
            job_type: jobType,
            status: 'pending',
            progress: 0,
            attempts: 0,
            max_attempts: 3,
            priority: 1
          });

        if (error) {
          console.log(`   ❌ FAILED: ${error.message}`);
          results.failed++;
          results.errors.push(`${jobType}: ${error.message}`);
        } else {
          console.log(`   ✅ PASSED`);
          results.passed++;
          
          // Clean up successful test
          await supabase.from('ai_generation_jobs').delete().eq('id', testJobId);
        }
      } catch (error) {
        console.log(`   ❌ ERROR: ${error}`);
        results.failed++;
        results.errors.push(`${jobType}: ${error}`);
      }
    }

    console.log(`\n📊 Job Type Test Results:`);
    console.log(`   ✅ Passed: ${results.passed}/${jobTypes.length}`);
    console.log(`   ❌ Failed: ${results.failed}/${jobTypes.length}`);
    
    if (results.errors.length > 0) {
      console.log(`\n❌ Errors:`);
      results.errors.forEach(error => console.log(`   • ${error}`));
    }

    return results.failed === 0;

  } catch (error) {
    console.error('❌ Job type constraint test failed:', error);
    return false;
  }
}

async function testStatusConstraints() {
  console.log('\n📊 Testing Status Constraints...');
  console.log('-'.repeat(50));

  try {
    // Import TypeScript enums
    const { JobStatus } = await import('../src/lib/jobs/types');
    const jobStatuses = Object.values(JobStatus);

    console.log(`Testing ${jobStatuses.length} statuses from TypeScript enum...`);

    const results = {
      passed: 0,
      failed: 0,
      errors: [] as string[]
    };

    // Create a base job for status testing
    const baseJobId = generateUUID();
    const { error: createError } = await supabase
      .from('ai_generation_jobs')
      .insert({
        id: baseJobId,
        job_type: 'scrape', // Use known valid type
        status: 'pending',
        progress: 0,
        attempts: 0,
        max_attempts: 3,
        priority: 1
      });

    if (createError) {
      console.log(`❌ Could not create base job for status testing: ${createError.message}`);
      return false;
    }

    for (const status of jobStatuses) {
      try {
        console.log(`   Testing status: '${status}'...`);
        
        const { error } = await supabase
          .from('ai_generation_jobs')
          .update({ status: status })
          .eq('id', baseJobId);

        if (error) {
          console.log(`   ❌ FAILED: ${error.message}`);
          results.failed++;
          results.errors.push(`${status}: ${error.message}`);
        } else {
          console.log(`   ✅ PASSED`);
          results.passed++;
        }
      } catch (error) {
        console.log(`   ❌ ERROR: ${error}`);
        results.failed++;
        results.errors.push(`${status}: ${error}`);
      }
    }

    // Clean up base job
    await supabase.from('ai_generation_jobs').delete().eq('id', baseJobId);

    console.log(`\n📊 Status Test Results:`);
    console.log(`   ✅ Passed: ${results.passed}/${jobStatuses.length}`);
    console.log(`   ❌ Failed: ${results.failed}/${jobStatuses.length}`);
    
    if (results.errors.length > 0) {
      console.log(`\n❌ Errors:`);
      results.errors.forEach(error => console.log(`   • ${error}`));
    }

    return results.failed === 0;

  } catch (error) {
    console.error('❌ Status constraint test failed:', error);
    return false;
  }
}

async function testProblematicCases() {
  console.log('\n🚨 Testing Previously Problematic Cases...');
  console.log('-'.repeat(50));

  const problematicCases = [
    {
      name: 'web_scraping job creation',
      test: async () => {
        const testJobId = generateUUID();
        const { error } = await supabase
          .from('ai_generation_jobs')
          .insert({
            id: testJobId,
            job_type: 'web_scraping',
            status: 'pending',
            progress: 0,
            attempts: 0,
            max_attempts: 3,
            priority: 1
          });

        if (!error) {
          await supabase.from('ai_generation_jobs').delete().eq('id', testJobId);
        }
        
        return { success: !error, error: error?.message };
      }
    },
    {
      name: 'retrying status update',
      test: async () => {
        const testJobId = generateUUID();
        
        // Create job
        const { error: createError } = await supabase
          .from('ai_generation_jobs')
          .insert({
            id: testJobId,
            job_type: 'scrape',
            status: 'pending',
            progress: 0,
            attempts: 0,
            max_attempts: 3,
            priority: 1
          });

        if (createError) {
          return { success: false, error: createError.message };
        }

        // Update to retrying
        const { error: updateError } = await supabase
          .from('ai_generation_jobs')
          .update({ status: 'retrying' })
          .eq('id', testJobId);

        // Clean up
        await supabase.from('ai_generation_jobs').delete().eq('id', testJobId);
        
        return { success: !updateError, error: updateError?.message };
      }
    },
    {
      name: 'Complete workflow simulation',
      test: async () => {
        const testJobId = generateUUID();
        
        try {
          // Create web_scraping job
          const { error: createError } = await supabase
            .from('ai_generation_jobs')
            .insert({
              id: testJobId,
              job_type: 'web_scraping',
              status: 'pending',
              progress: 0,
              attempts: 0,
              max_attempts: 3,
              priority: 1
            });

          if (createError) {
            return { success: false, error: createError.message };
          }

          // Simulate workflow: pending → processing → retrying → completed
          const statusFlow = ['processing', 'retrying', 'completed'];
          
          for (const status of statusFlow) {
            const { error } = await supabase
              .from('ai_generation_jobs')
              .update({ status: status })
              .eq('id', testJobId);

            if (error) {
              await supabase.from('ai_generation_jobs').delete().eq('id', testJobId);
              return { success: false, error: `Failed at ${status}: ${error.message}` };
            }
          }

          // Clean up
          await supabase.from('ai_generation_jobs').delete().eq('id', testJobId);
          
          return { success: true, error: null };
        } catch (error) {
          return { success: false, error: String(error) };
        }
      }
    }
  ];

  const results = {
    passed: 0,
    failed: 0,
    errors: [] as string[]
  };

  for (const testCase of problematicCases) {
    console.log(`   Testing: ${testCase.name}...`);
    
    try {
      const result = await testCase.test();
      
      if (result.success) {
        console.log(`   ✅ PASSED`);
        results.passed++;
      } else {
        console.log(`   ❌ FAILED: ${result.error}`);
        results.failed++;
        results.errors.push(`${testCase.name}: ${result.error}`);
      }
    } catch (error) {
      console.log(`   ❌ ERROR: ${error}`);
      results.failed++;
      results.errors.push(`${testCase.name}: ${error}`);
    }
  }

  console.log(`\n📊 Problematic Cases Test Results:`);
  console.log(`   ✅ Passed: ${results.passed}/${problematicCases.length}`);
  console.log(`   ❌ Failed: ${results.failed}/${problematicCases.length}`);
  
  if (results.errors.length > 0) {
    console.log(`\n❌ Errors:`);
    results.errors.forEach(error => console.log(`   • ${error}`));
  }

  return results.failed === 0;
}

async function main() {
  console.log('🚀 Testing Job Constraint Fixes...');
  console.log('=' .repeat(70));

  const results = {
    jobTypeConstraints: false,
    statusConstraints: false,
    problematicCases: false
  };

  // Run all tests
  results.jobTypeConstraints = await testJobTypeConstraints();
  results.statusConstraints = await testStatusConstraints();
  results.problematicCases = await testProblematicCases();

  // Summary
  console.log('\n📊 Final Test Results:');
  console.log('=' .repeat(70));
  console.log(`Job Type Constraints:          ${results.jobTypeConstraints ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Status Constraints:            ${results.statusConstraints ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Problematic Cases:             ${results.problematicCases ? '✅ PASSED' : '❌ FAILED'}`);

  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 All constraint fixes verified successfully!');
    console.log('   • All TypeScript job types are now supported');
    console.log('   • All TypeScript job statuses are now supported');
    console.log('   • web_scraping job creation works');
    console.log('   • retrying status updates work');
    console.log('   • Complete job workflows functional');
    console.log('');
    console.log('🚀 The enhanced job queue system is now fully operational!');
  } else {
    console.log('\n❌ Some constraint issues remain:');
    if (!results.jobTypeConstraints) {
      console.log('   • Job type constraints need attention');
    }
    if (!results.statusConstraints) {
      console.log('   • Status constraints need attention');
    }
    if (!results.problematicCases) {
      console.log('   • Previously problematic cases still failing');
    }
    console.log('');
    console.log('📋 Required actions:');
    console.log('   1. Execute Migration 008 SQL in Supabase');
    console.log('   2. Verify all constraints were created correctly');
    console.log('   3. Check Supabase logs for any constraint creation errors');
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

export { main as testJobConstraintFixes };
