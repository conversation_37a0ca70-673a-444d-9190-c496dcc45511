# Migration 006: Fix Missing AI Generation Jobs Columns

## Overview

This migration resolves the database schema issue where bulk processing jobs were failing due to missing columns in the `ai_generation_jobs` table. The enhanced job queue system expects several columns that were not included in the original schema migration.

## Problem Statement

**Error**: `Could not find the 'attempts' column of 'ai_generation_jobs' in the schema cache`

**Root Cause**: The enhanced job queue system (`src/lib/jobs/enhanced-queue.ts`) was developed after the initial database schema migration and expects 12 additional columns that don't exist in the current `ai_generation_jobs` table.

## Solution

### Missing Columns Added

| Column | Type | Default | Description |
|--------|------|---------|-------------|
| `attempts` | INTEGER | 0 | Current number of processing attempts |
| `max_attempts` | INTEGER | 3 | Maximum retry attempts allowed |
| `priority` | INTEGER | 1 | Job priority (1-10, higher = more priority) |
| `scheduled_for` | TIMESTAMP | NULL | When job should be processed (for delays/retries) |
| `job_data` | JSONB | NULL | Job-specific data and parameters |
| `tags` | JSONB | `[]` | Array of tags for categorization |
| `result` | JSONB | NULL | Job execution result data |
| `can_pause` | BOOLEAN | true | Whether job supports pausing |
| `can_resume` | BOOLEAN | false | Whether job can be resumed |
| `can_stop` | BOOLEAN | true | Whether job can be manually stopped |
| `estimated_duration` | INTEGER | NULL | Estimated processing time (ms) |
| `actual_duration` | INTEGER | NULL | Actual processing time (ms) |

### Performance Indexes Added

- `idx_ai_generation_jobs_priority` - For priority-based job processing
- `idx_ai_generation_jobs_scheduled_for` - For scheduled job retrieval
- `idx_ai_generation_jobs_attempts` - For retry mechanism
- `idx_ai_generation_jobs_queue_processing` - Composite index for queue processing
- `idx_ai_generation_jobs_control_flags` - For job control monitoring

## Files Created

### 1. Migration Script
- **File**: `src/lib/database/migrations/006_add_missing_ai_generation_jobs_columns.sql`
- **Purpose**: Contains the SQL DDL statements to add missing columns and indexes
- **Features**: 
  - Uses `IF NOT EXISTS` for idempotent execution
  - Includes data type constraints and validation
  - Updates existing records with sensible defaults
  - Adds comprehensive documentation comments

### 2. Migration Execution Script
- **File**: `scripts/run-migration-006.ts`
- **Purpose**: Handles migration execution with verification and testing
- **Command**: `npm run db:migrate:006`
- **Features**:
  - Displays migration SQL for manual execution in Supabase
  - Verifies migration was applied correctly
  - Tests basic job queue functionality
  - Records migration completion

### 3. Test Script
- **File**: `scripts/test-bulk-processing-after-migration.ts`
- **Purpose**: Comprehensive testing of bulk processing after migration
- **Command**: `npm run test:bulk-after-migration`
- **Features**:
  - Verifies table schema accessibility
  - Tests enhanced job queue functionality
  - Creates and monitors test bulk processing jobs
  - Validates AI generation job creation
  - Includes cleanup of test data

## Execution Instructions

### Step 1: Run Migration Script
```bash
npm run db:migrate:006
```

### Step 2: Execute SQL in Supabase
1. Copy the displayed SQL from the migration script output
2. Open Supabase SQL Editor
3. Paste and execute the SQL
4. Verify successful execution (should see NOTICE messages)

### Step 3: Verify Migration
```bash
npm run test:bulk-after-migration
```

### Step 4: Test Bulk Processing
Create a test bulk processing job to ensure the full lifecycle works:
```bash
# Use existing bulk processing endpoints or admin interface
# Verify jobs complete without schema errors
```

## Expected Outcomes

### Before Migration
- ❌ Bulk processing jobs fail with schema cache errors
- ❌ Job retry mechanism non-functional
- ❌ Job priority and scheduling unavailable
- ❌ Limited job control capabilities

### After Migration
- ✅ Bulk processing jobs complete successfully
- ✅ Retry mechanism works with exponential backoff
- ✅ Job priority and scheduling functional
- ✅ Full job control (pause/resume/stop) available
- ✅ Performance optimized with proper indexes
- ✅ Comprehensive job monitoring and tracking

## Backward Compatibility

- **Existing Data**: All existing `ai_generation_jobs` records are updated with sensible defaults
- **Code Compatibility**: Enhanced job queue code now works without modifications
- **API Compatibility**: No breaking changes to existing APIs

## Rollback Procedure

If rollback is needed:

```sql
-- Remove added columns (WARNING: This will lose data)
ALTER TABLE ai_generation_jobs 
DROP COLUMN IF EXISTS attempts,
DROP COLUMN IF EXISTS max_attempts,
DROP COLUMN IF EXISTS priority,
DROP COLUMN IF EXISTS scheduled_for,
DROP COLUMN IF EXISTS job_data,
DROP COLUMN IF EXISTS tags,
DROP COLUMN IF EXISTS result,
DROP COLUMN IF EXISTS can_pause,
DROP COLUMN IF EXISTS can_resume,
DROP COLUMN IF EXISTS can_stop,
DROP COLUMN IF EXISTS estimated_duration,
DROP COLUMN IF EXISTS actual_duration;

-- Remove indexes
DROP INDEX IF EXISTS idx_ai_generation_jobs_priority;
DROP INDEX IF EXISTS idx_ai_generation_jobs_scheduled_for;
DROP INDEX IF EXISTS idx_ai_generation_jobs_attempts;
DROP INDEX IF EXISTS idx_ai_generation_jobs_queue_processing;
DROP INDEX IF EXISTS idx_ai_generation_jobs_control_flags;
```

## Verification Checklist

- [ ] Migration SQL executed successfully in Supabase
- [ ] All 12 new columns exist in `ai_generation_jobs` table
- [ ] Performance indexes created successfully
- [ ] Test job creation works with new columns
- [ ] Bulk processing jobs complete without errors
- [ ] Job retry mechanism functional
- [ ] Job priority and scheduling working
- [ ] No existing functionality broken

## Related Documentation

- [Enhanced AI System Architecture](./enhanced-ai-system/01-system-architecture.md)
- [Background Jobs System](./Background-Jobs-System.md)
- [Database Schema Documentation](./database-schema.md)
- [Bulk Processing System](./bulk-processing-production-readiness-report.md)

## Support

If issues arise after migration:

1. Check Supabase logs for detailed error messages
2. Run the test script to identify specific problems
3. Verify all SQL was executed correctly
4. Check that environment variables are properly configured
5. Ensure no concurrent jobs are running during migration
