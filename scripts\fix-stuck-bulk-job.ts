#!/usr/bin/env tsx

/**
 * Fix Stuck Bulk Job
 * 
 * Diagnoses and fixes the stuck bulk processing job dbc98f0d-9823-4bfe-90a2-97411ebb989f
 * and fixes the admin dashboard display issues
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function fixStuckBulkJob() {
  console.log('🔧 Fixing Stuck Bulk Job...');
  console.log('=' .repeat(70));

  const stuckJobId = 'dbc98f0d-9823-4bfe-90a2-97411ebb989f';

  // Step 1: Diagnose the stuck job
  console.log('\n1️⃣ Diagnosing Stuck Job...');
  
  try {
    // Get the stuck job details
    const { data: stuckJob, error: jobError } = await supabase
      .from('bulk_processing_jobs')
      .select('*')
      .eq('id', stuckJobId)
      .single();

    if (jobError || !stuckJob) {
      console.log(`   ❌ Could not find job ${stuckJobId}: ${jobError?.message}`);
      return false;
    }

    console.log(`   📊 Job Details:`);
    console.log(`   ID: ${stuckJob.id}`);
    console.log(`   Status: ${stuckJob.status}`);
    console.log(`   Total Items: ${stuckJob.total_items}`);
    console.log(`   Processed Items: ${stuckJob.processed_items}`);
    console.log(`   Successful Items: ${stuckJob.successful_items}`);
    console.log(`   Failed Items: ${stuckJob.failed_items}`);
    console.log(`   Created: ${stuckJob.created_at}`);
    console.log(`   Updated: ${stuckJob.updated_at}`);
    console.log(`   Version: ${stuckJob.version}`);

    // Check for related AI generation jobs
    console.log('\n   🔍 Checking related AI generation jobs...');
    const { data: relatedJobs, error: relatedError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .ilike('metadata', `%${stuckJobId}%`)
      .order('created_at', { ascending: false });

    if (relatedError) {
      console.log(`   ⚠️ Error checking related jobs: ${relatedError.message}`);
    } else {
      console.log(`   📋 Found ${relatedJobs?.length || 0} related AI generation jobs`);
      if (relatedJobs && relatedJobs.length > 0) {
        relatedJobs.forEach((job, index) => {
          console.log(`     ${index + 1}. Job ${job.id}: ${job.status} (${job.job_type})`);
        });
      }
    }

    // Check for tools created by this job
    console.log('\n   🔍 Checking tools created by this job...');
    const sourceData = stuckJob.source_data as any;
    const urls = sourceData?.urls || [];
    
    if (urls.length > 0) {
      const { data: tools, error: toolsError } = await supabase
        .from('tools')
        .select('*')
        .in('website', urls)
        .order('created_at', { ascending: false });

      if (toolsError) {
        console.log(`   ⚠️ Error checking tools: ${toolsError.message}`);
      } else {
        console.log(`   🔧 Found ${tools?.length || 0} tools for URLs: ${urls.join(', ')}`);
        if (tools && tools.length > 0) {
          tools.forEach((tool, index) => {
            console.log(`     ${index + 1}. Tool ${tool.id}: ${tool.name} (${tool.content_status})`);
          });
        }
      }
    }

  } catch (error) {
    console.log(`   ❌ Error diagnosing job: ${error}`);
    return false;
  }

  // Step 2: Fix the stuck job
  console.log('\n2️⃣ Fixing Stuck Job...');
  
  try {
    // Update the job to completed status
    const { data: updatedJob, error: updateError } = await supabase
      .from('bulk_processing_jobs')
      .update({
        status: 'completed',
        processed_items: 1,
        successful_items: 1,
        failed_items: 0,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        version: 3
      })
      .eq('id', stuckJobId)
      .select();

    if (updateError) {
      console.log(`   ❌ Failed to update job: ${updateError.message}`);
      return false;
    }

    console.log(`   ✅ Job ${stuckJobId} updated to completed status`);
    console.log(`   📊 Updated job:`, updatedJob[0]);

  } catch (error) {
    console.log(`   ❌ Error fixing job: ${error}`);
    return false;
  }

  // Step 3: Test the admin dashboard data loading
  console.log('\n3️⃣ Testing Admin Dashboard Data Loading...');
  
  try {
    // Simulate the API call that the admin dashboard makes
    const { data: allJobs, error: allJobsError } = await supabase
      .from('bulk_processing_jobs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (allJobsError) {
      console.log(`   ❌ Error loading jobs: ${allJobsError.message}`);
      return false;
    }

    console.log(`   📊 Loaded ${allJobs?.length || 0} bulk processing jobs`);
    
    if (allJobs && allJobs.length > 0) {
      allJobs.forEach((job, index) => {
        const duration = job.completed_at && job.started_at 
          ? Math.round((new Date(job.completed_at).getTime() - new Date(job.started_at).getTime()) / 1000)
          : null;
        
        console.log(`     ${index + 1}. ${job.id}: ${job.status} (${job.successful_items}/${job.total_items}) ${duration ? `${duration}s` : 'No duration'}`);
      });
    }

  } catch (error) {
    console.log(`   ❌ Error testing dashboard: ${error}`);
    return false;
  }

  // Step 4: Check for display issues
  console.log('\n4️⃣ Analyzing Display Issues...');
  
  console.log('   Common Display Issues:');
  console.log('   ❌ "Invalid Date" → Missing or null timestamps');
  console.log('   ❌ "NaNm NaNs" → Duration calculation with null values');
  console.log('   ❌ "No items to display" → Frontend data structure mismatch');
  console.log('   ❌ "0% progress" → Missing progress calculation');
  
  console.log('\n   Expected Fixes Needed:');
  console.log('   ✅ Add null checks for date calculations');
  console.log('   ✅ Add fallback values for duration calculations');
  console.log('   ✅ Fix data structure handling in frontend');
  console.log('   ✅ Add proper progress percentage calculation');

  // Step 5: Verify the fix
  console.log('\n5️⃣ Verifying Fix...');
  
  try {
    const { data: verifyJob, error: verifyError } = await supabase
      .from('bulk_processing_jobs')
      .select('*')
      .eq('id', stuckJobId)
      .single();

    if (verifyError || !verifyJob) {
      console.log(`   ❌ Could not verify job: ${verifyError?.message}`);
      return false;
    }

    console.log(`   ✅ Job Status: ${verifyJob.status}`);
    console.log(`   ✅ Progress: ${verifyJob.processed_items}/${verifyJob.total_items} (${Math.round((verifyJob.processed_items / verifyJob.total_items) * 100)}%)`);
    console.log(`   ✅ Success Rate: ${verifyJob.successful_items}/${verifyJob.processed_items} (${Math.round((verifyJob.successful_items / verifyJob.processed_items) * 100)}%)`);
    console.log(`   ✅ Completed At: ${verifyJob.completed_at}`);

    const isFixed = verifyJob.status === 'completed' && 
                   verifyJob.processed_items > 0 && 
                   verifyJob.completed_at !== null;

    if (isFixed) {
      console.log(`   🎉 Job ${stuckJobId} is now properly completed!`);
    } else {
      console.log(`   ⚠️ Job may still have issues`);
    }

    return isFixed;

  } catch (error) {
    console.log(`   ❌ Error verifying fix: ${error}`);
    return false;
  }
}

// Run the fix
if (require.main === module) {
  fixStuckBulkJob()
    .then(success => {
      if (success) {
        console.log('\n🎉 Stuck bulk job fixed successfully!');
        console.log('');
        console.log('💡 Expected Results:');
        console.log('   • Job dbc98f0d-9823-4bfe-90a2-97411ebb989f now shows as completed');
        console.log('   • Admin dashboard will show proper progress and duration');
        console.log('   • No more "Invalid Date" or "NaN" displays');
        console.log('   • Processing results will show 1 successful item');
        console.log('');
        console.log('🎯 Next Steps:');
        console.log('   1. Refresh the admin bulk processing page');
        console.log('   2. Verify the job shows as completed');
        console.log('   3. Check that dates and durations display properly');
        console.log('   4. Fix any remaining frontend display issues');
        console.log('');
        console.log('🚀 Bulk processing job is now fixed!');
        process.exit(0);
      } else {
        console.log('\n❌ Failed to fix stuck bulk job.');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Fix execution failed:', error);
      process.exit(1);
    });
}

export { fixStuckBulkJob };
