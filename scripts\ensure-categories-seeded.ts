#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import { AI_CATEGORIES } from '../src/lib/constants';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function ensureCategoriesSeeded() {
  console.log('🌱 ENSURING ALL CATEGORIES ARE SEEDED');
  console.log('=' .repeat(60));

  try {
    // Get existing categories
    const { data: existingCategories, error: fetchError } = await supabase
      .from('categories')
      .select('id');

    if (fetchError) {
      console.error('❌ Error fetching existing categories:', fetchError.message);
      return;
    }

    const existingIds = new Set(existingCategories?.map(c => c.id) || []);
    console.log(`📊 Found ${existingIds.size} existing categories in database`);

    // Check which categories need to be created
    const categoriesToCreate = AI_CATEGORIES.filter(cat => !existingIds.has(cat.id));
    
    if (categoriesToCreate.length === 0) {
      console.log('✅ All categories already exist in database');
      return;
    }

    console.log(`🔧 Need to create ${categoriesToCreate.length} categories:`);
    categoriesToCreate.forEach(cat => {
      console.log(`   • ${cat.id} - ${cat.title}`);
    });

    // Create missing categories
    const categoriesToInsert = categoriesToCreate.map(category => ({
      id: category.id,
      title: category.title,
      icon_name: category.iconName,
      description: category.description,
      color_class: category.seeAllButton?.colorClass || 'bg-blue-500 hover:bg-blue-400',
      text_color_class: category.seeAllButton?.textColorClass || 'text-white',
      meta_title: `${category.title} - AI Tools Directory`,
      meta_description: `Discover the best ${category.title.toLowerCase()} for your projects. ${category.description}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    const { data: insertedCategories, error: insertError } = await supabase
      .from('categories')
      .insert(categoriesToInsert)
      .select('id, title');

    if (insertError) {
      console.error('❌ Error creating categories:', insertError.message);
      return;
    }

    console.log(`✅ Successfully created ${insertedCategories?.length || 0} categories:`);
    insertedCategories?.forEach(cat => {
      console.log(`   • ${cat.id} - ${cat.title}`);
    });

    console.log('\n🎉 All categories are now properly seeded!');

  } catch (error) {
    console.error('❌ Failed to ensure categories are seeded:', error);
  }
}

// Run the seeding
if (require.main === module) {
  ensureCategoriesSeeded().catch(console.error);
}

export { ensureCategoriesSeeded };
