#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import { AI_CATEGORIES } from '../src/lib/constants';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugCategoryConstraint() {
  console.log('🔍 DEBUGGING CATEGORY CONSTRAINT VIOLATION');
  console.log('=' .repeat(60));

  try {
    // 1. Check what categories exist in the database
    console.log('\n1️⃣ Checking categories in database...');
    const { data: dbCategories, error: catError } = await supabase
      .from('categories')
      .select('id, title')
      .order('id');

    if (catError) {
      console.log('❌ Error fetching categories:', catError.message);
      return;
    }

    console.log(`✅ Found ${dbCategories?.length || 0} categories in database:`);
    dbCategories?.forEach(cat => {
      console.log(`   • ${cat.id} - ${cat.title}`);
    });

    // 2. Check what categories are defined in constants
    console.log('\n2️⃣ Checking categories in constants...');
    console.log(`✅ Found ${AI_CATEGORIES.length} categories in constants:`);
    AI_CATEGORIES.forEach(cat => {
      console.log(`   • ${cat.id} - ${cat.title}`);
    });

    // 3. Find mismatches
    console.log('\n3️⃣ Finding mismatches...');
    const dbCategoryIds = new Set(dbCategories?.map(c => c.id) || []);
    const constantCategoryIds = new Set(AI_CATEGORIES.map(c => c.id));

    const missingInDb = AI_CATEGORIES.filter(c => !dbCategoryIds.has(c.id));
    const extraInDb = dbCategories?.filter(c => !constantCategoryIds.has(c.id)) || [];

    if (missingInDb.length > 0) {
      console.log('❌ Categories missing in database:');
      missingInDb.forEach(cat => {
        console.log(`   • ${cat.id} - ${cat.title}`);
      });
    }

    if (extraInDb.length > 0) {
      console.log('⚠️ Extra categories in database:');
      extraInDb.forEach(cat => {
        console.log(`   • ${cat.id} - ${cat.title}`);
      });
    }

    if (missingInDb.length === 0 && extraInDb.length === 0) {
      console.log('✅ Categories are in sync!');
    }

    // 4. Check the specific failed tool
    console.log('\n4️⃣ Checking the failed tool...');
    const failedToolId = '3aea56ef-30d3-4933-a05e-fc1274a6ba2a';
    
    const { data: failedTool, error: toolError } = await supabase
      .from('tools')
      .select('id, name, category_id, website')
      .eq('id', failedToolId)
      .single();

    if (toolError) {
      console.log('❌ Error fetching failed tool:', toolError.message);
    } else if (failedTool) {
      console.log('✅ Found failed tool:');
      console.log(`   • ID: ${failedTool.id}`);
      console.log(`   • Name: ${failedTool.name}`);
      console.log(`   • Category ID: ${failedTool.category_id}`);
      console.log(`   • Website: ${failedTool.website}`);

      // Check if the category_id exists
      if (failedTool.category_id) {
        const categoryExists = dbCategoryIds.has(failedTool.category_id);
        console.log(`   • Category exists in DB: ${categoryExists ? '✅ YES' : '❌ NO'}`);
        
        if (!categoryExists) {
          console.log(`   🚨 PROBLEM FOUND: Tool has category_id "${failedTool.category_id}" which doesn't exist!`);
        }
      } else {
        console.log('   • Category ID: NULL (this is allowed)');
      }
    } else {
      console.log('❌ Failed tool not found');
    }

    // 5. Check recent AI generation jobs for this tool
    console.log('\n5️⃣ Checking recent AI generation jobs...');
    const { data: jobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select('id, tool_id, status, ai_responses, created_at')
      .eq('tool_id', failedToolId)
      .order('created_at', { ascending: false })
      .limit(3);

    if (jobsError) {
      console.log('❌ Error fetching AI jobs:', jobsError.message);
    } else if (jobs && jobs.length > 0) {
      console.log(`✅ Found ${jobs.length} recent AI generation job(s):`);
      jobs.forEach((job, index) => {
        console.log(`   Job ${index + 1}:`);
        console.log(`     • ID: ${job.id}`);
        console.log(`     • Status: ${job.status}`);
        console.log(`     • Created: ${job.created_at}`);
        
        // Check if AI response contains category_id
        if (job.ai_responses && typeof job.ai_responses === 'object') {
          const responses = job.ai_responses as any;
          if (responses.content && responses.content.category_id) {
            console.log(`     • AI Generated Category ID: ${responses.content.category_id}`);
            const aiCategoryExists = dbCategoryIds.has(responses.content.category_id);
            console.log(`     • AI Category exists in DB: ${aiCategoryExists ? '✅ YES' : '❌ NO'}`);
          }
        }
      });
    } else {
      console.log('❌ No AI generation jobs found for this tool');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
if (require.main === module) {
  debugCategoryConstraint().catch(console.error);
}

export { debugCategoryConstraint };
