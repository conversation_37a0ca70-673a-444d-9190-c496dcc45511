#!/usr/bin/env tsx

/**
 * Test Complete Submission Source Fix
 * 
 * This script tests the complete fix for submission source detection
 * including the race condition prevention and verification steps.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function testCompleteSubmissionSourceFix() {
  console.log('🧪 TESTING COMPLETE SUBMISSION SOURCE FIX\n');
  console.log('=' .repeat(60) + '\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. Check current state of FoodiePrep tools
    console.log('1. 📋 CHECKING CURRENT STATE\n');
    
    const { data: tools, error: toolsError } = await supabase
      .from('tools')
      .select('*')
      .or('name.ilike.%foodieprep%,website.ilike.%foodieprep%')
      .order('created_at', { ascending: false });

    if (toolsError) {
      throw new Error(`Failed to fetch tools: ${toolsError.message}`);
    }

    if (!tools || tools.length === 0) {
      console.log('❌ No FoodiePrep tools found');
      console.log('   Please process www.foodieprep.ai through bulk processing first');
      return;
    }

    console.log(`✅ Found ${tools.length} FoodiePrep tool(s):\n`);

    tools.forEach((tool, index) => {
      console.log(`   Tool ${index + 1}: ${tool.name} (${tool.id})`);
      console.log(`      🌐 Website: ${tool.website}`);
      console.log(`      📊 Content Status: ${tool.content_status}`);
      console.log(`      🤖 AI Status: ${tool.ai_generation_status}`);
      console.log(`      📝 Submission Type: ${tool.submission_type}`);
      console.log(`      📋 Submission Source: ${tool.submission_source}`);
      console.log(`      📅 Created: ${tool.created_at}`);
      console.log(`      📅 Updated: ${tool.updated_at}`);
      
      // Check if it's correctly configured
      const isCorrect = 
        tool.submission_source === 'bulk_processing' &&
        tool.submission_type === 'admin';
        
      console.log(`      ${isCorrect ? '✅' : '❌'} Configuration: ${isCorrect ? 'CORRECT' : 'NEEDS FIX'}`);
      console.log('');
    });

    // 2. Check recent AI generation jobs
    console.log('2. 🔍 CHECKING RECENT AI GENERATION JOBS\n');
    
    const { data: jobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (jobsError) {
      console.log(`⚠️ Could not fetch jobs: ${jobsError.message}`);
    } else if (jobs && jobs.length > 0) {
      console.log(`✅ Found ${jobs.length} recent job(s):\n`);
      
      jobs.forEach((job, index) => {
        console.log(`   Job ${index + 1}: ${job.id}`);
        console.log(`      📋 Type: ${job.job_type}`);
        console.log(`      📊 Status: ${job.status}`);
        console.log(`      🔗 Tool ID: ${job.tool_id}`);
        console.log(`      📅 Created: ${job.created_at}`);
        
        // Check if this job is for a FoodiePrep tool
        const relatedTool = tools.find(t => t.id === job.tool_id);
        if (relatedTool) {
          console.log(`      🎯 Related to: ${relatedTool.name}`);
        }
        console.log('');
      });
    }

    // 3. Test the pipeline detection logic
    console.log('3. 🧪 TESTING PIPELINE DETECTION LOGIC\n');
    
    for (const tool of tools) {
      console.log(`   Testing tool: ${tool.name} (${tool.id})`);
      
      // Simulate the pipeline detection
      const { data: testTool, error: testError } = await supabase
        .from('tools')
        .select('submission_source, submission_type, name, website')
        .eq('id', tool.id)
        .single();

      if (testError) {
        console.log(`      ❌ Test failed: ${testError.message}`);
      } else {
        console.log(`      📋 Tool details: ${testTool.name} (${testTool.website})`);
        console.log(`      📝 Submission type: ${testTool.submission_type}`);
        console.log(`      📋 Submission source: ${testTool.submission_source}`);

        const isBulkProcessing = testTool.submission_source === 'bulk_processing';
        console.log(`      🎯 Pipeline detection result: ${isBulkProcessing ? 'BULK PROCESSING ✅' : 'USER SUBMISSION ❌'}`);
        
        if (isBulkProcessing) {
          console.log(`      ✅ Should bypass manual review and publish directly`);
        } else {
          console.log(`      ❌ Will go to manual review (not desired for bulk processing)`);
        }
      }
      console.log('');
    }

    // 4. Summary and recommendations
    console.log('4. 📊 SUMMARY AND RECOMMENDATIONS\n');
    console.log('=' .repeat(60));

    const correctlyConfigured = tools.filter(t => 
      t.submission_source === 'bulk_processing' && 
      t.submission_type === 'admin'
    );

    const published = tools.filter(t => t.content_status === 'published');
    const completed = tools.filter(t => t.ai_generation_status === 'completed');

    console.log(`\n📊 Statistics:`);
    console.log(`   Total tools: ${tools.length}`);
    console.log(`   Correctly configured: ${correctlyConfigured.length}/${tools.length}`);
    console.log(`   Published: ${published.length}/${tools.length}`);
    console.log(`   AI completed: ${completed.length}/${tools.length}`);

    if (correctlyConfigured.length === tools.length) {
      console.log('\n✅ ALL TOOLS CORRECTLY CONFIGURED!');
    } else {
      console.log('\n❌ Some tools need configuration fixes');
    }

    if (published.length === tools.length && completed.length === tools.length) {
      console.log('✅ ALL TOOLS SUCCESSFULLY PROCESSED!');
    } else {
      console.log('⚠️ Some tools may still be processing or need retry');
    }

    console.log('\n🔧 FIXES APPLIED:');
    console.log('   ✅ Enhanced pipeline logging for submission source detection');
    console.log('   ✅ Added 100ms delay to prevent database race conditions');
    console.log('   ✅ Added verification step before content generation');
    console.log('   ✅ Auto-fix submission_source if incorrect');

    console.log('\n🚀 EXPECTED BEHAVIOR:');
    console.log('   1. Tools created with submission_source = "bulk_processing"');
    console.log('   2. Small delay ensures database commit before content generation');
    console.log('   3. Verification step double-checks and fixes submission_source');
    console.log('   4. Pipeline detects bulk processing and bypasses manual review');
    console.log('   5. Tools are published directly without manual intervention');

    console.log('\n🧪 TO TEST:');
    console.log('   1. Process www.foodieprep.ai through bulk processing again');
    console.log('   2. Monitor logs for enhanced debugging messages');
    console.log('   3. Look for "🎯 Bulk processing detection result: YES"');
    console.log('   4. Verify tool is published directly');

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
testCompleteSubmissionSourceFix().catch(console.error);
