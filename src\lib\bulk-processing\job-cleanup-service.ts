/**
 * Comprehensive Job Cleanup Service
 * Handles orphaned jobs, lifecycle management, and automated cleanup
 */

import { createClient } from '@supabase/supabase-js';
import { JobStatus } from '@/lib/types';
import { getJobMemoryManager } from '@/lib/jobs/memory-manager';
import { getSynchronizationManager } from './sync-manager';
import { getTransactionManager } from './transaction-manager';

export interface OrphanedJob {
  id: string;
  status: JobStatus;
  created_at: string;
  updated_at: string;
  job_type: string;
  age_hours: number;
  reason: 'stale' | 'stuck' | 'abandoned' | 'corrupted';
}

export interface CleanupStats {
  totalJobsScanned: number;
  orphanedJobsFound: number;
  jobsCleaned: number;
  resourcesFreed: number;
  memoryFreedMB: number;
  errors: string[];
}

export interface CleanupOptions {
  maxAgeHours?: number;
  includeStuckJobs?: boolean;
  includeAbandonedJobs?: boolean;
  dryRun?: boolean;
  batchSize?: number;
}

/**
 * Job Cleanup Service
 * Provides comprehensive cleanup functionality for the bulk processing system
 */
export class JobCleanupService {
  private supabase;
  private memoryManager;
  private syncManager;
  private transactionManager;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    this.memoryManager = getJobMemoryManager();
    this.syncManager = getSynchronizationManager();
    this.transactionManager = getTransactionManager();
  }

  /**
   * Start automated cleanup monitoring
   */
  startAutomatedCleanup(intervalMinutes: number = 30): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(async () => {
      try {
        await this.performAutomatedCleanup();
      } catch (error) {
        console.error('Automated cleanup failed:', error);
      }
    }, intervalMinutes * 60 * 1000);

    console.log(`🤖 Automated cleanup started (every ${intervalMinutes} minutes)`);
  }

  /**
   * Start cleanup monitoring and reporting
   */
  startCleanupMonitoring(intervalMinutes: number = 15): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(async () => {
      try {
        await this.reportCleanupStatus();
      } catch (error) {
        console.error('Cleanup monitoring failed:', error);
      }
    }, intervalMinutes * 60 * 1000);

    console.log(`📊 Cleanup monitoring started (every ${intervalMinutes} minutes)`);
  }

  /**
   * Detect orphaned jobs in the database
   */
  async detectOrphanedJobs(options: CleanupOptions = {}): Promise<OrphanedJob[]> {
    const {
      maxAgeHours = 24,
      includeStuckJobs = true,
      includeAbandonedJobs = true
    } = options;

    console.log('🔍 Scanning for orphaned jobs...');

    try {
      const { data: jobs, error } = await this.supabase
        .from('bulk_processing_jobs')
        .select('id, status, created_at, updated_at, job_type')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch jobs: ${error.message}`);
      }

      const now = new Date();
      const orphanedJobs: OrphanedJob[] = [];

      for (const job of jobs) {
        const createdAt = new Date(job.created_at);
        const updatedAt = new Date(job.updated_at);
        const ageHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
        const timeSinceUpdate = (now.getTime() - updatedAt.getTime()) / (1000 * 60 * 60);

        let reason: OrphanedJob['reason'] | null = null;

        // Check for stale jobs (completed/failed but old)
        if (ageHours > maxAgeHours && ['completed', 'failed', 'cancelled'].includes(job.status)) {
          reason = 'stale';
        }
        // Check for stuck jobs (processing for too long)
        else if (includeStuckJobs && job.status === 'processing' && timeSinceUpdate > 2) {
          reason = 'stuck';
        }
        // Check for abandoned jobs (pending for too long)
        else if (includeAbandonedJobs && job.status === 'pending' && ageHours > 1) {
          reason = 'abandoned';
        }
        // Check for corrupted jobs (invalid status or data)
        else if (!['pending', 'processing', 'completed', 'failed', 'cancelled', 'paused'].includes(job.status)) {
          reason = 'corrupted';
        }

        if (reason) {
          orphanedJobs.push({
            id: job.id,
            status: job.status as JobStatus,
            created_at: job.created_at,
            updated_at: job.updated_at,
            job_type: job.job_type,
            age_hours: ageHours,
            reason
          });
        }
      }

      console.log(`📊 Found ${orphanedJobs.length} orphaned jobs out of ${jobs.length} total jobs`);
      return orphanedJobs;

    } catch (error) {
      console.error('Failed to detect orphaned jobs:', error);
      throw error;
    }
  }

  /**
   * Clean up orphaned jobs
   */
  async cleanupOrphanedJobs(
    orphanedJobs: OrphanedJob[],
    options: CleanupOptions = {}
  ): Promise<CleanupStats> {
    const { dryRun = false, batchSize = 10 } = options;
    
    const stats: CleanupStats = {
      totalJobsScanned: orphanedJobs.length,
      orphanedJobsFound: orphanedJobs.length,
      jobsCleaned: 0,
      resourcesFreed: 0,
      memoryFreedMB: 0,
      errors: []
    };

    if (dryRun) {
      console.log(`🧪 DRY RUN: Would clean up ${orphanedJobs.length} orphaned jobs`);
      return stats;
    }

    console.log(`🧹 Cleaning up ${orphanedJobs.length} orphaned jobs...`);

    // Process jobs in batches to avoid overwhelming the system
    for (let i = 0; i < orphanedJobs.length; i += batchSize) {
      const batch = orphanedJobs.slice(i, i + batchSize);
      
      await Promise.allSettled(
        batch.map(async (job) => {
          try {
            await this.cleanupSingleJob(job);
            stats.jobsCleaned++;
            console.log(`✅ Cleaned up ${job.reason} job: ${job.id}`);
          } catch (error) {
            const errorMsg = `Failed to cleanup job ${job.id}: ${(error as Error).message}`;
            stats.errors.push(errorMsg);
            console.error(`❌ ${errorMsg}`);
          }
        })
      );

      // Small delay between batches to prevent overwhelming the system
      if (i + batchSize < orphanedJobs.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`✅ Cleanup completed: ${stats.jobsCleaned}/${stats.orphanedJobsFound} jobs cleaned`);
    return stats;
  }

  /**
   * Clean up a single job completely
   */
  private async cleanupSingleJob(job: OrphanedJob): Promise<void> {
    try {
      // 1. Cleanup memory resources
      await this.memoryManager.cleanupJobResources(job.id);

      // 2. Release any locks
      if (this.syncManager.isLocked(job.id)) {
        // Force release expired locks
        this.syncManager.cleanupExpiredLocks();
      }

      // 3. Cleanup any active transactions
      this.transactionManager.cleanup();

      // 4. Update job status to cancelled if it's still active
      if (['pending', 'processing', 'paused'].includes(job.status)) {
        await this.supabase
          .from('bulk_processing_jobs')
          .update({ 
            status: 'cancelled',
            updated_at: new Date().toISOString()
          })
          .eq('id', job.id);
      }

      // 5. For very old jobs, consider archiving or deleting
      if (job.age_hours > 168) { // 1 week old
        await this.archiveJob(job.id);
      }

    } catch (error) {
      console.error(`Failed to cleanup job ${job.id}:`, error);
      throw error;
    }
  }

  /**
   * Archive old job data
   */
  private async archiveJob(jobId: string): Promise<void> {
    try {
      // In a production system, you might move data to an archive table
      // For now, we'll just add an archived flag or delete very old data
      
      const { error } = await this.supabase
        .from('bulk_processing_jobs')
        .update({ 
          status: 'archived',
          updated_at: new Date().toISOString()
        })
        .eq('id', jobId);

      if (error) {
        throw new Error(`Failed to archive job: ${error.message}`);
      }

      console.log(`📦 Archived job: ${jobId}`);
    } catch (error) {
      console.error(`Failed to archive job ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * Perform automated cleanup
   */
  private async performAutomatedCleanup(): Promise<void> {
    console.log('🤖 Starting automated cleanup...');

    try {
      const orphanedJobs = await this.detectOrphanedJobs({
        maxAgeHours: 24,
        includeStuckJobs: true,
        includeAbandonedJobs: true
      });

      if (orphanedJobs.length > 0) {
        const stats = await this.cleanupOrphanedJobs(orphanedJobs, {
          batchSize: 5 // Smaller batches for automated cleanup
        });

        console.log(`🤖 Automated cleanup completed: ${stats.jobsCleaned} jobs cleaned`);
      } else {
        console.log('🤖 No orphaned jobs found during automated cleanup');
      }

    } catch (error) {
      console.error('Automated cleanup failed:', error);
    }
  }

  /**
   * Report cleanup status and metrics
   */
  private async reportCleanupStatus(): Promise<void> {
    try {
      const orphanedJobs = await this.detectOrphanedJobs({ dryRun: true });
      const memoryStats = this.memoryManager.getMemoryStats();

      console.log('📊 Cleanup Status Report:');
      console.log(`   Orphaned jobs detected: ${orphanedJobs.length}`);
      console.log(`   Active resources: ${memoryStats.activeResources}`);
      console.log(`   Active jobs: ${memoryStats.activeJobs}`);
      console.log(`   Memory usage: ${memoryStats.heapUsed}MB / ${memoryStats.heapTotal}MB`);

      // Group orphaned jobs by reason
      const byReason = orphanedJobs.reduce((acc, job) => {
        acc[job.reason] = (acc[job.reason] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      if (Object.keys(byReason).length > 0) {
        console.log('   Breakdown by reason:');
        Object.entries(byReason).forEach(([reason, count]) => {
          console.log(`     ${reason}: ${count}`);
        });
      }

    } catch (error) {
      console.error('Failed to report cleanup status:', error);
    }
  }

  /**
   * Manual cleanup with full control
   */
  async performManualCleanup(options: CleanupOptions = {}): Promise<CleanupStats> {
    console.log('🔧 Starting manual cleanup...');

    const orphanedJobs = await this.detectOrphanedJobs(options);
    const stats = await this.cleanupOrphanedJobs(orphanedJobs, options);

    console.log('🔧 Manual cleanup completed');
    return stats;
  }

  /**
   * Stop all cleanup processes
   */
  shutdown(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('🛑 Job cleanup service shutdown');
  }
}

// Singleton instance
let cleanupServiceInstance: JobCleanupService | null = null;

export function getJobCleanupService(): JobCleanupService {
  if (!cleanupServiceInstance) {
    cleanupServiceInstance = new JobCleanupService();
  }
  return cleanupServiceInstance;
}
