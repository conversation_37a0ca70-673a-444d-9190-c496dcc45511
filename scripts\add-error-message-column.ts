#!/usr/bin/env tsx

/**
 * Add error_message column to tools table
 * 
 * This script fixes the immediate database schema issue causing media collection failures
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function addErrorMessageColumn() {
  console.log('🔧 Adding error_message column to tools table');
  console.log('=' .repeat(60));
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  try {
    // Step 1: Check if column already exists
    console.log('1️⃣ Checking if error_message column exists...');
    
    const { data: columns, error: columnError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'tools')
      .eq('column_name', 'error_message');
    
    if (columnError) {
      console.log('⚠️ Could not check existing columns, proceeding with migration...');
    } else if (columns && columns.length > 0) {
      console.log('✅ error_message column already exists');
      return true;
    }
    
    // Step 2: Add the column using raw SQL
    console.log('2️⃣ Adding error_message column...');
    
    // Use the SQL editor approach - we'll provide the SQL for manual execution
    const migrationSQL = `
-- Add error_message column to tools table
ALTER TABLE tools ADD COLUMN IF NOT EXISTS error_message TEXT;

-- Add processing_notes column if it doesn't exist
ALTER TABLE tools ADD COLUMN IF NOT EXISTS processing_notes TEXT;

-- Create index for error tracking
CREATE INDEX IF NOT EXISTS idx_tools_error_message ON tools(error_message) WHERE error_message IS NOT NULL;

-- Update any existing tools with failed status but no error message
UPDATE tools 
SET error_message = 'Legacy error - details not available'
WHERE ai_generation_status = 'failed' 
  AND (error_message IS NULL OR error_message = '');
`;

    console.log('📋 SQL Migration to run in Supabase SQL Editor:');
    console.log('=' .repeat(40));
    console.log(migrationSQL);
    console.log('=' .repeat(40));
    
    // Step 3: Try to add the column directly
    console.log('3️⃣ Attempting to add column via API...');
    
    try {
      // Try using a simple approach - insert a test record to see if column exists
      const testId = 'test-' + Date.now();
      const { error: testError } = await supabase
        .from('tools')
        .insert({
          id: testId,
          name: 'Test Tool',
          website: 'https://test.com',
          error_message: 'Test error message'
        });
      
      if (testError) {
        if (testError.message.includes('error_message')) {
          console.log('❌ error_message column does not exist');
          console.log('');
          console.log('🔧 MANUAL ACTION REQUIRED:');
          console.log('1. Go to Supabase Dashboard → SQL Editor');
          console.log('2. Run the SQL migration shown above');
          console.log('3. Re-run this script to verify');
          return false;
        } else {
          console.log('⚠️ Test insert failed for other reason:', testError.message);
        }
      } else {
        console.log('✅ error_message column exists and is working');
        
        // Clean up test record
        await supabase
          .from('tools')
          .delete()
          .eq('id', testId);
        
        return true;
      }
    } catch (error) {
      console.log('⚠️ API test failed:', error);
    }
    
    // Step 4: Verify the fix worked
    console.log('4️⃣ Testing error_message column functionality...');
    
    // Find an existing tool to test with
    const { data: existingTool, error: toolError } = await supabase
      .from('tools')
      .select('id, name')
      .limit(1)
      .single();
    
    if (toolError || !existingTool) {
      console.log('⚠️ No existing tools found for testing');
      return false;
    }
    
    // Try to update with error_message
    const { error: updateError } = await supabase
      .from('tools')
      .update({ 
        error_message: 'Test error - ' + new Date().toISOString()
      })
      .eq('id', existingTool.id);
    
    if (updateError) {
      console.error('❌ Failed to update error_message:', updateError.message);
      
      if (updateError.message.includes('error_message')) {
        console.log('');
        console.log('🔧 SOLUTION: Run this SQL in Supabase SQL Editor:');
        console.log('ALTER TABLE tools ADD COLUMN error_message TEXT;');
      }
      
      return false;
    } else {
      console.log('✅ Successfully updated error_message column');
      
      // Clear the test error message
      await supabase
        .from('tools')
        .update({ error_message: null })
        .eq('id', existingTool.id);
      
      return true;
    }
    
  } catch (error) {
    console.error('❌ Failed to add error_message column:', error);
    return false;
  }
}

async function main() {
  const success = await addErrorMessageColumn();
  
  console.log('\n' + '=' .repeat(60));
  
  if (success) {
    console.log('🎉 error_message column is ready!');
    console.log('✅ Media collection should now work without schema errors');
  } else {
    console.log('❌ error_message column needs manual setup');
    console.log('');
    console.log('📋 Manual steps:');
    console.log('1. Go to Supabase Dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Run: ALTER TABLE tools ADD COLUMN error_message TEXT;');
    console.log('4. Run: ALTER TABLE tools ADD COLUMN processing_notes TEXT;');
    console.log('5. Re-run this script to verify');
  }
  
  process.exit(success ? 0 : 1);
}

main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
