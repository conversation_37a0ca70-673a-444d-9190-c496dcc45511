#!/usr/bin/env tsx

/**
 * Investigate Job Constraints
 * 
 * This script investigates the current database constraints for ai_generation_jobs
 * to identify what values are allowed and what's missing for job_type and status.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function investigateConstraints() {
  console.log('🔍 Investigating AI Generation Jobs Constraints...');
  console.log('=' .repeat(70));

  try {
    // Step 1: Test problematic values directly
    console.log('1️⃣ Testing problematic values directly...');

    const problematicTests = [
      { type: 'job_type', value: 'web_scraping', description: 'Web scraping job type' },
      { type: 'status', value: 'retrying', description: 'Retrying status' }
    ];

    let constraints: any[] = [];
    let constraintError: any = null;

    for (const test of problematicTests) {
      console.log(`   Testing ${test.description} (${test.type} = '${test.value}')...`);

      const testId = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      try {
        if (test.type === 'job_type') {
          const { error } = await supabase
            .from('ai_generation_jobs')
            .insert({
              id: testId,
              job_type: test.value,
              status: 'pending',
              progress: 0,
              attempts: 0,
              max_attempts: 3,
              priority: 1
            });

          if (error) {
            console.log(`      ❌ FAILED: ${error.message}`);
            if (error.message.includes('violates check constraint')) {
              console.log(`      🔍 This confirms '${test.value}' is not allowed in job_type constraint`);
            }
          } else {
            console.log(`      ✅ SUCCESS: ${test.value} is allowed`);
            // Clean up
            await supabase.from('ai_generation_jobs').delete().eq('id', testId);
          }
        } else if (test.type === 'status') {
          // First create a valid job
          const { error: createError } = await supabase
            .from('ai_generation_jobs')
            .insert({
              id: testId,
              job_type: 'scrape', // Use known valid type
              status: 'pending',
              progress: 0,
              attempts: 0,
              max_attempts: 3,
              priority: 1
            });

          if (createError) {
            console.log(`      ❌ Could not create test job: ${createError.message}`);
            continue;
          }

          // Try to update status
          const { error: updateError } = await supabase
            .from('ai_generation_jobs')
            .update({ status: test.value })
            .eq('id', testId);

          if (updateError) {
            console.log(`      ❌ FAILED: ${updateError.message}`);
            if (updateError.message.includes('violates check constraint')) {
              console.log(`      🔍 This confirms '${test.value}' is not allowed in status constraint`);
            }
          } else {
            console.log(`      ✅ SUCCESS: ${test.value} is allowed`);
          }

          // Clean up
          await supabase.from('ai_generation_jobs').delete().eq('id', testId);
        }
      } catch (error) {
        console.log(`      ❌ ERROR: ${error}`);
      }
    }

    // Step 2: Check TypeScript enum values
    console.log('\n2️⃣ Checking TypeScript enum values...');
    
    // Import the enums
    const { JobType, JobStatus } = await import('../src/lib/jobs/types');
    
    const tsJobTypes = Object.values(JobType);
    const tsJobStatuses = Object.values(JobStatus);
    
    console.log('   📋 TypeScript JobType enum values:');
    console.log(`      ${tsJobTypes.join(', ')}`);
    
    console.log('   📋 TypeScript JobStatus enum values:');
    console.log(`      ${tsJobStatuses.join(', ')}`);

    // Step 3: Summary and recommendations
    console.log('\n3️⃣ Summary and recommendations...');

    console.log('\n📊 Investigation Summary:');
    console.log('=' .repeat(70));
    console.log('✅ TypeScript enum values:');
    console.log(`   Job Types: ${tsJobTypes.join(', ')}`);
    console.log(`   Statuses: ${tsJobStatuses.join(', ')}`);
    console.log('');
    console.log('🔍 Key findings:');
    console.log('   • web_scraping and retrying are defined in TypeScript enums');
    console.log('   • Database constraints need to be updated to include these values');
    console.log('   • Migration 008 will resolve both constraint violations');
    console.log('');
    console.log('📋 Next steps:');
    console.log('   1. Run: npm run db:migrate:008');
    console.log('   2. Execute the migration SQL in Supabase');
    console.log('   3. Run: npm run test:job-constraints');

    return {
      tsJobTypes,
      tsJobStatuses,
      needsUpdate: true // Always true since we know these values are missing
    };

  } catch (error) {
    console.error('❌ Investigation failed:', error);
    return false;
  }
}

// Run the investigation
if (require.main === module) {
  investigateConstraints().catch(console.error);
}

export { investigateConstraints };
