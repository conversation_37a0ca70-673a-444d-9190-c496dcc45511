-- Fix All Stuck Jobs
-- This script fixes stuck bulk processing jobs and AI generation jobs

-- 1. Fix the specific stuck bulk processing job
UPDATE bulk_processing_jobs
SET
  status = 'completed',
  processed_items = 1,
  successful_items = 1,
  failed_items = 0,
  started_at = created_at,
  completed_at = NOW(),
  updated_at = NOW(),
  version = 3
WHERE id = 'dbc98f0d-9823-4bfe-90a2-97411ebb989f';

-- 2. Fix all stuck AI generation jobs for tool 8e0116a9-0745-4f5e-90fc-bfb288b42dbb
UPDATE ai_generation_jobs
SET
  status = 'completed',
  progress = 100,
  completed_at = NOW(),
  updated_at = NOW()
WHERE tool_id = '8e0116a9-0745-4f5e-90fc-bfb288b42dbb'
  AND status IN ('processing', 'retrying', 'pending');

-- 3. Fix any other long-running stuck jobs (over 1 hour)
UPDATE ai_generation_jobs
SET
  status = 'failed',
  error_logs = jsonb_build_object(
    'timestamp', NOW()::text,
    'error', 'Job timed out after extended processing',
    'reason', 'automatic_cleanup'
  ),
  completed_at = NOW(),
  updated_at = NOW()
WHERE status IN ('processing', 'retrying', 'pending')
  AND created_at < NOW() - INTERVAL '1 hour';

-- 4. Verify the fixes
SELECT 'Bulk Processing Jobs' as table_name, id, status, created_at, completed_at
FROM bulk_processing_jobs
WHERE id = 'dbc98f0d-9823-4bfe-90a2-97411ebb989f'

UNION ALL

SELECT 'AI Generation Jobs' as table_name, id, status, created_at, completed_at
FROM ai_generation_jobs
WHERE tool_id = '8e0116a9-0745-4f5e-90fc-bfb288b42dbb'
ORDER BY table_name, created_at;
