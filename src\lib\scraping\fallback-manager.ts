/**
 * Enhanced Fallback Manager
 * 
 * Manages intelligent fallback strategies for web scraping operations,
 * including timeout-based fallbacks and cost optimization integration.
 */

import { ScrapeResult, ScrapeOptions, EnhancedScrapeRequest, EnhancedScrapeResult } from './types';
import { SCRAPING_CONFIG } from '../config/scraping-config';
import { scrapeDoClient } from './scrape-do-client';

export interface FallbackContext {
  originalRequest: EnhancedScrapeRequest;
  basicScrapingResult?: ScrapeResult;
  enhancedScrapingError?: Error;
  timeoutOccurred?: boolean;
  costOptimizationDecision?: 'basic' | 'enhanced' | 'skip';
  attemptCount: number;
  startTime: number;
}

export interface FallbackResult {
  success: boolean;
  result?: EnhancedScrapeResult;
  strategy: 'enhanced' | 'basic' | 'preserved' | 'failed';
  reasoning: string;
  costSavings?: number;
}

export class FallbackManager {
  private config = SCRAPING_CONFIG.errorHandling.fallbackStrategies;

  /**
   * Execute scraping with intelligent fallback strategy
   */
  async executeWithFallback(
    request: EnhancedScrapeRequest,
    basicResult?: ScrapeResult
  ): Promise<FallbackResult> {
    const context: FallbackContext = {
      originalRequest: request,
      basicScrapingResult: basicResult,
      attemptCount: 0,
      startTime: Date.now()
    };

    console.log(`🔄 Starting fallback-enabled scraping for: ${request.url}`);

    // Step 1: Check if we should skip enhanced scraping based on cost optimization
    if (this.shouldSkipEnhancedScraping(context)) {
      return this.useBasicContentFallback(context);
    }

    // Step 2: Attempt enhanced scraping with timeout protection
    try {
      const enhancedResult = await this.attemptEnhancedScraping(context);
      if (enhancedResult.success) {
        return {
          success: true,
          result: enhancedResult,
          strategy: 'enhanced',
          reasoning: 'Enhanced scraping completed successfully'
        };
      }
    } catch (error) {
      context.enhancedScrapingError = error as Error;
      context.timeoutOccurred = (error as Error).message.includes('timeout');
      
      console.log(`⚠️ Enhanced scraping failed: ${(error as Error).message}`);
    }

    // Step 3: Execute fallback strategy
    return await this.executeFallbackStrategy(context);
  }

  /**
   * Check if enhanced scraping should be skipped based on cost optimization
   */
  private shouldSkipEnhancedScraping(context: FallbackContext): boolean {
    // If we have basic content and it's sufficient, skip enhanced scraping
    if (context.basicScrapingResult && this.isBasicContentSufficient(context.basicScrapingResult)) {
      context.costOptimizationDecision = 'basic';
      console.log('💰 COST-SAVE: Basic content is sufficient, skipping enhanced scraping');
      return true;
    }

    // Check if enhanced scraping is explicitly disabled
    if (!this.config.useBasicScraping) {
      context.costOptimizationDecision = 'skip';
      return true;
    }

    context.costOptimizationDecision = 'enhanced';
    return false;
  }

  /**
   * Attempt enhanced scraping with proper timeout handling
   */
  private async attemptEnhancedScraping(context: FallbackContext): Promise<EnhancedScrapeResult> {
    const request = context.originalRequest;
    const timeout = this.getTimeoutForMode('enhanced');

    console.log(`⚡ Attempting enhanced scraping with ${timeout}ms timeout...`);

    // Create enhanced scraping options
    const enhancedOptions: ScrapeOptions = {
      ...SCRAPING_CONFIG.defaultOptions.enhanced,
      ...request.options,
      enableJSRendering: true,
      timeout: timeout
    };

    // Execute enhanced scraping with timeout protection
    const result = await scrapeDoClient.scrapePage(request.url, enhancedOptions);

    if (!result.success) {
      throw new Error(result.error || 'Enhanced scraping failed');
    }

    // Convert to enhanced result format
    return {
      success: true,
      url: request.url,
      content: result.content,
      metadata: result.metadata,
      costAnalysis: {
        creditsUsed: 5, // Enhanced scraping typically uses 5 credits
        optimizationStrategy: 'Enhanced Scraping',
        reasoning: 'Enhanced scraping completed successfully'
      },
      contentAnalysis: {
        scenario: 'enhanced_successful',
        qualityScore: 90,
        needsEnhancedScraping: false
      }
    };
  }

  /**
   * Execute appropriate fallback strategy based on context
   */
  private async executeFallbackStrategy(context: FallbackContext): Promise<FallbackResult> {
    // Strategy 1: Use preserved basic content if available and sufficient
    if (this.config.preserveBasicContent && context.basicScrapingResult) {
      if (this.isBasicContentSufficient(context.basicScrapingResult)) {
        return this.useBasicContentFallback(context);
      }
    }

    // Strategy 2: Attempt basic scraping as fallback
    if (this.config.enhancedToBasicFallback) {
      return await this.attemptBasicScrapingFallback(context);
    }

    // Strategy 3: Return failure if no fallback options
    return {
      success: false,
      strategy: 'failed',
      reasoning: `Enhanced scraping failed: ${context.enhancedScrapingError?.message || 'Unknown error'}. No fallback options available.`
    };
  }

  /**
   * Use existing basic content as fallback
   */
  private useBasicContentFallback(context: FallbackContext): FallbackResult {
    if (!context.basicScrapingResult) {
      return {
        success: false,
        strategy: 'failed',
        reasoning: 'No basic content available for fallback'
      };
    }

    const result: EnhancedScrapeResult = {
      success: true,
      url: context.originalRequest.url,
      content: context.basicScrapingResult.content,
      metadata: context.basicScrapingResult.metadata,
      costAnalysis: {
        creditsUsed: 1, // Basic scraping uses 1 credit
        optimizationStrategy: 'Cost-Optimized Basic Scraping',
        reasoning: context.costOptimizationDecision === 'basic' 
          ? 'Basic content sufficient, enhanced scraping skipped'
          : 'Enhanced scraping failed, using basic content fallback'
      },
      contentAnalysis: {
        scenario: 'basic_fallback',
        qualityScore: 70,
        needsEnhancedScraping: false
      }
    };

    const costSavings = context.costOptimizationDecision === 'basic' ? 4 : 0; // Saved 4 credits by not doing enhanced

    return {
      success: true,
      result,
      strategy: 'preserved',
      reasoning: 'Using preserved basic content (cost-optimized)',
      costSavings
    };
  }

  /**
   * Attempt basic scraping as fallback
   */
  private async attemptBasicScrapingFallback(context: FallbackContext): Promise<FallbackResult> {
    try {
      console.log('🔄 Attempting basic scraping fallback...');

      const basicOptions: ScrapeOptions = {
        ...SCRAPING_CONFIG.defaultOptions.basic,
        timeout: this.getTimeoutForMode('basic')
      };

      const basicResult = await scrapeDoClient.scrapePage(context.originalRequest.url, basicOptions);

      if (!basicResult.success) {
        throw new Error(basicResult.error || 'Basic scraping fallback failed');
      }

      const result: EnhancedScrapeResult = {
        success: true,
        url: context.originalRequest.url,
        content: basicResult.content,
        metadata: basicResult.metadata,
        costAnalysis: {
          creditsUsed: 1,
          optimizationStrategy: 'Basic Scraping Fallback',
          reasoning: 'Enhanced scraping failed, basic scraping fallback successful'
        },
        contentAnalysis: {
          scenario: 'basic_fallback',
          qualityScore: 70,
          needsEnhancedScraping: false
        }
      };

      return {
        success: true,
        result,
        strategy: 'basic',
        reasoning: 'Enhanced scraping failed, basic scraping fallback successful'
      };

    } catch (error) {
      return {
        success: false,
        strategy: 'failed',
        reasoning: `Both enhanced and basic scraping failed. Enhanced: ${context.enhancedScrapingError?.message}. Basic: ${(error as Error).message}`
      };
    }
  }

  /**
   * Check if basic content is sufficient for AI processing
   */
  private isBasicContentSufficient(basicResult: ScrapeResult): boolean {
    if (!basicResult.success || !basicResult.content) {
      return false;
    }

    const contentLength = basicResult.content.length;
    const minLength = SCRAPING_CONFIG.contentProcessing.qualityThresholds.minimumLength;
    
    // Check content length
    if (contentLength < minLength) {
      return false;
    }

    // Check for substantial content (not just metadata)
    const substantialContentRegex = /[a-zA-Z]{50,}/g;
    const substantialMatches = basicResult.content.match(substantialContentRegex);
    
    if (!substantialMatches || substantialMatches.length < 3) {
      return false;
    }

    // Check quality score if available
    const qualityScore = (basicResult.metadata as any)?.qualityScore;
    if (qualityScore && qualityScore < SCRAPING_CONFIG.contentProcessing.qualityThresholds.minimumQualityScore) {
      return false;
    }

    return true;
  }

  /**
   * Get timeout configuration for specific scraping mode
   */
  getTimeoutForMode(mode: 'basic' | 'enhanced' | 'job'): number {
    switch (mode) {
      case 'basic':
        return SCRAPING_CONFIG.defaultOptions.basic.timeout || 15000;
      case 'enhanced':
        return SCRAPING_CONFIG.defaultOptions.enhanced.timeout || 50000;
      case 'job':
        return SCRAPING_CONFIG.api.timeout || 70000;
      default:
        return 30000;
    }
  }
}

export const getFallbackManager = () => new FallbackManager();
