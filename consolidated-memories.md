# Consolidated Augment Memories

## Technology Stack & Architecture
- **Framework**: Next.js with App Router, TypeScript, Tailwind CSS
- **Database**: Supabase (preferred over PostgreSQL) with snake_case for DB interfaces, camelCase for internal interfaces
- **Layout**: Full viewport layouts (min-h-screen, w-full) with custom CSS properties (--top-bar-height: 34px, --header-desk-height: 210px, --container-width: 1150px)
- **Architecture**: Hybrid with Next.js API routes, simpler background job processing (Bull/BullMQ with Redis or cron jobs)
- **Storage**: All data in DB, favicon/images stored on server

## UI Design System
- **Theme**: Dark theme with bg-zinc-900 background, #151515 borders, #2a2b31 card backgrounds
- **Colors**: Custom orange RGB(255, 150, 0) for hover effects
- **Typography**: Roboto font family
- **Layout**: Traditional card layouts with gaps (gap-4), rounded corners (rounded-lg), shadows (shadow-lg), black borders
- **Pages**: Tool detail pages with left-aligned main content, right sidebar for featured tools, sections for pros/cons, releases, verification status

## AI Content Generation System
- **Providers**: Dual AI provider support (OpenAI + OpenRouter) with Google Gemini 2.5 Pro Preview and GPT-4o-2024-11-20 models
- **Features**: Web scraping integration, GPT-4 content generation in irreverent voice, draft/published workflow states, smart categorization
- **Error Handling**: Fallback mechanisms between providers
- **AI Dude Prompt System**: 9 templates total - 1 system prompt, 1 complete user prompt, 7 partial generation templates (general partial, features, pricing, pros/cons, SEO, FAQ, releases)
- **Prompt Structure**: Strict separation between system prompts (methodology/schema variables) and user prompts (raw input variables like toolUrl and scrapedContent)
- **Automation**: Bulk import system automatically triggers media collection and AI content generation (admin users only)

## Web Scraping & Media Collection
- **API**: scrape.do integration with render=true and waitUntil=networkidle0 parameters
- **Content Format**: scrape.do returns Markdown (.md) format, requiring Markdown link parsing instead of HTML anchor tags
- **Media Priority**: Always extract favicon (use placeholder if unavailable), capture screenshot only when no OG images (og:image, twitter:image, facebook:image) available
- **Screenshots**: Standard viewport screenshots
- **Simplified Media**: For detailed submissions, require only one primary image (screenshot OR OG image) and one logo/favicon as single fields

## Database Design & Data Management
- **Naming**: snake_case for database interfaces, camelCase for internal interfaces with systematic conversion
- **URLs**: Clean URL slugs without timestamp suffixes, database-level unique constraints for tool names/slugs
- **FAQs**: Stored as JSONB arrays directly in tools table (not normalized tables)
- **Updates**: Manual Supabase SQL execution preferred over automated migrations
- **Validation**: Bidirectional snake_case/camelCase transformation validation

## Development Methodology & Quality Assurance
- **Approach**: Documentation-first implementation - all documentation deliverables must be completed before development begins
- **Implementation**: Structured 5-phase methodology (Research/Planning, Implementation, Quality Assurance, Documentation Updates, Version Control)
- **Testing**: Comprehensive 4-phase testing (Unit Testing, Integration Testing, Live Testing, Quality Assurance)
- **Requirements**: 100% Jest test pass rate (0 failures) before implementing new features, establishing test-first development
- **Tools**: MSW (Mock Service Worker) for API mocking with environment-aware setup (Node.js only)
- **Code Quality**: Strict TypeScript typing without 'any' types, proper ESLint compliance, no unused imports, specific interfaces instead of generic types

## Admin Tools & Editorial Workflow
- **Workflow States**: draft, under review, approved, published, rejected with Supabase integration
- **Admin Interface**: Dedicated admin tool management pages with separate routes (/admin/tools), consistent admin layout with sidebar starting below header
- **Import/Export**: Tool Import/Export functionality with CSV/JSON formats, data validation/sanitization, duplicate detection, transaction handling for bulk operations
- **Submission Pathways**: 
  - Simple Submit: URL/title/description only, then admin uses scraper + AI Dude
  - Detailed Submission: User fills all fields manually
  - Both require manual editorial review

## Package Management & Dependencies
- **Rule**: Always use appropriate package managers for dependency management instead of manually editing package configuration files
- **Tools**: npm/yarn/pnpm for Node.js, pip/poetry for Python, cargo for Rust, etc.
- **Rationale**: Prevents version mismatches, dependency conflicts, and broken builds

## Context & Documentation Tools
- **Preferred**: context7 tool for retrieving library documentation and context
- **Integration**: Seamless integration with development workflow
