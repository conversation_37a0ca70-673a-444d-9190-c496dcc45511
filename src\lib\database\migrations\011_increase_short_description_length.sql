-- Migration 011: Increase short_description field length
-- This migration increases the short_description field from VARCHAR(150) to VARCHAR(300)
-- to accommodate longer AI-generated descriptions and prevent constraint violations

-- =====================================================
-- UPDATE FIELD LENGTH
-- =====================================================

-- Increase short_description field length from 150 to 300 characters
ALTER TABLE tools ALTER COLUMN short_description TYPE VARCHAR(300);

-- =====================================================
-- VERIFICATION TESTS
-- =====================================================

-- Test longer short_description insertion
DO $$
DECLARE
    test_uuid VARCHAR(255) := 'test-short-desc-' || extract(epoch from now());
    long_description TEXT := 'This is a test short description that is longer than 150 characters to verify that the field length increase worked correctly. This description should now be accepted by the database without any constraint violations occurring during the content generation pipeline.';
BEGIN
    -- Test longer short_description
    INSERT INTO tools (
        id, 
        name, 
        slug,
        short_description,
        link,
        description
    ) VALUES (
        test_uuid, 
        'Short Description Test Tool', 
        'short-desc-test-' || extract(epoch from now()),
        long_description,
        '/tools/short-desc-test',
        'Test tool for short description length validation'
    );
    
    RAISE NOTICE 'SUCCESS: Long short_description (% chars) accepted', length(long_description);
    
    -- Clean up test record
    DELETE FROM tools WHERE id = test_uuid;
    
EXCEPTION
    WHEN string_data_right_truncation THEN
        RAISE EXCEPTION 'FAILED: short_description still has length constraint violation';
    WHEN OTHERS THEN
        RAISE NOTICE 'Test completed with other error (may be expected): %', SQLERRM;
        -- Clean up test record if it exists
        DELETE FROM tools WHERE id = test_uuid;
END $$;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check the updated column definition
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'tools' 
AND column_name = 'short_description';

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

-- Log successful migration
DO $$
BEGIN
    RAISE NOTICE '✅ Migration 011 completed successfully';
    RAISE NOTICE '✅ Increased short_description field from VARCHAR(150) to VARCHAR(300)';
    RAISE NOTICE '✅ Content generation pipeline should no longer encounter length violations';
    RAISE NOTICE '✅ AI-generated descriptions up to 300 characters now supported';
END $$;
