/**
 * Configurable Content Validation System
 * 
 * Uses database-stored validation rules instead of hardcoded values.
 * Allows admin panel configuration of validation standards.
 */

import { createClient } from '@supabase/supabase-js';

interface ValidationRule {
  id: string;
  field: string;
  type: 'required' | 'length' | 'array_length' | 'word_count' | 'enum' | 'format';
  params: {
    min?: number;
    max?: number;
    values?: string[];
    pattern?: string;
  };
  message: string;
  severity: 'error' | 'warning';
  enabled: boolean;
}

interface ValidationRulesConfig {
  contentStandards: {
    minDescriptionLength: number;
    maxDescriptionLength: number;
    minDetailedDescriptionWords: number;
    maxDetailedDescriptionWords: number;
    minFeaturesCount: number;
    maxFeaturesCount: number;
    minProsCount: number;
    maxProsCount: number;
    minConsCount: number;
    maxConsCount: number;
    minFaqsCount: number;
    maxFaqsCount: number;
    minHashtagsCount: number;
    maxHashtagsCount: number;
    maxMetaTitleLength: number;
    minMetaDescriptionLength: number;
    maxMetaDescriptionLength: number;
    maxTooltipLength: number;
  };
  validationRules: ValidationRule[];
  bannedWords: string[];
  requiredFields: string[];
}

interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
  value?: any;
}

interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  qualityScore: number;
}

export class ConfigurableValidator {
  private config: ValidationRulesConfig | null = null;
  private lastConfigLoad: number = 0;
  private configCacheTTL: number = 5 * 60 * 1000; // 5 minutes

  /**
   * Load validation configuration from database
   */
  private async loadValidationConfig(): Promise<ValidationRulesConfig> {
    const now = Date.now();
    
    // Return cached config if still valid
    if (this.config && (now - this.lastConfigLoad) < this.configCacheTTL) {
      return this.config;
    }

    try {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );

      const { data: configData, error } = await supabase
        .from('system_configuration')
        .select('config_value')
        .eq('config_key', 'validation_rules_config')
        .single();

      if (error || !configData) {
        console.warn('Validation config not found, using defaults');
        return this.getDefaultConfig();
      }

      this.config = configData.config_value as ValidationRulesConfig;
      this.lastConfigLoad = now;
      
      return this.config;
    } catch (error) {
      console.error('Failed to load validation config:', error);
      return this.getDefaultConfig();
    }
  }

  /**
   * Get default validation configuration
   */
  private getDefaultConfig(): ValidationRulesConfig {
    return {
      contentStandards: {
        minDescriptionLength: 50,
        maxDescriptionLength: 500,
        minDetailedDescriptionWords: 50,
        maxDetailedDescriptionWords: 300,
        minFeaturesCount: 3,
        maxFeaturesCount: 8,
        minProsCount: 3,
        maxProsCount: 10,
        minConsCount: 3,
        maxConsCount: 10,
        minFaqsCount: 3,
        maxFaqsCount: 8,
        minHashtagsCount: 5,
        maxHashtagsCount: 10,
        maxMetaTitleLength: 60,
        minMetaDescriptionLength: 150,
        maxMetaDescriptionLength: 160,
        maxTooltipLength: 100
      },
      validationRules: [],
      bannedWords: ['placeholder', 'lorem ipsum', 'test content'],
      requiredFields: ['name', 'description', 'detailed_description']
    };
  }

  /**
   * Validate content against configurable rules
   */
  async validateContent(content: any): Promise<ValidationResult> {
    const config = await this.loadValidationConfig();
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    let qualityScore = 100;

    // Validate required fields
    for (const field of config.requiredFields) {
      if (!this.getNestedValue(content, field)) {
        errors.push({
          field,
          message: `${field} is required`,
          severity: 'error',
          value: this.getNestedValue(content, field)
        });
        qualityScore -= 10;
      }
    }

    // Apply configurable validation rules
    for (const rule of config.validationRules) {
      if (!rule.enabled) continue;

      const fieldValue = this.getNestedValue(content, rule.field);
      const validationError = this.validateField(fieldValue, rule);

      if (validationError) {
        if (rule.severity === 'error') {
          errors.push(validationError);
          qualityScore -= 10;
        } else {
          warnings.push(validationError);
          qualityScore -= 5;
        }
      }
    }

    // Validate content standards
    const standardsErrors = this.validateContentStandards(content, config.contentStandards);
    errors.push(...standardsErrors.filter(e => e.severity === 'error'));
    warnings.push(...standardsErrors.filter(e => e.severity === 'warning'));
    qualityScore -= standardsErrors.length * 5;

    // Check banned words
    const bannedWordErrors = this.checkBannedWords(content, config.bannedWords);
    warnings.push(...bannedWordErrors);
    qualityScore -= bannedWordErrors.length * 3;

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      qualityScore: Math.max(0, qualityScore)
    };
  }

  /**
   * Validate individual field against rule
   */
  private validateField(value: any, rule: ValidationRule): ValidationError | null {
    switch (rule.type) {
      case 'required':
        if (!value || (typeof value === 'string' && value.trim() === '')) {
          return {
            field: rule.field,
            message: rule.message,
            severity: rule.severity,
            value
          };
        }
        break;

      case 'length':
        if (typeof value === 'string') {
          const length = value.length;
          if ((rule.params.min && length < rule.params.min) || 
              (rule.params.max && length > rule.params.max)) {
            return {
              field: rule.field,
              message: rule.message,
              severity: rule.severity,
              value: length
            };
          }
        }
        break;

      case 'word_count':
        if (typeof value === 'string') {
          const wordCount = value.split(/\s+/).filter(w => w.length > 0).length;
          if ((rule.params.min && wordCount < rule.params.min) || 
              (rule.params.max && wordCount > rule.params.max)) {
            return {
              field: rule.field,
              message: rule.message,
              severity: rule.severity,
              value: wordCount
            };
          }
        }
        break;

      case 'array_length':
        if (Array.isArray(value)) {
          const length = value.length;
          if ((rule.params.min && length < rule.params.min) || 
              (rule.params.max && length > rule.params.max)) {
            return {
              field: rule.field,
              message: rule.message,
              severity: rule.severity,
              value: length
            };
          }
        }
        break;

      case 'enum':
        if (rule.params.values && !rule.params.values.includes(value)) {
          return {
            field: rule.field,
            message: rule.message,
            severity: rule.severity,
            value
          };
        }
        break;

      case 'format':
        if (typeof value === 'string' && rule.params.pattern) {
          const regex = new RegExp(rule.params.pattern);
          if (!regex.test(value)) {
            return {
              field: rule.field,
              message: rule.message,
              severity: rule.severity,
              value
            };
          }
        }
        break;
    }

    return null;
  }

  /**
   * Validate content against standards
   */
  private validateContentStandards(content: any, standards: ValidationRulesConfig['contentStandards']): ValidationError[] {
    const errors: ValidationError[] = [];

    // Validate description length
    if (content.description) {
      const length = content.description.length;
      if (length < standards.minDescriptionLength) {
        errors.push({
          field: 'description',
          message: `Description too short (${length} chars, minimum ${standards.minDescriptionLength})`,
          severity: 'error',
          value: length
        });
      } else if (length > standards.maxDescriptionLength) {
        errors.push({
          field: 'description',
          message: `Description too long (${length} chars, maximum ${standards.maxDescriptionLength})`,
          severity: 'warning',
          value: length
        });
      }
    }

    // Validate detailed description word count
    if (content.detailed_description) {
      const wordCount = content.detailed_description.split(/\s+/).filter((w: string) => w.length > 0).length;
      if (wordCount < standards.minDetailedDescriptionWords) {
        errors.push({
          field: 'detailed_description',
          message: `Detailed description too short (${wordCount} words, minimum ${standards.minDetailedDescriptionWords})`,
          severity: 'error',
          value: wordCount
        });
      } else if (wordCount > standards.maxDetailedDescriptionWords) {
        errors.push({
          field: 'detailed_description',
          message: `Detailed description too long (${wordCount} words, maximum ${standards.maxDetailedDescriptionWords})`,
          severity: 'warning',
          value: wordCount
        });
      }
    }

    // Validate features count
    if (content.features && Array.isArray(content.features)) {
      const count = content.features.length;
      if (count < standards.minFeaturesCount) {
        errors.push({
          field: 'features',
          message: `Too few features (${count}, minimum ${standards.minFeaturesCount})`,
          severity: 'error',
          value: count
        });
      } else if (count > standards.maxFeaturesCount) {
        errors.push({
          field: 'features',
          message: `Too many features (${count}, maximum ${standards.maxFeaturesCount})`,
          severity: 'warning',
          value: count
        });
      }
    }

    // Validate pros and cons
    if (content.pros_and_cons) {
      if (content.pros_and_cons.pros && Array.isArray(content.pros_and_cons.pros)) {
        const count = content.pros_and_cons.pros.length;
        if (count < standards.minProsCount) {
          errors.push({
            field: 'pros_and_cons.pros',
            message: `Too few pros (${count}, minimum ${standards.minProsCount})`,
            severity: 'error',
            value: count
          });
        } else if (count > standards.maxProsCount) {
          errors.push({
            field: 'pros_and_cons.pros',
            message: `Too many pros (${count}, maximum ${standards.maxProsCount})`,
            severity: 'warning',
            value: count
          });
        }
      }

      if (content.pros_and_cons.cons && Array.isArray(content.pros_and_cons.cons)) {
        const count = content.pros_and_cons.cons.length;
        if (count < standards.minConsCount) {
          errors.push({
            field: 'pros_and_cons.cons',
            message: `Too few cons (${count}, minimum ${standards.minConsCount})`,
            severity: 'error',
            value: count
          });
        } else if (count > standards.maxConsCount) {
          errors.push({
            field: 'pros_and_cons.cons',
            message: `Too many cons (${count}, maximum ${standards.maxConsCount})`,
            severity: 'warning',
            value: count
          });
        }
      }
    }

    return errors;
  }

  /**
   * Check for banned words
   */
  private checkBannedWords(content: any, bannedWords: string[]): ValidationError[] {
    const errors: ValidationError[] = [];
    const contentText = JSON.stringify(content).toLowerCase();

    for (const bannedWord of bannedWords) {
      if (contentText.includes(bannedWord.toLowerCase())) {
        errors.push({
          field: 'content',
          message: `Content contains banned word: '${bannedWord}'`,
          severity: 'warning',
          value: bannedWord
        });
      }
    }

    return errors;
  }

  /**
   * Get nested object value by dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Clear configuration cache (useful for testing or immediate updates)
   */
  public clearCache(): void {
    this.config = null;
    this.lastConfigLoad = 0;
  }
}

// Export singleton instance
export const configurableValidator = new ConfigurableValidator();
