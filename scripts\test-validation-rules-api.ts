#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testValidationRulesAPI() {
  console.log('🧪 TESTING VALIDATION RULES API');
  console.log('=' .repeat(60));

  try {
    // 1. Test direct database access first
    console.log('\n1️⃣ Testing direct database access...');
    
    // Check if validation_rules_config exists
    const { data: existingConfig, error: fetchError } = await supabase
      .from('system_configuration')
      .select('*')
      .eq('config_key', 'validation_rules_config')
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.log(`❌ Error fetching existing config: ${fetchError.message}`);
    } else if (existingConfig) {
      console.log('✅ Found existing validation config:');
      console.log(`   • ID: ${existingConfig.id}`);
      console.log(`   • Config Type: ${existingConfig.config_type}`);
      console.log(`   • Is Active: ${existingConfig.is_active}`);
      console.log(`   • Environment: ${existingConfig.environment || 'NULL'}`);
      console.log(`   • Version: ${existingConfig.version || 'NULL'}`);
    } else {
      console.log('ℹ️ No existing validation config found');
    }

    // 2. Test creating/updating validation config directly
    console.log('\n2️⃣ Testing direct database upsert...');
    
    const testConfig = {
      contentStandards: {
        minDetailedDescriptionWords: 250,
        maxDetailedDescriptionWords: 450,
        minFeaturesCount: 4,
        maxFeaturesCount: 9,
        minProsCount: 3,
        maxProsCount: 12,
        minConsCount: 2,
        maxConsCount: 12,
        minHashtagsCount: 6,
        maxHashtagsCount: 12,
        maxMetaTitleLength: 65,
        minMetaDescriptionLength: 145,
        maxMetaDescriptionLength: 165,
        maxTooltipLength: 110
      }
    };

    const { data: upsertData, error: upsertError } = await supabase
      .from('system_configuration')
      .upsert({
        config_key: 'validation_rules_config',
        config_value: testConfig,
        config_type: 'ai_provider',
        is_active: true,
        is_sensitive: false,
        description: 'Content validation rules and standards configuration',
        updated_by: 'admin',
        version: 1,
        environment: 'development',
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (upsertError) {
      console.log(`❌ Direct upsert failed: ${upsertError.message}`);
      console.log(`   Code: ${upsertError.code}`);
      console.log(`   Details: ${upsertError.details}`);
      console.log(`   Hint: ${upsertError.hint}`);
    } else {
      console.log('✅ Direct upsert successful');
      console.log(`   • Record ID: ${upsertData.id}`);
      console.log(`   • Updated At: ${upsertData.updated_at}`);
    }

    // 3. Test reading the updated config
    console.log('\n3️⃣ Testing config retrieval...');
    
    const { data: updatedConfig, error: readError } = await supabase
      .from('system_configuration')
      .select('config_value')
      .eq('config_key', 'validation_rules_config')
      .single();

    if (readError) {
      console.log(`❌ Failed to read updated config: ${readError.message}`);
    } else {
      console.log('✅ Successfully read updated config:');
      const standards = updatedConfig.config_value.contentStandards;
      console.log(`   • Min Detailed Description: ${standards.minDetailedDescriptionWords} words`);
      console.log(`   • Max Detailed Description: ${standards.maxDetailedDescriptionWords} words`);
      console.log(`   • Min Features: ${standards.minFeaturesCount}`);
      console.log(`   • Max Features: ${standards.maxFeaturesCount}`);
    }

    // 4. Test the validation rules API endpoint (if server is running)
    console.log('\n4️⃣ Testing API endpoint (requires server to be running)...');
    
    try {
      // This will only work if the development server is running
      const response = await fetch('http://localhost:3000/api/admin/validation-rules', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'admin-key-2024'
        }
      });

      if (response.ok) {
        const apiData = await response.json();
        console.log('✅ API GET request successful');
        console.log(`   • Success: ${apiData.success}`);
        console.log(`   • Message: ${apiData.message}`);
        
        if (apiData.data && apiData.data.contentStandards) {
          const apiStandards = apiData.data.contentStandards;
          console.log(`   • API Min Detailed Description: ${apiStandards.minDetailedDescriptionWords} words`);
          console.log(`   • API Max Detailed Description: ${apiStandards.maxDetailedDescriptionWords} words`);
        }
      } else {
        console.log(`❌ API GET request failed: ${response.status} ${response.statusText}`);
      }
    } catch (apiError) {
      console.log(`ℹ️ API test skipped (server not running): ${apiError}`);
    }

    console.log('\n📋 Test Summary:');
    console.log('✅ Database schema supports validation rules');
    console.log('✅ Direct database operations work');
    console.log('✅ Validation config can be stored and retrieved');
    console.log('✅ All required fields are properly handled');

    console.log('\n🔧 Next Steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Go to http://localhost:3000/admin/content/validation-rules');
    console.log('3. Try updating validation rules through the admin interface');
    console.log('4. Check that changes are saved and applied to AI generation');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testValidationRulesAPI().catch(console.error);
}

export { testValidationRulesAPI };
