#!/usr/bin/env tsx

/**
 * Comprehensive Job Action System Audit
 * 
 * This script audits the entire job action system to verify consistency
 * between UI components, backend logic, and API endpoints.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Define expected action mappings based on audit requirements
const EXPECTED_ACTION_MAPPINGS = {
  'pending': {
    base: ['stop', 'delete'],
    conditional: { stale: 'retry' }, // >30 min
    description: 'Waiting to be processed'
  },
  'processing': {
    base: ['pause', 'stop'],
    conditional: {},
    description: 'Currently being processed'
  },
  'paused': {
    base: ['resume', 'stop'],
    conditional: {},
    description: 'Temporarily paused'
  },
  'stopped': {
    base: ['retry', 'delete'],
    conditional: {},
    description: 'Manually stopped'
  },
  'failed': {
    base: ['retry', 'delete'],
    conditional: {},
    description: 'Processing failed'
  },
  'retrying': {
    base: ['delete'],
    conditional: { stuck: 'retry' }, // >15 min
    description: 'Attempting retry'
  },
  'completed': {
    base: ['delete'],
    conditional: {},
    description: 'Successfully completed'
  },
  'cancelled': {
    base: ['retry', 'delete'],
    conditional: {},
    description: 'Cancelled by user'
  },
  'stopping': {
    base: ['delete'],
    conditional: {},
    description: 'In process of stopping'
  }
};

async function auditJobActionSystem() {
  console.log('🔍 COMPREHENSIVE JOB ACTION SYSTEM AUDIT\n');
  console.log('=' .repeat(60) + '\n');

  const issues: string[] = [];
  const warnings: string[] = [];

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. AUDIT UI COMPONENT CONSISTENCY
    console.log('1. 📋 AUDITING UI COMPONENT CONSISTENCY\n');

    // Simulate JobListTable logic
    const jobListTableLogic = (status: string, isStale: boolean, isStuck: boolean) => {
      switch (status) {
        case 'processing': return ['pause', 'stop'];
        case 'paused': return ['resume', 'stop'];
        case 'failed': return ['retry', 'delete'];
        case 'retrying': return isStuck ? ['retry', 'delete'] : ['delete'];
        case 'completed': return ['delete'];
        case 'pending': 
          const actions = ['stop', 'delete'];
          if (isStale) actions.unshift('retry');
          return actions;
        case 'stopped': return ['retry', 'delete'];
        case 'cancelled': return ['retry', 'delete'];
        case 'stopping': return ['delete'];
        default: return ['delete'];
      }
    };

    // Simulate JobDetailsModal logic
    const jobDetailsModalLogic = (status: string) => {
      switch (status) {
        case 'processing': return ['pause', 'stop'];
        case 'paused': return ['resume', 'stop'];
        case 'failed': return ['retry', 'delete'];
        case 'retrying': return ['retry', 'delete'];
        case 'completed': return ['delete'];
        case 'pending': return ['stop', 'delete'];
        case 'stopped': return ['retry', 'delete'];
        case 'cancelled': return ['retry', 'delete'];
        case 'stopping': return ['delete'];
        default: return ['delete'];
      }
    };

    // Check consistency between components
    for (const [status, expected] of Object.entries(EXPECTED_ACTION_MAPPINGS)) {
      const listTableActions = jobListTableLogic(status, false, false);
      const detailsModalActions = jobDetailsModalLogic(status);
      
      console.log(`   Status: ${status.toUpperCase()}`);
      console.log(`      Expected: ${expected.base.join(', ')}`);
      console.log(`      JobListTable: ${listTableActions.join(', ')}`);
      console.log(`      JobDetailsModal: ${detailsModalActions.join(', ')}`);

      // Check base actions consistency
      const expectedBase = expected.base.sort();
      const listTableBase = listTableActions.filter(a => a !== 'retry' || !expected.conditional.stale).sort();
      const detailsModalBase = detailsModalActions.sort();

      if (JSON.stringify(expectedBase) !== JSON.stringify(listTableBase)) {
        issues.push(`❌ JobListTable: ${status} shows [${listTableBase.join(', ')}] but expected [${expectedBase.join(', ')}]`);
      }

      if (JSON.stringify(expectedBase) !== JSON.stringify(detailsModalBase)) {
        issues.push(`❌ JobDetailsModal: ${status} shows [${detailsModalBase.join(', ')}] but expected [${expectedBase.join(', ')}]`);
      }

      if (JSON.stringify(listTableBase) !== JSON.stringify(detailsModalBase)) {
        issues.push(`❌ Inconsistency: JobListTable vs JobDetailsModal for ${status} status`);
      }

      console.log(`      ✅ Consistency: ${issues.length === 0 ? 'PASS' : 'FAIL'}\n`);
    }

    // 2. AUDIT CONDITIONAL LOGIC
    console.log('2. ⏰ AUDITING CONDITIONAL LOGIC\n');

    // Test stale pending logic (>30 min)
    const stalePendingActions = jobListTableLogic('pending', true, false);
    if (!stalePendingActions.includes('retry')) {
      issues.push('❌ Stale pending jobs (>30 min) should show retry option');
    } else {
      console.log('   ✅ Stale pending jobs correctly show retry option');
    }

    // Test stuck retrying logic (>15 min)
    const stuckRetryingActions = jobListTableLogic('retrying', false, true);
    if (!stuckRetryingActions.includes('retry')) {
      issues.push('❌ Stuck retrying jobs (>15 min) should show retry option');
    } else {
      console.log('   ✅ Stuck retrying jobs correctly show retry option');
    }

    // 3. AUDIT BACKEND API SUPPORT
    console.log('\n3. 🔧 AUDITING BACKEND API SUPPORT\n');

    const supportedApiActions = ['retry', 'pause', 'resume', 'stop', 'cancel', 'delete'];
    const requiredActions = new Set();
    
    Object.values(EXPECTED_ACTION_MAPPINGS).forEach(mapping => {
      mapping.base.forEach(action => requiredActions.add(action));
      Object.values(mapping.conditional).forEach(action => requiredActions.add(action));
    });

    console.log(`   Required actions: ${Array.from(requiredActions).join(', ')}`);
    console.log(`   API supports: ${supportedApiActions.join(', ')}`);

    const missingActions = Array.from(requiredActions).filter(action => !supportedApiActions.includes(action));
    if (missingActions.length > 0) {
      issues.push(`❌ API missing support for: ${missingActions.join(', ')}`);
    } else {
      console.log('   ✅ API supports all required actions');
    }

    // 4. TEST WITH REAL JOBS (if available)
    console.log('\n4. 🧪 TESTING WITH REAL JOBS\n');

    const { data: jobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(10);

    if (jobsError) {
      warnings.push(`⚠️ Could not fetch jobs for testing: ${jobsError.message}`);
    } else if (jobs && jobs.length > 0) {
      console.log(`   Found ${jobs.length} jobs for testing\n`);

      const statusCounts: Record<string, number> = {};
      const now = new Date();

      jobs.forEach((job, index) => {
        const status = job.status;
        statusCounts[status] = (statusCounts[status] || 0) + 1;

        if (index < 3) { // Test first 3 jobs
          const updatedAt = new Date(job.updated_at);
          const minutesSinceUpdate = (now.getTime() - updatedAt.getTime()) / (1000 * 60);
          
          const isStale = status === 'pending' && minutesSinceUpdate > 30;
          const isStuck = status === 'retrying' && minutesSinceUpdate > 15;

          const expectedActions = jobListTableLogic(status, isStale, isStuck);
          
          console.log(`   Job ${index + 1}: ${job.id.substring(0, 8)}...`);
          console.log(`      Status: ${status}`);
          console.log(`      Age: ${Math.round(minutesSinceUpdate)} minutes`);
          console.log(`      Expected actions: ${expectedActions.join(', ')}`);
          
          if (isStale) console.log(`      🔄 Stale pending - retry available`);
          if (isStuck) console.log(`      ⚠️ Stuck retrying - force retry available`);
          console.log('');
        }
      });

      console.log('   Status distribution:');
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log(`      ${status}: ${count} job(s)`);
      });
    } else {
      warnings.push('⚠️ No jobs found for testing');
    }

    // 5. SUMMARY AND RECOMMENDATIONS
    console.log('\n5. 📊 AUDIT SUMMARY\n');
    console.log('=' .repeat(60));

    if (issues.length === 0) {
      console.log('✅ AUDIT PASSED - No critical issues found!');
    } else {
      console.log(`❌ AUDIT FAILED - ${issues.length} critical issue(s) found:`);
      issues.forEach(issue => console.log(`   ${issue}`));
    }

    if (warnings.length > 0) {
      console.log(`\n⚠️ ${warnings.length} warning(s):`);
      warnings.forEach(warning => console.log(`   ${warning}`));
    }

    console.log('\n📋 EXPECTED BEHAVIOR SUMMARY:');
    Object.entries(EXPECTED_ACTION_MAPPINGS).forEach(([status, mapping]) => {
      let actionText = mapping.base.join(', ');
      if (Object.keys(mapping.conditional).length > 0) {
        const conditionals = Object.entries(mapping.conditional)
          .map(([condition, action]) => `+${action} if ${condition}`)
          .join(', ');
        actionText += ` (${conditionals})`;
      }
      console.log(`   ${status.toUpperCase()}: [${actionText}] - ${mapping.description}`);
    });

    return { issues, warnings };

  } catch (error) {
    console.error('💥 Audit failed:', error);
    return { issues: [`💥 Audit failed: ${error}`], warnings: [] };
  }
}

// Run the audit
auditJobActionSystem()
  .then(({ issues, warnings }) => {
    console.log('\n' + '='.repeat(60));
    if (issues.length === 0) {
      console.log('🎯 JOB ACTION SYSTEM AUDIT COMPLETED SUCCESSFULLY!');
    } else {
      console.log('🚨 JOB ACTION SYSTEM AUDIT FOUND ISSUES - FIXES REQUIRED');
      process.exit(1);
    }
  })
  .catch(console.error);
