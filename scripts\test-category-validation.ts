#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Simplified category validation logic (no mapping)
 */
function validateCategoryId(categoryId: string, existingIds: Set<string>): { exists: boolean; action: string } {
  const exists = existingIds.has(categoryId);
  return {
    exists,
    action: exists ? 'USE_EXISTING' : 'CREATE_NEW'
  };
}

async function testCategoryValidation() {
  console.log('🧪 TESTING CATEGORY VALIDATION LOGIC');
  console.log('=' .repeat(60));

  try {
    // Get all existing categories
    const { data: categories, error } = await supabase
      .from('categories')
      .select('id, title')
      .order('id');

    if (error) {
      console.error('❌ Error fetching categories:', error.message);
      return;
    }

    console.log(`📊 Found ${categories?.length || 0} categories in database:`);
    const existingIds = new Set(categories?.map(c => c.id) || []);
    categories?.forEach(cat => {
      console.log(`   • ${cat.id} - ${cat.title}`);
    });

    // Test simplified category validation
    console.log('\n🔄 Testing simplified category validation logic:');
    const testCases = [
      'writing-tools', // Existing category
      'food-ai', // New category that would be created
      'cooking-tools', // New category that would be created
      'productivity-ai', // Existing category
      'invalid-category' // New category that would be created
    ];

    testCases.forEach(testCase => {
      const result = validateCategoryId(testCase, existingIds);
      const status = result.exists ? '✅ EXISTS' : '🔧 WILL CREATE';
      console.log(`   ${testCase} → ${result.action} ${status}`);
    });

    // Test the specific case that might have caused the error
    console.log('\n🎯 Testing potential AI-generated categories:');
    const aiGeneratedCases = [
      'food-preparation',
      'cooking',
      'meal-planning',
      'recipe-generation',
      'culinary-ai',
      'health-fitness',
      'business-tools'
    ];

    aiGeneratedCases.forEach(testCase => {
      const result = validateCategoryId(testCase, existingIds);
      const status = result.exists ? '✅ EXISTS' : '🔧 WILL CREATE';
      console.log(`   ${testCase} → ${result.action} ${status}`);
    });

    console.log('\n📋 Summary:');
    console.log('✅ Simplified category validation logic is working');
    console.log('✅ No mapping - AI categories are used exactly as provided');
    console.log('✅ Unknown categories will be created automatically');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testCategoryValidation().catch(console.error);
}

export { testCategoryValidation };
