import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Mock the pipeline's isBulkProcessingTool method
async function testIsBulkProcessingTool(toolId: string): Promise<boolean> {
  const maxRetries = 3;
  const retryDelay = 1000; // 1 second

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔍 Testing bulk processing status for tool: ${toolId} (attempt ${attempt}/${maxRetries})`);

      // Validate tool ID format
      if (!toolId || typeof toolId !== 'string' || toolId.trim().length === 0) {
        console.warn(`❌ Invalid tool ID format: ${toolId}`);
        return false;
      }

      // First, check if tool exists and handle multiple/no rows gracefully
      const { data: tools, error: queryError } = await supabase
        .from('tools')
        .select('submission_source, submission_type, name, website, id')
        .eq('id', toolId.trim());

      if (queryError) {
        console.warn(`❌ Database query error for tool ${toolId} (attempt ${attempt}): ${queryError.message}`);
        
        // If this is the last attempt, log detailed error and return false
        if (attempt === maxRetries) {
          console.warn(`❌ Final attempt failed for tool ${toolId}. Error details:`, {
            code: queryError.code,
            message: queryError.message,
            details: queryError.details,
            hint: queryError.hint
          });
          console.warn(`Could not determine submission source for tool ${toolId}, defaulting to user submission workflow`);
          return false;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        continue;
      }

      if (!tools || tools.length === 0) {
        console.warn(`❌ Tool ${toolId} not found in database (attempt ${attempt})`);
        
        // For "not found" errors, don't retry - the tool simply doesn't exist
        console.warn(`Could not determine submission source for tool ${toolId}, defaulting to user submission workflow`);
        return false;
      }

      if (tools.length > 1) {
        console.warn(`⚠️ Multiple tools found with ID ${toolId} - this indicates a database integrity issue`);
        console.warn(`Found ${tools.length} tools with same ID. Using first tool for submission source detection`);
        
        // Log all duplicate tools for debugging
        tools.forEach((tool, index) => {
          console.warn(`  Tool ${index + 1}: ${tool.name} (${tool.website}) - ${tool.submission_source}`);
        });
      }

      // Use the first (or only) tool
      const tool = tools[0];

      // Validate tool data
      if (!tool.submission_source) {
        console.warn(`⚠️ Tool ${toolId} has null/undefined submission_source, defaulting to user submission workflow`);
        return false;
      }

      console.log(`📋 Tool details: ${tool.name} (${tool.website})`);
      console.log(`📝 Submission type: ${tool.submission_type}`);
      console.log(`📋 Submission source: ${tool.submission_source}`);

      const isBulkProcessing = tool.submission_source === 'bulk_processing';
      console.log(`🎯 Bulk processing detection result: ${isBulkProcessing ? 'YES - will bypass manual review' : 'NO - will go to manual review'}`);

      return isBulkProcessing;
    } catch (error) {
      console.warn(`💥 Exception checking bulk processing status for tool ${toolId} (attempt ${attempt}):`, error);
      
      // If this is the last attempt, return false
      if (attempt === maxRetries) {
        console.warn(`Could not determine submission source for tool ${toolId} after ${maxRetries} attempts, defaulting to user submission workflow`);
        return false;
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
    }
  }

  // This should never be reached, but just in case
  console.warn(`Unexpected end of retry loop for tool ${toolId}, defaulting to user submission workflow`);
  return false;
}

async function runTests() {
  console.log('🧪 Testing Pipeline Fixes...\n');

  // Test 1: Valid bulk processing tool (the one that failed in logs)
  console.log('Test 1: Valid bulk processing tool (from logs)');
  const result1 = await testIsBulkProcessingTool('2d25e305-1924-49e2-a5b4-c1e80b951123');
  console.log(`Result: ${result1 ? 'PASS' : 'FAIL'} - Expected: true, Got: ${result1}\n`);

  // Test 2: Invalid tool ID
  console.log('Test 2: Invalid tool ID');
  const result2 = await testIsBulkProcessingTool('invalid-tool-id');
  console.log(`Result: ${result2 ? 'FAIL' : 'PASS'} - Expected: false, Got: ${result2}\n`);

  // Test 3: Empty tool ID
  console.log('Test 3: Empty tool ID');
  const result3 = await testIsBulkProcessingTool('');
  console.log(`Result: ${result3 ? 'FAIL' : 'PASS'} - Expected: false, Got: ${result3}\n`);

  // Test 4: Null tool ID
  console.log('Test 4: Null tool ID');
  const result4 = await testIsBulkProcessingTool(null as any);
  console.log(`Result: ${result4 ? 'FAIL' : 'PASS'} - Expected: false, Got: ${result4}\n`);

  console.log('🎯 Summary:');
  const tests = [
    { name: 'Valid bulk processing tool (from logs)', result: result1, expected: true },
    { name: 'Invalid tool ID', result: result2, expected: false },
    { name: 'Empty tool ID', result: result3, expected: false },
    { name: 'Null tool ID', result: result4, expected: false },
  ];

  let passed = 0;
  tests.forEach((test, i) => {
    const success = test.result === test.expected;
    console.log(`  Test ${i + 1}: ${success ? '✅ PASS' : '❌ FAIL'} - ${test.name}`);
    if (success) passed++;
  });

  console.log(`\n📊 Results: ${passed}/${tests.length} tests passed`);
  
  if (passed === tests.length) {
    console.log('🎉 All pipeline fixes are working correctly!');
  } else {
    console.log('⚠️ Some tests failed - pipeline fixes may need adjustment');
  }
}

runTests().catch(console.error);
