import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { ToolDetailPage } from '@/components/features/ToolDetailPage';
import { findToolById } from '@/lib/categoryUtils';
import { getTools } from '@/lib/supabase';
import { AITool } from '@/lib/types';

interface ToolPageProps {
  params: {
    toolId: string;
  };
}

// Generate metadata for SEO
export async function generateMetadata({ params }: ToolPageProps): Promise<Metadata> {
  const { toolId } = await params;
  const tool = await findToolById(toolId);

  if (!tool) {
    return {
      title: 'Tool Not Found - AI Dude Directory',
      description: 'The requested AI tool could not be found.',
    };
  }

  // Use database SEO fields with fallbacks to defaults
  const title = tool.metaTitle || `${tool.name} - AI Tool Review | AI Dude Directory`;
  const description = tool.metaDescription || tool.detailedDescription || tool.description;
  const keywords = tool.metaKeywords || `${tool.name}, AI tool, ${tool.category}, artificial intelligence, review`;

  return {
    title,
    description,
    keywords,
    openGraph: {
      title: tool.metaTitle || `${tool.name} - AI Tool Review`,
      description,
      images: tool.screenshots ? [tool.screenshots[0]] : (tool.logoUrl ? [tool.logoUrl] : []),
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title: tool.metaTitle || `${tool.name} - AI Tool Review`,
      description,
      images: tool.screenshots ? [tool.screenshots[0]] : (tool.logoUrl ? [tool.logoUrl] : []),
    },
  };
}

// Generate static params for all tools (for static generation)
export async function generateStaticParams() {
  // Disable static generation for now to avoid build issues
  return [];
}

export default async function ToolPage({ params }: ToolPageProps) {
  const { toolId } = await params;
  const tool = await findToolById(toolId);

  if (!tool) {
    notFound();
  }

  return <ToolDetailPage tool={tool} />;
}
