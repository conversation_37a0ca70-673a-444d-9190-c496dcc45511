import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/admin/content/validate
 * Validate AI-generated content using AI Dude methodology
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { content, methodology = 'ai_dude', toolId } = body;

    if (!content) {
      return NextResponse.json(
        { success: false, error: 'Content is required for validation' },
        { status: 400 }
      );
    }

    // Get AI Dude validation template from database
    const { data: template, error: templateError } = await supabase
      .from('system_configuration')
      .select('config_value')
      .eq('config_key', 'prompt_ai_dude_validation')
      .single();

    if (templateError || !template?.config_value?.template) {
      return NextResponse.json(
        { success: false, error: 'AI Dude validation template not found' },
        { status: 500 }
      );
    }

    // Perform comprehensive validation
    const validationResult = await performAIDudeValidation(content, template.config_value.template);

    // Store validation results if toolId provided
    if (toolId) {
      await storeValidationResults(toolId, validationResult);
    }

    return NextResponse.json({
      success: true,
      data: validationResult,
      message: 'Content validation completed'
    });

  } catch (error: any) {
    console.error('Content validation API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to validate content' 
      },
      { status: 500 }
    );
  }
}

/**
 * Perform AI Dude content validation
 */
async function performAIDudeValidation(content: any, validationTemplate: string): Promise<any> {
  const issues: string[] = [];
  const warnings: string[] = [];
  let qualityScore = 100;

  // Required field validation
  const requiredFields = [
    'name', 'description', 'short_description', 'detailed_description',
    'company', 'category_primary', 'features', 'pricing', 'pros_and_cons',
    'faqs', 'hashtags', 'meta_title', 'meta_description'
  ];

  for (const field of requiredFields) {
    if (!content[field]) {
      issues.push(`Missing required field: ${field}`);
      qualityScore -= 10;
    }
  }

  // Content length validation
  if (content.description && content.description.length > 500) {
    warnings.push('Description exceeds 500 character limit');
    qualityScore -= 5;
  }

  if (content.short_description && content.short_description.length > 150) {
    warnings.push('Short description exceeds 150 character limit');
    qualityScore -= 5;
  }

  if (content.detailed_description) {
    const wordCount = content.detailed_description.split(' ').length;
    if (wordCount < 50) {
      warnings.push(`Detailed description too short (${wordCount} words, minimum 50)`);
      qualityScore -= 10;
    } else if (wordCount > 300) {
      warnings.push(`Detailed description too long (${wordCount} words, maximum 300)`);
      qualityScore -= 5;
    }
  }

  // Features validation
  if (content.features) {
    if (!Array.isArray(content.features)) {
      issues.push('Features must be an array');
      qualityScore -= 15;
    } else if (content.features.length < 3) {
      warnings.push(`Too few features (${content.features.length}, minimum 3)`);
      qualityScore -= 10;
    } else if (content.features.length > 8) {
      warnings.push(`Too many features (${content.features.length}, maximum 8)`);
      qualityScore -= 5;
    }
  }

  // Pricing validation
  if (content.pricing) {
    if (!content.pricing.type) {
      issues.push('Pricing type is required');
      qualityScore -= 10;
    } else {
      const validTypes = ['Free', 'Paid', 'Freemium', 'Open Source'];
      if (!validTypes.includes(content.pricing.type)) {
        warnings.push(`Invalid pricing type: ${content.pricing.type}`);
        qualityScore -= 5;
      }
    }
  }

  // Use configurable validation for pros and cons
  try {
    const { configurableValidator } = await import('@/lib/validation/configurable-validator');
    const validationResult = await configurableValidator.validateContent(content);

    // Add configurable validation results
    for (const error of validationResult.errors) {
      if (error.field.includes('pros') || error.field.includes('cons')) {
        issues.push(error.message);
        qualityScore -= 10;
      }
    }

    for (const warning of validationResult.warnings) {
      if (warning.field.includes('pros') || warning.field.includes('cons')) {
        warnings.push(warning.message);
        qualityScore -= 5;
      }
    }
  } catch (error) {
    console.error('Configurable validation failed, using fallback:', error);

    // Fallback to basic validation
    if (content.pros_and_cons) {
      if (!content.pros_and_cons.pros || !Array.isArray(content.pros_and_cons.pros)) {
        issues.push('Pros must be an array');
        qualityScore -= 10;
      } else if (content.pros_and_cons.pros.length < 3) {
        warnings.push(`Too few pros (${content.pros_and_cons.pros.length}, minimum 3)`);
        qualityScore -= 5;
      }

      if (!content.pros_and_cons.cons || !Array.isArray(content.pros_and_cons.cons)) {
        issues.push('Cons must be an array');
        qualityScore -= 10;
      } else if (content.pros_and_cons.cons.length < 3) {
        warnings.push(`Too few cons (${content.pros_and_cons.cons.length}, minimum 3)`);
        qualityScore -= 5;
      }
    }
  }

  // FAQ validation
  if (content.faqs) {
    if (!Array.isArray(content.faqs)) {
      issues.push('FAQs must be an array');
      qualityScore -= 10;
    } else {
      content.faqs.forEach((faq: any, index: number) => {
        if (!faq.question || !faq.answer) {
          issues.push(`FAQ ${index + 1} missing question or answer`);
          qualityScore -= 5;
        }
        if (!faq.id) {
          warnings.push(`FAQ ${index + 1} missing ID`);
          qualityScore -= 2;
        }
      });
    }
  }

  // SEO validation
  if (content.meta_title && content.meta_title.length > 60) {
    warnings.push(`Meta title too long (${content.meta_title.length} chars, maximum 60)`);
    qualityScore -= 5;
  }

  if (content.meta_description) {
    if (content.meta_description.length < 150) {
      warnings.push(`Meta description too short (${content.meta_description.length} chars, minimum 150)`);
      qualityScore -= 5;
    } else if (content.meta_description.length > 160) {
      warnings.push(`Meta description too long (${content.meta_description.length} chars, maximum 160)`);
      qualityScore -= 5;
    }
  }

  // Category confidence validation
  if (content.category_confidence !== undefined) {
    if (content.category_confidence < 0.75) {
      warnings.push(`Low category confidence score: ${content.category_confidence}`);
      qualityScore -= 5;
    }
  }

  // Hashtags validation
  if (content.hashtags) {
    if (!Array.isArray(content.hashtags)) {
      issues.push('Hashtags must be an array');
      qualityScore -= 5;
    } else if (content.hashtags.length < 5) {
      warnings.push(`Too few hashtags (${content.hashtags.length}, minimum 5)`);
      qualityScore -= 3;
    } else if (content.hashtags.length > 10) {
      warnings.push(`Too many hashtags (${content.hashtags.length}, maximum 10)`);
      qualityScore -= 3;
    }
  }

  // Bonus points for optional fields
  if (content.releases && Array.isArray(content.releases) && content.releases.length > 0) {
    qualityScore += 5;
  }

  if (content.social_links && Object.keys(content.social_links).length > 0) {
    qualityScore += 5;
  }

  if (content.haiku && content.haiku.lines && content.haiku.lines.length === 3) {
    qualityScore += 5;
  }

  // Ensure score is within bounds
  qualityScore = Math.max(0, Math.min(100, qualityScore));

  return {
    isValid: issues.length === 0,
    issues,
    warnings,
    qualityScore,
    validatedAt: new Date().toISOString(),
    methodology: 'ai_dude',
    fieldCounts: {
      requiredFields: requiredFields.length,
      presentFields: requiredFields.filter(field => content[field]).length,
      totalIssues: issues.length,
      totalWarnings: warnings.length
    }
  };
}

/**
 * Store validation results in database
 */
async function storeValidationResults(toolId: string, validationResult: any): Promise<void> {
  try {
    const { error } = await supabase
      .from('tools')
      .update({
        validation_results: validationResult,
        last_validated_at: new Date().toISOString()
      })
      .eq('id', toolId);

    if (error) {
      console.error('Failed to store validation results:', error);
    }
  } catch (error) {
    console.error('Error storing validation results:', error);
  }
}
