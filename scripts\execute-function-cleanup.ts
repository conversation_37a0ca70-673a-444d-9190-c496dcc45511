#!/usr/bin/env tsx

/**
 * Execute Function Cleanup Script
 * 
 * This script executes the cleanup-duplicate-functions.sql to remove
 * old TEXT parameter versions of bulk processing functions and ensure
 * only UUID versions remain.
 */

import { config } from 'dotenv';
import { readFileSync } from 'fs';
import { join } from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

// Also try .env as fallback
if (!process.env.SUPABASE_URL) {
  config({ path: '.env' });
}

const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeFunctionCleanup() {
  console.log('🧹 Executing Function Cleanup...');
  console.log('=' .repeat(60));

  try {
    // Read the cleanup SQL file
    const cleanupPath = join(process.cwd(), 'scripts/cleanup-duplicate-functions.sql');
    const cleanupSQL = readFileSync(cleanupPath, 'utf8');

    console.log('📄 Loaded cleanup script: cleanup-duplicate-functions.sql');
    console.log('');

    // Show what this cleanup will do
    console.log('📋 This cleanup will:');
    console.log('   🗑️  Remove old TEXT parameter versions of bulk processing functions');
    console.log('   ✅ Keep only UUID parameter versions');
    console.log('   🔍 Verify function signatures are correct');
    console.log('   🧪 Test function calls with UUID parameters');
    console.log('');

    // Display the SQL for manual execution
    console.log('⚠️  MANUAL SQL EXECUTION REQUIRED');
    console.log('Please copy and paste the following SQL into your Supabase SQL Editor:');
    console.log('');
    console.log('--- COPY AND PASTE THE FOLLOWING SQL ---');
    console.log(cleanupSQL);
    console.log('--- END OF SQL ---');
    console.log('');

    // Wait for user confirmation
    console.log('After executing the SQL in Supabase, press Enter to continue...');
    await new Promise(resolve => {
      process.stdin.once('data', () => resolve(undefined));
    });

    // Verify the cleanup was applied
    console.log('🔍 Verifying function cleanup...');
    
    // Check function signatures
    const { data: functionParams, error: paramsError } = await supabase
      .from('information_schema.parameters')
      .select('specific_name, parameter_name, data_type, ordinal_position')
      .eq('specific_schema', 'public')
      .like('specific_name', '%bulk%')
      .eq('parameter_name', 'p_job_id')
      .order('specific_name')
      .order('ordinal_position');

    if (paramsError) {
      console.log('⚠️  Could not verify function parameters:', paramsError.message);
    } else if (functionParams) {
      console.log('✅ Function parameter verification:');
      functionParams.forEach(param => {
        const status = param.data_type === 'uuid' ? '✅' : '❌';
        console.log(`   ${status} ${param.specific_name}.${param.parameter_name}: ${param.data_type}`);
      });
      
      // Check if all are UUID type
      const allUuid = functionParams.every(param => param.data_type === 'uuid');
      if (allUuid) {
        console.log('🎉 All bulk processing functions now use UUID parameters!');
      } else {
        console.log('⚠️  Some functions still have non-UUID parameters');
      }
    }

    // Test function calls
    console.log('\n🧪 Testing function calls...');
    
    const testUuid = '00000000-0000-0000-0000-000000000000';
    
    try {
      const { data: testResult, error: testError } = await supabase.rpc('update_bulk_job_status_atomic', {
        p_job_id: testUuid,
        p_new_status: 'processing'
      });

      if (testError) {
        console.log('❌ Function test failed:', testError.message);
      } else if (testResult && testResult.error === 'job_not_found') {
        console.log('✅ Function test successful (expected job_not_found)');
      } else {
        console.log('⚠️  Unexpected function test result:', testResult);
      }
    } catch (testError) {
      console.log('❌ Function test error:', testError);
    }

    console.log('');
    console.log('🎉 Function cleanup completed!');
    console.log('');
    console.log('✅ What was accomplished:');
    console.log('   • Removed old TEXT parameter function versions');
    console.log('   • Verified UUID parameter functions are working');
    console.log('   • Tested function calls with UUID parameters');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('   • Test bulk processing job creation');
    console.log('   • Verify no more version_mismatch errors');
    console.log('   • Run end-to-end bulk processing test');

  } catch (error) {
    console.error('❌ Function cleanup failed:', error);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('   1. Ensure you have executed the cleanup SQL in Supabase SQL Editor');
    console.log('   2. Check that all DROP FUNCTION statements executed successfully');
    console.log('   3. Verify function signatures show UUID parameter types');
    console.log('   4. Check Supabase logs for detailed error information');
    process.exit(1);
  }
}

// Run the cleanup
if (require.main === module) {
  executeFunctionCleanup().catch(console.error);
}

export { executeFunctionCleanup };
