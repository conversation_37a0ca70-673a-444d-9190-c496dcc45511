import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tool<PERSON>ub<PERSON>JobD<PERSON>, JobType } from '../types';
import { JobManager } from '../job-manager';
import { createClient } from '@supabase/supabase-js';

export class ToolSubmissionHandler implements JobHandler {
  private _supabase: any = null;
  private jobManager = new JobManager();

  private get supabase() {
    if (!this._supabase) {
      this._supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );
    }
    return this._supabase;
  }

  async handle(job: Job): Promise<any> {
    const data = job.data as ToolSubmissionJobData;

    try {
      // Check if this is a bulk processing job with existing tool ID
      const existingToolId = (data as any).toolId;

      if (existingToolId) {
        // Bulk processing workflow: tool already exists, just process it
        console.log(`🔄 Processing existing tool ${existingToolId} for ${data.url}`);

        // Extract AI provider from processing options
        const processingOptions = (data as any).processingOptions;
        const aiProvider = processingOptions?.aiProvider;

        // SEQUENTIAL PIPELINE EXECUTION (enforced by job queue dependency management)
        // Stage 1: Web Scraping - Must complete first to gather raw data
        const scrapingResult = await this.scrapeWebsite(data.url, existingToolId);

        // Stage 2: AI Content Generation - Depends on scraped content and includes media collection
        const contentResult = await this.generateContent(data.url, scrapingResult, existingToolId, aiProvider);

        // Stage 3: Database Update - Final stage to persist all generated content
        const toolResult = await this.updateToolWithContent(existingToolId, data, contentResult);

        return {
          success: true,
          toolId: existingToolId,
          message: 'Tool processing completed successfully',
        };
      } else {
        // Original workflow: create new tool
        console.log(`🆕 Creating new tool for ${data.url}`);

        // SEQUENTIAL PIPELINE EXECUTION (enforced by job queue dependency management)
        // Stage 1: Web Scraping - Must complete first to gather raw data
        const scrapingResult = await this.scrapeWebsite(data.url);

        // Stage 2: AI Content Generation - Depends on scraped content and includes media collection
        const contentResult = await this.generateContent(data.url, scrapingResult);

        // Stage 3: Database Creation - Final stage to create tool with all generated content
        const toolResult = await this.createToolDraft(data, contentResult);

        // Step 4: Send notification email
        await this.sendNotificationEmail(data, toolResult);

        return {
          success: true,
          toolId: toolResult.id,
          message: 'Tool submission processed successfully',
        };
      }
    } catch (error) {
      console.error('Tool submission processing failed:', error);

      // Send error notification
      await this.sendErrorNotification(data, error);

      throw error;
    }
  }

  private async scrapeWebsite(url: string, toolId?: string): Promise<any> {
    const scrapingJob = await this.jobManager.createJob(JobType.WEB_SCRAPING, {
      url,
      toolId, // Pass tool ID to web scraping job
      options: {
        timeout: 70000, // Use updated timeout
        extractImages: true,
        extractLinks: true,
      },
    });

    // Wait for scraping job to complete
    return new Promise((resolve, reject) => {
      const checkJob = async () => {
        const job = await this.jobManager.getJob(scrapingJob.id);
        if (!job) {
          reject(new Error('Scraping job not found'));
          return;
        }

        if (job.status === 'completed') {
          resolve(job.result);
        } else if (job.status === 'failed') {
          reject(new Error(job.error || 'Scraping failed'));
        } else {
          setTimeout(checkJob, 2000); // Check every 2 seconds
        }
      };

      checkJob();
    });
  }

  private async generateContent(url: string, scrapedData: any, toolId?: string, aiProvider?: string): Promise<any> {
    const contentJob = await this.jobManager.createJob(JobType.CONTENT_GENERATION, {
      url,
      scrapedData,
      toolId, // Pass tool ID to content generation job
      aiProvider, // Pass user's AI provider choice
    });

    // Wait for content generation job to complete
    return new Promise((resolve, reject) => {
      const checkJob = async () => {
        const job = await this.jobManager.getJob(contentJob.id);
        if (!job) {
          reject(new Error('Content generation job not found'));
          return;
        }

        if (job.status === 'completed') {
          resolve(job.result);
        } else if (job.status === 'failed') {
          reject(new Error(job.error || 'Content generation failed'));
        } else {
          setTimeout(checkJob, 2000); // Check every 2 seconds
        }
      };

      checkJob();
    });
  }

  private async createToolDraft(
    submissionData: ToolSubmissionJobData,
    contentData: any
  ): Promise<any> {
    const { data, error } = await this.supabase
      .from('tools')
      .insert({
        name: submissionData.name,
        url: submissionData.url,
        description: contentData.description || submissionData.description,
        content_status: 'draft',
        generated_content: contentData,
        submitter_email: submissionData.submitterEmail,
        submitter_name: submissionData.submitterName,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create tool draft: ${error.message}`);
    }

    return data;
  }

  /**
   * Update existing tool with generated content (for bulk processing)
   */
  private async updateToolWithContent(
    toolId: string,
    submissionData: ToolSubmissionJobData,
    contentData: any
  ): Promise<any> {
    // Enhanced bulk processing detection with retry logic and race condition handling
    console.log(`🔍 Tool submission: Checking bulk processing status for tool: ${toolId}`);

    let isBulkProcessing = false;
    const maxRetries = 5; // Increased for better race condition handling

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Add progressive delay on first attempt to allow for database consistency
        // This helps with race conditions where tool was just created
        if (attempt === 1) {
          await new Promise(resolve => setTimeout(resolve, 300)); // Increased to 300ms initial delay
        }

        const { data: toolInfoArray, error: toolError } = await this.supabase
          .from('tools')
          .select('submission_source, name, id')
          .eq('id', toolId);

        if (toolError) {
          console.warn(`❌ Tool submission: Database error on attempt ${attempt}/${maxRetries}:`, toolError);
          if (attempt === maxRetries) {
            throw new Error(`Failed to query tool after ${maxRetries} attempts: ${toolError.message}`);
          }
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          continue;
        }

        if (!toolInfoArray || toolInfoArray.length === 0) {
          // For "not found" errors, always retry - this is likely a race condition
          if (attempt < maxRetries) {
            console.warn(`🔄 Tool submission: Tool ${toolId} not found on attempt ${attempt}, retrying (likely race condition)`);
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            continue;
          }
          throw new Error(`Tool ${toolId} not found in database during submission processing after ${maxRetries} attempts`);
        }

        if (toolInfoArray.length > 1) {
          console.warn(`⚠️ Tool submission: Multiple tools found with ID ${toolId} (${toolInfoArray.length} rows)`);
          console.warn(`Using first tool for submission source detection`);
        }

        const toolInfo = toolInfoArray[0];
        isBulkProcessing = toolInfo.submission_source === 'bulk_processing';

        console.log(`📋 Tool submission: ${toolInfo.name} has submission_source: ${toolInfo.submission_source}`);
        console.log(`🎯 Tool submission: Bulk processing detection result: ${isBulkProcessing ? 'YES - will publish directly' : 'NO - will keep as draft'}`);

        break; // Success, exit retry loop
      } catch (error) {
        console.warn(`💥 Tool submission: Exception on attempt ${attempt}/${maxRetries}:`, error);
        if (attempt === maxRetries) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }



    const updateData: any = {
      description: contentData.description || submissionData.description,
      generated_content: contentData,
      ai_generation_status: 'completed',
      updated_at: new Date().toISOString(),
    };

    if (isBulkProcessing) {
      // Bulk processing: publish directly
      updateData.content_status = 'published';
      updateData.published_at = new Date().toISOString();
      console.log(`🚀 Bulk processing tool ${toolId} - publishing directly`);
    } else {
      // User submission: keep as draft for review
      updateData.content_status = 'draft';
    }

    const { data, error } = await this.supabase
      .from('tools')
      .update(updateData)
      .eq('id', toolId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update tool ${toolId}: ${error.message}`);
    }

    console.log(`✅ Updated tool ${toolId} with generated content (${isBulkProcessing ? 'published' : 'draft'})`);
    return data;
  }

  private async sendNotificationEmail(
    submissionData: ToolSubmissionJobData,
    toolResult: any
  ): Promise<void> {
    await this.jobManager.createJob(JobType.EMAIL_NOTIFICATION, {
      to: submissionData.submitterEmail,
      subject: 'Tool Submission Received - AI Dude Directory',
      template: 'tool-submission-received',
      data: {
        toolName: submissionData.name,
        toolUrl: submissionData.url,
        submitterName: submissionData.submitterName,
        toolId: toolResult.id,
      },
    });

    // Also notify admin
    if (process.env.ADMIN_EMAIL) {
      await this.jobManager.createJob(JobType.EMAIL_NOTIFICATION, {
        to: process.env.ADMIN_EMAIL,
        subject: 'New Tool Submission - AI Dude Directory',
        template: 'admin-tool-submission',
        data: {
          toolName: submissionData.name,
          toolUrl: submissionData.url,
          submitterEmail: submissionData.submitterEmail,
          submitterName: submissionData.submitterName,
          toolId: toolResult.id,
        },
      });
    }
  }

  private async sendErrorNotification(
    submissionData: ToolSubmissionJobData,
    error: any
  ): Promise<void> {
    if (!process.env.ADMIN_EMAIL) return;

    await this.jobManager.createJob(JobType.EMAIL_NOTIFICATION, {
      to: process.env.ADMIN_EMAIL,
      subject: 'Tool Submission Processing Failed - AI Dude Directory',
      template: 'admin-processing-error',
      data: {
        toolName: submissionData.name,
        toolUrl: submissionData.url,
        submitterEmail: submissionData.submitterEmail,
        error: error.message || String(error),
      },
    });
  }
}
