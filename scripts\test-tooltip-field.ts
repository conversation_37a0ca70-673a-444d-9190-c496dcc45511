#!/usr/bin/env tsx

/**
 * Test Tooltip Field
 * 
 * This script tests that the tooltip field was added successfully
 * and can be updated in the tools table.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function testTooltipField() {
  console.log('🧪 Testing Tooltip Field...\n');

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // 1. Test if tooltip field exists by querying it
    console.log('1. 🔍 Testing tooltip field exists...');
    const { data: testData, error: testError } = await supabase
      .from('tools')
      .select('id, name, tooltip')
      .limit(1);

    if (testError) {
      if (testError.message.includes('tooltip')) {
        console.log('❌ Tooltip field does not exist yet');
        console.log('   Please run: npx supabase db push');
        return;
      } else {
        throw new Error(`Query failed: ${testError.message}`);
      }
    }

    console.log('✅ Tooltip field exists in database');

    // 2. Test updating a tool with tooltip
    console.log('\n2. 🔧 Testing tooltip field update...');
    
    // Find a FoodiePrep tool to test with
    const { data: foodieTools, error: foodieError } = await supabase
      .from('tools')
      .select('id, name, tooltip')
      .or('name.ilike.%foodieprep%,website.ilike.%foodieprep%')
      .limit(1);

    if (foodieError) {
      throw new Error(`Failed to find test tool: ${foodieError.message}`);
    }

    if (!foodieTools || foodieTools.length === 0) {
      console.log('⚠️ No FoodiePrep tools found for testing');
      return;
    }

    const testTool = foodieTools[0];
    console.log(`   Testing with tool: ${testTool.name} (${testTool.id})`);
    console.log(`   Current tooltip: ${testTool.tooltip || 'null'}`);

    // Update with a test tooltip
    const testTooltip = 'AI-powered recipes and meal planning made personal.';
    const { error: updateError } = await supabase
      .from('tools')
      .update({
        tooltip: testTooltip,
        updated_at: new Date().toISOString()
      })
      .eq('id', testTool.id);

    if (updateError) {
      throw new Error(`Failed to update tooltip: ${updateError.message}`);
    }

    console.log(`   ✅ Successfully updated tooltip to: "${testTooltip}"`);

    // 3. Verify the update worked
    console.log('\n3. ✅ Verifying tooltip update...');
    const { data: verifyData, error: verifyError } = await supabase
      .from('tools')
      .select('id, name, tooltip')
      .eq('id', testTool.id)
      .single();

    if (verifyError) {
      throw new Error(`Failed to verify update: ${verifyError.message}`);
    }

    if (verifyData.tooltip === testTooltip) {
      console.log('✅ Tooltip field update verified successfully');
    } else {
      console.log(`❌ Tooltip mismatch: expected "${testTooltip}", got "${verifyData.tooltip}"`);
    }

    console.log('\n🎯 Summary:');
    console.log('   ✅ Tooltip field exists in database');
    console.log('   ✅ Tooltip field can be updated');
    console.log('   ✅ Content parsing should now work without errors');
    console.log('');
    console.log('🚀 Next Steps:');
    console.log('   1. Process www.foodieprep.ai through bulk processing again');
    console.log('   2. The tooltip field should now be populated from AI generation');
    console.log('   3. No more "Could not find the \'tooltip\' column" errors');

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
testTooltipField().catch(console.error);
