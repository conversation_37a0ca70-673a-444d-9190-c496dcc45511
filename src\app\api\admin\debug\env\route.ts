import { NextRequest, NextResponse } from 'next/server';

/**
 * Debug endpoint to check environment variables
 * Only works in development or with proper admin key
 */
export async function GET(request: NextRequest) {
  // Only allow in development or with admin key
  const isDevelopment = process.env.NODE_ENV === 'development';
  const adminKey = request.headers.get('x-admin-api-key');
  const expectedKey = process.env.ADMIN_API_KEY;
  
  if (!isDevelopment && adminKey !== expectedKey) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const envVars = {
    // Environment info
    NODE_ENV: process.env.NODE_ENV,
    VERCEL_ENV: process.env.VERCEL_ENV,
    VERCEL_URL: process.env.VERCEL_URL,
    
    // Admin API keys (masked for security)
    ADMIN_API_KEY: process.env.ADMIN_API_KEY ? 
      `${process.env.ADMIN_API_KEY.substring(0, 10)}...` : 'NOT_SET',
    NEXT_PUBLIC_ADMIN_API_KEY: process.env.NEXT_PUBLIC_ADMIN_API_KEY ? 
      `${process.env.NEXT_PUBLIC_ADMIN_API_KEY.substring(0, 10)}...` : 'NOT_SET',
    
    // Feature flags
    JOB_QUEUE_ENABLED: process.env.JOB_QUEUE_ENABLED,
    ENABLE_BULK_PROCESSING: process.env.ENABLE_BULK_PROCESSING,
    ENABLE_ADMIN_PANEL: process.env.ENABLE_ADMIN_PANEL,
    CONTENT_GENERATION_ENABLED: process.env.CONTENT_GENERATION_ENABLED,
    
    // Database
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT_SET',
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'SET' : 'NOT_SET',
    
    // AI providers
    OPENAI_API_KEY: process.env.OPENAI_API_KEY ? 'SET' : 'NOT_SET',
    OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY ? 'SET' : 'NOT_SET',
    
    // Request headers (for debugging)
    requestHeaders: {
      'x-admin-api-key': adminKey ? `${adminKey.substring(0, 10)}...` : 'NOT_PROVIDED',
      'user-agent': request.headers.get('user-agent'),
      'host': request.headers.get('host'),
    }
  };

  return NextResponse.json({
    success: true,
    environment: envVars,
    timestamp: new Date().toISOString()
  });
}
