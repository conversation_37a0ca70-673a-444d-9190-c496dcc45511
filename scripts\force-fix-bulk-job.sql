-- Force Fix Bulk Job with Explicit Timestamps
-- This script aggressively fixes the stuck bulk job with explicit timestamp values

-- 1. Force update the specific bulk processing job with explicit timestamps
UPDATE bulk_processing_jobs 
SET 
  status = 'completed',
  total_items = 1,
  processed_items = 1,
  successful_items = 1,
  failed_items = 0,
  started_at = '2025-06-22 19:02:57.394+00'::timestamptz,
  completed_at = '2025-06-22 19:05:00.000+00'::timestamptz,
  updated_at = NOW(),
  version = 4
WHERE id = 'dbc98f0d-9823-4bfe-90a2-97411ebb989f';

-- 2. Verify the update worked
SELECT 
  'AFTER UPDATE' as status,
  id,
  status,
  total_items,
  processed_items,
  successful_items,
  failed_items,
  created_at,
  started_at,
  completed_at,
  updated_at,
  version,
  -- Calculate duration in seconds
  CASE 
    WHEN started_at IS NOT NULL AND completed_at IS NOT NULL THEN
      EXTRACT(EPOCH FROM (completed_at - started_at))::integer
    ELSE NULL
  END as duration_seconds
FROM bulk_processing_jobs 
WHERE id = 'dbc98f0d-9823-4bfe-90a2-97411ebb989f';

-- 3. Test what the frontend formatDate function should receive
SELECT 
  'FRONTEND TEST' as test_type,
  created_at as raw_created_at,
  created_at::text as created_at_string,
  started_at as raw_started_at,
  started_at::text as started_at_string,
  completed_at as raw_completed_at,
  completed_at::text as completed_at_string
FROM bulk_processing_jobs 
WHERE id = 'dbc98f0d-9823-4bfe-90a2-97411ebb989f';

-- 4. Create a test job with known good data for comparison
INSERT INTO bulk_processing_jobs (
  id,
  job_type,
  status,
  total_items,
  processed_items,
  successful_items,
  failed_items,
  source_data,
  processing_options,
  created_by,
  created_at,
  started_at,
  completed_at,
  updated_at,
  version
) VALUES (
  gen_random_uuid(),
  'manual_entry',
  'completed',
  1,
  1,
  1,
  0,
  '{"urls": ["https://test.com/"]}',
  '{"batchSize": 5}',
  'admin',
  NOW() - INTERVAL '10 minutes',
  NOW() - INTERVAL '9 minutes',
  NOW() - INTERVAL '5 minutes',
  NOW(),
  1
) ON CONFLICT (id) DO NOTHING;

-- 5. Show comparison between the fixed job and test job
SELECT 
  'COMPARISON' as section,
  id,
  status,
  created_at IS NOT NULL as has_created_at,
  started_at IS NOT NULL as has_started_at,
  completed_at IS NOT NULL as has_completed_at,
  CASE 
    WHEN started_at IS NOT NULL AND completed_at IS NOT NULL THEN
      EXTRACT(EPOCH FROM (completed_at - started_at))::integer || ' seconds'
    ELSE 'No duration'
  END as calculated_duration
FROM bulk_processing_jobs 
WHERE status = 'completed'
ORDER BY created_at DESC
LIMIT 3;
