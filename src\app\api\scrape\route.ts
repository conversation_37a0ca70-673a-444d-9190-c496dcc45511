import { NextRequest, NextResponse } from 'next/server';
import { contentProcessor } from '@/lib/scraping/content-processor';
import { EnhancedScrapeRequest } from '@/lib/scraping/types';

// Legacy interface for backward compatibility
interface LegacyScrapeOptions {
  waitForSelector?: string;
  timeout?: number;
  captureScreenshot?: boolean;
  extractImages?: boolean;
  extractFavicon?: boolean;
  scrapePricingPage?: boolean;
  scrapeFAQPage?: boolean;
}

// Helper functions for legacy compatibility
function extractTitleFromMarkdown(content: string): string {
  const titleMatch = content.match(/^#\s+(.+)$/m);
  return titleMatch ? titleMatch[1].trim() : '';
}

function extractDescriptionFromMarkdown(content: string): string {
  // Look for the first paragraph after a heading
  const lines = content.split('\n');
  let foundHeading = false;

  for (const line of lines) {
    if (line.startsWith('#')) {
      foundHeading = true;
      continue;
    }
    if (foundHeading && line.trim() && !line.startsWith('#')) {
      return line.trim().substring(0, 200); // Limit description length
    }
  }

  return '';
}

interface PageWithMetadata {
  content: string;
  metadata?: {
    pageType?: string;
    [key: string]: unknown;
  };
}

function extractSectionFromPages(pages: PageWithMetadata[] | undefined, sectionType: string): string {
  if (!pages) return '';

  const sectionPage = pages.find(page => page.metadata?.pageType === sectionType);
  return sectionPage ? sectionPage.content.substring(0, 1000) : '';
}

export async function POST(request: NextRequest) {
  try {
    const { url, options = {} }: { url: string; options: LegacyScrapeOptions } = await request.json();

    if (!url || !url.startsWith('http')) {
      return NextResponse.json(
        { success: false, error: 'Invalid URL provided' },
        { status: 400 }
      );
    }

    console.log(`🕷️ Enhanced scraping request for: ${url}`);

    // Convert legacy options to enhanced scrape request
    const enhancedRequest: EnhancedScrapeRequest = {
      url,
      options: {
        waitForSelector: options.waitForSelector,
        timeout: options.timeout,
        captureScreenshot: options.captureScreenshot,
        outputFormat: 'markdown',
        enableJSRendering: false, // Start with basic scraping for cost optimization
        blockResources: true
      },
      costOptimization: true, // Enable cost optimization by default
      mediaCollection: options.extractImages || options.extractFavicon || options.captureScreenshot,
      multiPageConfig: {
        enabled: Boolean(options.scrapePricingPage || options.scrapeFAQPage),
        mode: 'conditional',
        maxPagesPerTool: 2,
        creditThreshold: 50,
        pageTypes: {
          pricing: { enabled: !!options.scrapePricingPage, priority: 'high', patterns: ['/pricing', '/plans'], selectors: ['.pricing'], required: false },
          faq: { enabled: !!options.scrapeFAQPage, priority: 'medium', patterns: ['/faq', '/help'], selectors: ['.faq'], required: false },
          features: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false },
          about: { enabled: false, priority: 'low', patterns: [], selectors: [], required: false }
        },
        fallbackStrategy: { searchInMainPage: true, useNavigation: true, useSitemap: false }
      }
    };

    // Process enhanced scraping
    const result = await contentProcessor.processEnhancedScrape(enhancedRequest);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error || 'Scraping failed' },
        { status: 500 }
      );
    }

    // Convert enhanced result to legacy format for backward compatibility
    const legacyData = {
      title: extractTitleFromMarkdown(result.content),
      description: extractDescriptionFromMarkdown(result.content),
      textContent: result.content, // Keep full content for AI processing
      images: result.mediaAssets?.ogImages?.map(img => ({
        src: img.url,
        alt: '',
        width: img.metadata?.width || 0,
        height: img.metadata?.height || 0
      })) || [],
      favicon: result.mediaAssets?.favicon?.[0] || null,
      pricingText: extractSectionFromPages(result.additionalPages, 'pricing'),
      faqText: extractSectionFromPages(result.additionalPages, 'faq'),
      pricingLinks: [], // Legacy field - not needed with new system
      faqLinks: [], // Legacy field - not needed with new system
      screenshot: result.mediaAssets?.screenshot?.screenshot || null,
      metadata: {
        ogTitle: extractTitleFromMarkdown(result.content),
        ogDescription: extractDescriptionFromMarkdown(result.content),
        metaDescription: extractDescriptionFromMarkdown(result.content),
        url: result.url,
        // Enhanced metadata
        creditsUsed: result.costAnalysis?.creditsUsed || 0,
        optimizationStrategy: result.costAnalysis?.optimizationStrategy || 'unknown',
        qualityScore: result.metadata?.qualityScore || 0,
        contentAnalysis: result.contentAnalysis?.scenario || 'unknown'
      }
    };

    console.log(`✅ Enhanced scraping completed for: ${url} (${result.costAnalysis?.creditsUsed || 0} credits)`);

    return NextResponse.json({
      success: true,
      data: legacyData
    });

  } catch (error) {
    console.error('Scraping error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to scrape URL' },
      { status: 500 }
    );
  }
}
