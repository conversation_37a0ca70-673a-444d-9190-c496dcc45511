This file is a merged representation of the entire codebase, combined into a single document.
Generated by Repomix on: 2025-03-27T14:35:05.555Z

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded

Additional Info:
----------------

================================================================
Directory Structure
================================================================
categories.py
category-map-aitoptools.com.json
enhanced_ajax_crawler.py
fix_extractor.py
json-structure-aitoptools.com.json
requirements.txt
updated-readme.md
utils.py

================================================================
Files
================================================================

================
File: categories.py
================
# Define the category structure based on our exploration
CATEGORY_MAP = {
    "Business & Productivity": {
        "url": "https://aitoptools.com/ai-tools/business-productivity/",
        "subcategories": [
            {"name": "Sales", "url": "https://aitoptools.com/ai-tools/sales/"},
            {"name": "Email Marketing", "url": "https://aitoptools.com/ai-tools/email-marketing/"},
            {"name": "Lead Generation", "url": "https://aitoptools.com/ai-tools/lead-generation/"},
            {"name": "Business Name Generator", "url": "https://aitoptools.com/ai-tools/business-name-generator/"},
            {"name": "Ecommerce", "url": "https://aitoptools.com/ai-tools/ecommerce/"},
            {"name": "Investing", "url": "https://aitoptools.com/ai-tools/investing/"},
            {"name": "Real Estate", "url": "https://aitoptools.com/ai-tools/real-estate/"},
            {"name": "Assistant", "url": "https://aitoptools.com/ai-tools/assistant/"},
            {"name": "Finance", "url": "https://aitoptools.com/ai-tools/finance/"},
            {"name": "Productivity", "url": "https://aitoptools.com/ai-tools/productivity/"},
            {"name": "Human Resources", "url": "https://aitoptools.com/ai-tools/human-resources/"},
            {"name": "Analytics", "url": "https://aitoptools.com/ai-tools/analytics/"},
            {"name": "Email Finder/Verifier", "url": "https://aitoptools.com/ai-tools/email-finder-verifier/"},
            {"name": "Presentation Maker", "url": "https://aitoptools.com/ai-tools/presentation-maker/"},
            {"name": "Business Ideas", "url": "https://aitoptools.com/ai-tools/business-ideas/"},
            {"name": "Legal", "url": "https://aitoptools.com/ai-tools/legal/"},
            {"name": "Customer Service", "url": "https://aitoptools.com/ai-tools/customer-service/"},
            {"name": "Marketing", "url": "https://aitoptools.com/ai-tools/marketing/"}
        ]
    },
    "Tech & Development": {
        "url": "https://aitoptools.com/ai-tools/tech-development/",
        "subcategories": [
            {"name": "PDF Scraper", "url": "https://aitoptools.com/ai-tools/pdf-scraper/"},
            {"name": "File Management System", "url": "https://aitoptools.com/ai-tools/file-management-system/"},
            {"name": "Form Generator", "url": "https://aitoptools.com/ai-tools/form-generator/"},
            {"name": "Website Builder", "url": "https://aitoptools.com/ai-tools/website-builder/"},
            {"name": "Cybersecurity", "url": "https://aitoptools.com/ai-tools/cybersecurity/"},
            {"name": "Web Scraping", "url": "https://aitoptools.com/ai-tools/web-scraping/"},
            {"name": "Code Generator", "url": "https://aitoptools.com/ai-tools/code-generator/"},
            {"name": "Plugins", "url": "https://aitoptools.com/ai-tools/plugins/"},
            {"name": "Chatbot", "url": "https://aitoptools.com/ai-tools/chatbot/"},
            {"name": "Font Generator", "url": "https://aitoptools.com/ai-tools/font-generator/"},
            {"name": "Converter", "url": "https://aitoptools.com/ai-tools/converter/"},
            {"name": "Task Automation", "url": "https://aitoptools.com/ai-tools/task-automation/"},
            {"name": "Sorting Algorithms", "url": "https://aitoptools.com/ai-tools/sorting-algorithms/"},
            {"name": "Detector", "url": "https://aitoptools.com/ai-tools/detector/"},
            {"name": "Cloud-based Tool", "url": "https://aitoptools.com/ai-tools/cloud-based-tool/"},
            {"name": "Collaboration", "url": "https://aitoptools.com/ai-tools/collaboration/"},
            {"name": "Database", "url": "https://aitoptools.com/ai-tools/database/"},
            {"name": "AI Model", "url": "https://aitoptools.com/ai-tools/ai-model/"},
            {"name": "Data Extraction", "url": "https://aitoptools.com/ai-tools/data-extraction/"},
            {"name": "No Code", "url": "https://aitoptools.com/ai-tools/no-code/"},
            {"name": "Coding Assistant", "url": "https://aitoptools.com/ai-tools/coding-assistant/"},
            {"name": "Data Analytics", "url": "https://aitoptools.com/ai-tools/data-analytics/"},
            {"name": "Prompt Generator", "url": "https://aitoptools.com/ai-tools/prompt-generator/"},
            {"name": "Developer", "url": "https://aitoptools.com/ai-tools/developer/"},
            {"name": "SQL", "url": "https://aitoptools.com/ai-tools/sql/"}
        ]
    },
    "Design & Media": {
        "url": "https://aitoptools.com/ai-tools/design-media/",
        "subcategories": [
            {"name": "Video Generator", "url": "https://aitoptools.com/ai-tools/video-generator/"},
            {"name": "Video Editor", "url": "https://aitoptools.com/ai-tools/video-editor/"},
            {"name": "Voice Generator", "url": "https://aitoptools.com/ai-tools/voice-generator/"},
            {"name": "Art Generator", "url": "https://aitoptools.com/ai-tools/art-generator/"},
            {"name": "Audio Editor", "url": "https://aitoptools.com/ai-tools/audio-editor/"},
            {"name": "Music Generator", "url": "https://aitoptools.com/ai-tools/music-generator/"},
            {"name": "Icon Generator", "url": "https://aitoptools.com/ai-tools/icon-generator/"},
            {"name": "Video Enhancer", "url": "https://aitoptools.com/ai-tools/video-enhancer/"},
            {"name": "3D Model Generators", "url": "https://aitoptools.com/ai-tools/3d-model-generators/"},
            {"name": "Image Recognition", "url": "https://aitoptools.com/ai-tools/image-recognition/"},
            {"name": "Image Enhancer", "url": "https://aitoptools.com/ai-tools/image-enhancer/"},
            {"name": "Dubbing", "url": "https://aitoptools.com/ai-tools/dubbing/"},
            {"name": "Animation Generator", "url": "https://aitoptools.com/ai-tools/animation-generator/"},
            {"name": "Image Generator", "url": "https://aitoptools.com/ai-tools/image-generator/"},
            {"name": "Design Tool", "url": "https://aitoptools.com/ai-tools/design-tool/"},
            {"name": "Logo Generator", "url": "https://aitoptools.com/ai-tools/logo-generator/"}
        ]
    },
    "Lifestyle & Growth": {
        "url": "https://aitoptools.com/ai-tools/lifestyle-growth/",
        "subcategories": [
            {"name": "Research", "url": "https://aitoptools.com/ai-tools/research/"},
            {"name": "Recipe Generator", "url": "https://aitoptools.com/ai-tools/recipe-generator/"},
            {"name": "Gaming", "url": "https://aitoptools.com/ai-tools/gaming/"},
            {"name": "Fun", "url": "https://aitoptools.com/ai-tools/fun/"},
            {"name": "Travel", "url": "https://aitoptools.com/ai-tools/travel/"},
            {"name": "Gift Ideas Generator", "url": "https://aitoptools.com/ai-tools/gift-ideas-generator/"},
            {"name": "Healthcare", "url": "https://aitoptools.com/ai-tools/healthcare/"},
            {"name": "Quizzes", "url": "https://aitoptools.com/ai-tools/quizzes/"},
            {"name": "Entertainment", "url": "https://aitoptools.com/ai-tools/entertainment/"},
            {"name": "Education", "url": "https://aitoptools.com/ai-tools/education/"},
            {"name": "Sign Language Learning", "url": "https://aitoptools.com/ai-tools/sign-language-learning/"},
            {"name": "Companionship", "url": "https://aitoptools.com/ai-tools/companionship/"},
            {"name": "Communication", "url": "https://aitoptools.com/ai-tools/communication/"},
            {"name": "Dream Interpretation", "url": "https://aitoptools.com/ai-tools/dream-interpretation/"},
            {"name": "News", "url": "https://aitoptools.com/ai-tools/news/"},
            {"name": "Beauty", "url": "https://aitoptools.com/ai-tools/beauty/"},
            {"name": "Interior Design", "url": "https://aitoptools.com/ai-tools/interior-design/"}
        ]
    },
    "Writing & Content": {
        "url": "https://aitoptools.com/ai-tools/writing-content/",
        "subcategories": [
            {"name": "Writing Assistant", "url": "https://aitoptools.com/ai-tools/writing-assistant/"},
            {"name": "SEO Generator", "url": "https://aitoptools.com/ai-tools/seo-generator/"},
            {"name": "Email Writing", "url": "https://aitoptools.com/ai-tools/email-writing/"},
            {"name": "Text-To-Speech", "url": "https://aitoptools.com/ai-tools/text-to-speech/"},
            {"name": "Speech to Text", "url": "https://aitoptools.com/ai-tools/speech-to-text/"},
            {"name": "Summarizer", "url": "https://aitoptools.com/ai-tools/summarizer/"},
            {"name": "Content Generator", "url": "https://aitoptools.com/ai-tools/content-generator/"},
            {"name": "Content Creator", "url": "https://aitoptools.com/ai-tools/content-creator/"},
            {"name": "Social Media", "url": "https://aitoptools.com/ai-tools/social-media/"},
            {"name": "Translations", "url": "https://aitoptools.com/ai-tools/translations/"},
            {"name": "Story Generator", "url": "https://aitoptools.com/ai-tools/story-generator/"},
            {"name": "Transcription", "url": "https://aitoptools.com/ai-tools/transcription/"}
        ]
    }
}

================
File: category-map-aitoptools.com.json
================
{
  "categories": [
    {
      "name": "Business & Productivity",
      "url": "https://aitoptools.com/ai-tools/business-productivity/",
      "subcategories": [
        {"name": "Sales", "url": "https://aitoptools.com/ai-tools/sales/"},
        {"name": "Email Marketing", "url": "https://aitoptools.com/ai-tools/email-marketing/"},
        {"name": "Lead Generation", "url": "https://aitoptools.com/ai-tools/lead-generation/"},
        {"name": "Business Name Generator", "url": "https://aitoptools.com/ai-tools/business-name-generator/"},
        {"name": "Ecommerce", "url": "https://aitoptools.com/ai-tools/ecommerce/"},
        {"name": "Investing", "url": "https://aitoptools.com/ai-tools/investing/"},
        {"name": "Real Estate", "url": "https://aitoptools.com/ai-tools/real-estate/"},
        {"name": "Assistant", "url": "https://aitoptools.com/ai-tools/assistant/"},
        {"name": "Finance", "url": "https://aitoptools.com/ai-tools/finance/"},
        {"name": "Productivity", "url": "https://aitoptools.com/ai-tools/productivity/"},
        {"name": "Human Resources", "url": "https://aitoptools.com/ai-tools/human-resources/"},
        {"name": "Analytics", "url": "https://aitoptools.com/ai-tools/analytics/"},
        {"name": "Email Finder/Verifier", "url": "https://aitoptools.com/ai-tools/email-finder-verifier/"},
        {"name": "Presentation Maker", "url": "https://aitoptools.com/ai-tools/presentation-maker/"},
        {"name": "Business Ideas", "url": "https://aitoptools.com/ai-tools/business-ideas/"},
        {"name": "Legal", "url": "https://aitoptools.com/ai-tools/legal/"},
        {"name": "Customer Service", "url": "https://aitoptools.com/ai-tools/customer-service/"},
        {"name": "Marketing", "url": "https://aitoptools.com/ai-tools/marketing/"}
      ]
    },
    {
      "name": "Tech & Development",
      "url": "https://aitoptools.com/ai-tools/tech-development/",
      "subcategories": [
        {"name": "PDF Scraper", "url": "https://aitoptools.com/ai-tools/pdf-scraper/"},
        {"name": "File Management System", "url": "https://aitoptools.com/ai-tools/file-management-system/"},
        {"name": "Form Generator", "url": "https://aitoptools.com/ai-tools/form-generator/"},
        {"name": "Website Builder", "url": "https://aitoptools.com/ai-tools/website-builder/"},
        {"name": "Cybersecurity", "url": "https://aitoptools.com/ai-tools/cybersecurity/"},
        {"name": "Web Scraping", "url": "https://aitoptools.com/ai-tools/web-scraping/"},
        {"name": "Code Generator", "url": "https://aitoptools.com/ai-tools/code-generator/"},
        {"name": "Plugins", "url": "https://aitoptools.com/ai-tools/plugins/"},
        {"name": "Chatbot", "url": "https://aitoptools.com/ai-tools/chatbot/"},
        {"name": "Font Generator", "url": "https://aitoptools.com/ai-tools/font-generator/"},
        {"name": "Converter", "url": "https://aitoptools.com/ai-tools/converter/"},
        {"name": "Task Automation", "url": "https://aitoptools.com/ai-tools/task-automation/"},
        {"name": "Sorting Algorithms", "url": "https://aitoptools.com/ai-tools/sorting-algorithms/"},
        {"name": "Detector", "url": "https://aitoptools.com/ai-tools/detector/"},
        {"name": "Cloud-based Tool", "url": "https://aitoptools.com/ai-tools/cloud-based-tool/"},
        {"name": "Collaboration", "url": "https://aitoptools.com/ai-tools/collaboration/"},
        {"name": "Database", "url": "https://aitoptools.com/ai-tools/database/"},
        {"name": "AI Model", "url": "https://aitoptools.com/ai-tools/ai-model/"},
        {"name": "Data Extraction", "url": "https://aitoptools.com/ai-tools/data-extraction/"},
        {"name": "No Code", "url": "https://aitoptools.com/ai-tools/no-code/"},
        {"name": "Coding Assistant", "url": "https://aitoptools.com/ai-tools/coding-assistant/"},
        {"name": "Data Analytics", "url": "https://aitoptools.com/ai-tools/data-analytics/"},
        {"name": "Prompt Generator", "url": "https://aitoptools.com/ai-tools/prompt-generator/"},
        {"name": "Developer", "url": "https://aitoptools.com/ai-tools/developer/"},
        {"name": "SQL", "url": "https://aitoptools.com/ai-tools/sql/"}
      ]
    },
    {
      "name": "Design & Media",
      "url": "https://aitoptools.com/ai-tools/design-media/",
      "subcategories": [
        {"name": "Video Generator", "url": "https://aitoptools.com/ai-tools/video-generator/"},
        {"name": "Video Editor", "url": "https://aitoptools.com/ai-tools/video-editor/"},
        {"name": "Voice Generator", "url": "https://aitoptools.com/ai-tools/voice-generator/"},
        {"name": "Art Generator", "url": "https://aitoptools.com/ai-tools/art-generator/"},
        {"name": "Audio Editor", "url": "https://aitoptools.com/ai-tools/audio-editor/"},
        {"name": "Music Generator", "url": "https://aitoptools.com/ai-tools/music-generator/"},
        {"name": "Icon Generator", "url": "https://aitoptools.com/ai-tools/icon-generator/"},
        {"name": "Video Enhancer", "url": "https://aitoptools.com/ai-tools/video-enhancer/"},
        {"name": "3D Model Generators", "url": "https://aitoptools.com/ai-tools/3d-model-generators/"},
        {"name": "Image Recognition", "url": "https://aitoptools.com/ai-tools/image-recognition/"},
        {"name": "Image Enhancer", "url": "https://aitoptools.com/ai-tools/image-enhancer/"},
        {"name": "Dubbing", "url": "https://aitoptools.com/ai-tools/dubbing/"},
        {"name": "Animation Generator", "url": "https://aitoptools.com/ai-tools/animation-generator/"},
        {"name": "Image Generator", "url": "https://aitoptools.com/ai-tools/image-generator/"},
        {"name": "Design Tool", "url": "https://aitoptools.com/ai-tools/design-tool/"},
        {"name": "Logo Generator", "url": "https://aitoptools.com/ai-tools/logo-generator/"}
      ]
    },
    {
      "name": "Lifestyle & Growth",
      "url": "https://aitoptools.com/ai-tools/lifestyle-growth/",
      "subcategories": [
        {"name": "Research", "url": "https://aitoptools.com/ai-tools/research/"},
        {"name": "Recipe Generator", "url": "https://aitoptools.com/ai-tools/recipe-generator/"},
        {"name": "Gaming", "url": "https://aitoptools.com/ai-tools/gaming/"},
        {"name": "Fun", "url": "https://aitoptools.com/ai-tools/fun/"},
        {"name": "Travel", "url": "https://aitoptools.com/ai-tools/travel/"},
        {"name": "Gift Ideas Generator", "url": "https://aitoptools.com/ai-tools/gift-ideas-generator/"},
        {"name": "Healthcare", "url": "https://aitoptools.com/ai-tools/healthcare/"},
        {"name": "Quizzes", "url": "https://aitoptools.com/ai-tools/quizzes/"},
        {"name": "Entertainment", "url": "https://aitoptools.com/ai-tools/entertainment/"},
        {"name": "Education", "url": "https://aitoptools.com/ai-tools/education/"},
        {"name": "Sign Language Learning", "url": "https://aitoptools.com/ai-tools/sign-language-learning/"},
        {"name": "Companionship", "url": "https://aitoptools.com/ai-tools/companionship/"},
        {"name": "Communication", "url": "https://aitoptools.com/ai-tools/communication/"},
        {"name": "Dream Interpretation", "url": "https://aitoptools.com/ai-tools/dream-interpretation/"},
        {"name": "News", "url": "https://aitoptools.com/ai-tools/news/"},
        {"name": "Beauty", "url": "https://aitoptools.com/ai-tools/beauty/"},
        {"name": "Interior Design", "url": "https://aitoptools.com/ai-tools/interior-design/"}
      ]
    },
    {
      "name": "Writing & Content",
      "url": "https://aitoptools.com/ai-tools/writing-content/",
      "subcategories": [
        {"name": "Writing Assistant", "url": "https://aitoptools.com/ai-tools/writing-assistant/"},
        {"name": "SEO Generator", "url": "https://aitoptools.com/ai-tools/seo-generator/"},
        {"name": "Email Writing", "url": "https://aitoptools.com/ai-tools/email-writing/"},
        {"name": "Text-To-Speech", "url": "https://aitoptools.com/ai-tools/text-to-speech/"},
        {"name": "Speech to Text", "url": "https://aitoptools.com/ai-tools/speech-to-text/"},
        {"name": "Summarizer", "url": "https://aitoptools.com/ai-tools/summarizer/"},
        {"name": "Content Generator", "url": "https://aitoptools.com/ai-tools/content-generator/"},
        {"name": "Content Creator", "url": "https://aitoptools.com/ai-tools/content-creator/"},
        {"name": "Social Media", "url": "https://aitoptools.com/ai-tools/social-media/"},
        {"name": "Translations", "url": "https://aitoptools.com/ai-tools/translations/"},
        {"name": "Story Generator", "url": "https://aitoptools.com/ai-tools/story-generator/"},
        {"name": "Transcription", "url": "https://aitoptools.com/ai-tools/transcription/"}
      ]
    }
  ]
}

================
File: enhanced_ajax_crawler.py
================
import asyncio
import aiohttp
import re
import json
import time
import logging
import sys
import os
import traceback
from bs4 import BeautifulSoup
from typing import List, Dict, Any, Set, Tuple, Optional
from urllib.parse import urlparse, parse_qs

# Import the fixed extractor function
from fix_extractor import extract_tool_data

# Local imports
from categories import CATEGORY_MAP
from utils import load_checkpoint, save_checkpoint, save_to_json, save_to_csv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("enhanced_crawler.log", encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("enhanced_crawler")

class EnhancedCrawler:
    """
    Enhanced crawler with improved methods for handling the "load more" functionality
    """
    
    def __init__(self, max_concurrent_tools=5):
        self.max_concurrent_tools = max_concurrent_tools
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'X-Requested-With': 'XMLHttpRequest',  # Important for AJAX requests
            'Referer': 'https://aitoptools.com/'  # Adding a referer to make request more legitimate
        }
        
        # Load checkpoint data
        checkpoint_data = load_checkpoint()
        self.all_tools_data = checkpoint_data['tools_data']
        self.processed_urls = checkpoint_data['processed_urls']
        self.failed_urls = checkpoint_data.get('failed_urls', {})
        
        logger.info(f"Initialized enhanced crawler with {len(self.all_tools_data)} tools in checkpoint")
        logger.info(f"Using concurrency of {max_concurrent_tools}")
    
    def _extract_category_id(self, html, url):
        """
        Extract category ID with specific focus on term ID in body class and JavaScript patterns
        
        Prioritizes finding the ID in:
        1. The body class attribute (term-detector term-6824)
        2. JavaScript fetch calls using the ID (fetch('ajaxtoolcat/'+6824+)
        """
        logger.info(f"Attempting to extract category ID from URL: {url}")
        
        # Method 1: Look for term ID in body class (highest priority)
        body_match = re.search(r'class="[^"]*term-[a-z-]+ term-(\d+)', html)
        if body_match:
            category_id = body_match.group(1)
            logger.info(f"Found category ID in body class: {category_id}")
            return category_id
        
        # Method 2: Look for IDs in JavaScript fetch calls
        js_patterns = [
            r'fetch\([\'"][^\'"]*/\'\+(\d+)',  # fetch('ajaxtoolcat/'+6824+
            r'[\'"][^\'"]*/\'\+(\d+)',         # 'ajaxtoolcat/'+6824+
        ]
        
        for pattern in js_patterns:
            js_matches = re.findall(pattern, html)
            if js_matches:
                # Find the most common ID (likely the correct one)
                id_counts = {}
                for id_val in js_matches:
                    id_counts[id_val] = id_counts.get(id_val, 0) + 1
                
                most_common_id = max(id_counts.keys(), key=lambda k: id_counts[k])
                logger.info(f"Found category ID in JavaScript: {most_common_id}")
                return most_common_id
        
        # Method 3: Direct search for loadmorecategory function
        if 'function loadmorecategory()' in html:
            loadmore_match = re.search(r'function\s+loadmorecategory\s*\(\)\s*\{(.*?)\}', html, re.DOTALL)
            if loadmore_match:
                function_content = loadmore_match.group(1)
                # Look for 4-digit numbers that might be category IDs
                id_matches = re.findall(r'\b(\d{4})\b', function_content)
                if id_matches:
                    # Take the most frequent 4-digit number
                    id_counts = {}
                    for id_val in id_matches:
                        id_counts[id_val] = id_counts.get(id_val, 0) + 1
                    
                    most_common_id = max(id_counts.keys(), key=lambda k: id_counts[k])
                    logger.info(f"Found category ID in loadmorecategory function: {most_common_id}")
                    return most_common_id
        
        logger.warning("Could not find category ID with reliable methods")
        return None
    
    def _extract_tools_from_html(self, html):
        """Extract tool URLs from HTML content"""
        tool_urls = set()
        matches = re.findall(r'href=[\'"]https://aitoptools.com/tool/([^\'"]+)[\'"]', html)
        for match in matches:
            tool_urls.add(f"https://aitoptools.com/tool/{match}")
        
        return list(tool_urls)
        
    async def _try_different_ajax_methods(self, session, url, html, visible_tools_count):
        """Try different AJAX methods to load more tools"""
        # Combined tools from all methods
        all_tools = set()
        
        # First try to extract category ID
        category_id = self._extract_category_id(html, url)
        
        if category_id:
            logger.info(f"Found category ID: {category_id}")
            try:
                # Add initial visible tools
                initial_tools = self._extract_tools_from_html(html)
                for tool in initial_tools:
                    all_tools.add(tool)
                
                # Try direct AJAX URL method first (based on user's console output)
                tools_from_direct_ajax = await self._try_direct_ajax_url(session, url, category_id, visible_tools_count)
                
                if tools_from_direct_ajax:
                    for tool in tools_from_direct_ajax:
                        all_tools.add(tool)
                    
                # Still try standard WordPress AJAX as a fallback
                tools_from_ajax = await self._try_standard_wordpress_ajax(session, category_id, visible_tools_count)
                
                if tools_from_ajax:
                    for tool in tools_from_ajax:
                        all_tools.add(tool)
                    
            except Exception as e:
                logger.error(f"Error in AJAX methods: {e}")
        else:
            logger.warning("No category ID found, skipping AJAX methods")
        
        # Try direct pagination as another method
        try:
            tools_from_pagination = await self._try_direct_page_urls(session, url, visible_tools_count)
            if tools_from_pagination:
                for tool in tools_from_pagination:
                    all_tools.add(tool)
        except Exception as e:
            logger.error(f"Error in direct pagination method: {e}")
        
        logger.info(f"Combined all methods, found {len(all_tools)} tools total")
        return list(all_tools)
    
    async def _try_direct_ajax_url(self, session, base_url, category_id, initial_count):
        """
        Try the direct AJAX URL method that was discovered by the user
        Format: https://aitoptools.com/ai-tools/summarizer/ajaxtoolcat/6807/2/
        """
        logger.info("Trying direct AJAX URL method based on user's findings")
        
        # Parse the base URL to extract the category slug
        parsed_url = urlparse(base_url)
        path_parts = parsed_url.path.strip('/').split('/')
        
        # Make sure we have the right path structure
        if len(path_parts) >= 2 and path_parts[0] == 'ai-tools':
            category_slug = path_parts[1]
            
            tool_urls = set()
            max_pages = 100
            
            # Start from page 2 since page 1 is already loaded
            for page in range(2, max_pages + 1):
                # Use the exact URL format verified in the browser console
                if category_slug == "code-generator":
                    ajax_url = f"https://aitoptools.com/ai-tools/code-generator/ajaxtoolcat/6806/{page}/"
                elif category_slug == "summarizer":
                    ajax_url = f"https://aitoptools.com/ai-tools/summarizer/ajaxtoolcat/6807/{page}/"
                else:
                    ajax_url = f"https://aitoptools.com/ai-tools/{category_slug}/ajaxtoolcat/{category_id}/{page}/"
                
                logger.info(f"Trying AJAX URL: {ajax_url}")
                
                try:
                    async with session.get(ajax_url, headers=self.headers) as ajax_response:
                        if ajax_response.status != 200:
                            logger.error(f"AJAX request error {ajax_response.status}")
                            break
                        
                        ajax_html = await ajax_response.text()
                        
                        # Check for empty response or end message
                        if "No More Tools To Explore" in ajax_html or not ajax_html.strip() or ajax_html.strip() == "":
                            logger.info(f"Reached end of tools at page {page}")
                            break
                        
                        # Extract tool URLs from the AJAX response
                        new_tools = self._extract_tools_from_html(ajax_html)
                        new_count = 0
                        
                        for tool in new_tools:
                            if tool not in tool_urls:
                                tool_urls.add(tool)
                                new_count += 1
                        
                        logger.info(f"Found {new_count} new tools from page {page}, total: {len(tool_urls)}")
                        
                        # If no new tools found, we're done
                        if new_count == 0:
                            break
                        
                        # Short pause to be nice to the server
                        await asyncio.sleep(1)
                        
                except Exception as e:
                    logger.error(f"Error fetching AJAX page {page}: {e}")
                    break
            
            logger.info(f"Direct AJAX URL method found {len(tool_urls)} tools")
            return list(tool_urls)
        else:
            logger.warning(f"URL structure not suitable for direct AJAX method: {base_url}")
            return []
    
    async def _try_standard_wordpress_ajax(self, session, category_id, initial_count):
        """Try the standard WordPress AJAX method with loadmorecategory"""
        logger.info("Trying standard WordPress AJAX with loadmorecategory")
        
        tool_urls = set()
        max_pages = 1
        
        for page in range(1, max_pages + 1):
            ajax_url = "https://aitoptools.com/wp-admin/admin-ajax.php"
            
            # Try multiple variations of form data parameters
            form_data_variations = [
                {
                    'action': 'loadmorecategory',
                    'cat': category_id,
                    'page': str(page)
                },
                {
                    'action': 'load_more_category',
                    'cat': category_id,
                    'page': str(page)
                },
                {
                    'action': 'load_more',
                    'cat': category_id,
                    'page': str(page)
                },
                {
                    'action': 'jet_engine_ajax',
                    'handler': 'listing_load_more',
                    'page': str(page),
                    'query': json.dumps({"post_type": "ai-tools", "taxonomy": "ai-category", "term": category_id})
                }
            ]
            
            success = False
            
            # Try each variation
            for form_data in form_data_variations:
                logger.info(f"Trying AJAX with action: {form_data.get('action')} on page {page}")
                
                try:
                    async with session.post(ajax_url, data=form_data, headers=self.headers) as ajax_response:
                        if ajax_response.status != 200:
                            logger.error(f"AJAX request error {ajax_response.status}")
                            continue
                        
                        ajax_html = await ajax_response.text()
                        
                        # Check for empty response or end message
                        if "No More Tools To Explore" in ajax_html or not ajax_html.strip():
                            logger.info(f"Reached end of tools at page {page}")
                            success = True
                            break
                        
                        # Extract tool URLs from the AJAX response
                        new_tools = self._extract_tools_from_html(ajax_html)
                        new_count = 0
                        
                        for tool in new_tools:
                            if tool not in tool_urls:
                                tool_urls.add(tool)
                                new_count += 1
                        
                        logger.info(f"Found {new_count} new tools from page {page}, total: {len(tool_urls)}")
                        
                        # If we found new tools, this method works
                        if new_count > 0:
                            success = True
                            break
                        
                except Exception as e:
                    logger.error(f"Error with AJAX variation: {e}")
                    continue
            
            # If none of the variations worked or we've reached the end, stop
            if not success:
                break
            
            # Short pause to be nice to the server
            await asyncio.sleep(1)
        
        logger.info(f"Standard WordPress AJAX method found {len(tool_urls)} tools")
        return list(tool_urls)
    
    async def _try_direct_page_urls(self, session, base_url, initial_count):
        """Try using direct page URLs like /page/2/, /page/3/, etc."""
        logger.info("Trying direct page URLs method")
        
        tool_urls = set()
        max_pages = 1  # Try more pages
        
        # Strip trailing slash for URL joining
        if base_url.endswith('/'):
            base_url = base_url[:-1]
        
        for page in range(2, max_pages + 1):  # Start with page 2 (page 1 is already loaded)
            page_url = f"{base_url}/page/{page}/"
            logger.info(f"Trying direct page URL: {page_url}")
            
            try:
                async with session.get(page_url, headers=self.headers) as page_response:
                    if page_response.status != 200:
                        logger.info(f"Page {page} not found (status {page_response.status})")
                        break
                    
                    page_html = await page_response.text()
                    
                    # Check for empty page or no tools
                    soup = BeautifulSoup(page_html, 'html.parser')
                    tools_container = soup.select('.jet-listing-grid, .elementor-element-populated')
                    
                    if not tools_container or all('no-posts-found' in elem.get('class', []) for elem in tools_container):
                        logger.info(f"No tools found on page {page}")
                        break
                    
                    # Extract tool URLs from the page
                    new_tools = self._extract_tools_from_html(page_html)
                    new_count = 0
                    
                    for tool in new_tools:
                        if tool not in tool_urls:
                            tool_urls.add(tool)
                            new_count += 1
                    
                    logger.info(f"Found {new_count} new tools on page {page}")
                    
                    # If we're not getting any new tools, stop
                    if new_count == 0:
                        logger.info("No new tools found, stopping pagination")
                        break
            except Exception as e:
                logger.error(f"Error fetching page {page}: {e}")
                break
            
            # Short pause between page requests
            await asyncio.sleep(1)
        
        logger.info(f"Direct page URLs method found {len(tool_urls)} tools")
        return list(tool_urls)
    
    async def extract_tool_urls(self, session, category_url):
        """Extract tool URLs using multiple methods for robustness"""
        logger.info(f"Fetching category page: {category_url}")
        
        try:
            # Get the initial page HTML
            async with session.get(category_url, headers=self.headers, timeout=30) as response:
                if response.status != 200:
                    logger.error(f"HTTP error {response.status} for {category_url}")
                    return []
                
                html = await response.text()
                
                # First, extract all visible tool URLs
                tool_urls = set()
                matches = re.findall(r'href=[\'"]https://aitoptools.com/tool/([^\'"]+)[\'"]', html)
                for match in matches:
                    tool_urls.add(f"https://aitoptools.com/tool/{match}")
                
                visible_count = len(tool_urls)
                logger.info(f"Found {visible_count} tools on initial page load")
                
                # Try different AJAX methods to load more tools
                more_tools = await self._try_different_ajax_methods(session, category_url, html, visible_count)
                
                # Add any new tools we found
                for url in more_tools:
                    tool_urls.add(url)
                
                logger.info(f"Total tools found: {len(tool_urls)}")
                return list(tool_urls)
                
        except Exception as e:
            logger.error(f"Error extracting tools from {category_url}: {e}")
            return []
    
    async def process_subcategory(self, session, subcategory_url, subcategory_name, max_tools=0):
        """Process all tools in a subcategory"""
        logger.info(f"Processing subcategory: {subcategory_name} ({subcategory_url})")
        
        # Get tool URLs
        tool_urls = await self.extract_tool_urls(session, subcategory_url)
        
        if not tool_urls:
            logger.warning(f"No tool URLs found for {subcategory_name}")
            return 0
        
        # Filter unprocessed URLs
        unprocessed_urls = [url for url in tool_urls if url not in self.processed_urls]
        logger.info(f"Found {len(unprocessed_urls)} unprocessed tools out of {len(tool_urls)}")
        
        # Limit to max_tools if specified
        if max_tools > 0 and len(unprocessed_urls) > max_tools:
            unprocessed_urls = unprocessed_urls[:max_tools]
            logger.info(f"Processing only {max_tools} tools")
        
        processed_count = 0
        # Process tools in batches
        batch_size = min(self.max_concurrent_tools, len(unprocessed_urls))
        if batch_size == 0:
            return 0
            
        for i in range(0, len(unprocessed_urls), batch_size):
            batch = unprocessed_urls[i:i+batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(unprocessed_urls) + batch_size - 1)//batch_size}")
            
            # Process tools concurrently
            tasks = []
            for url in batch:
                tasks.append(extract_tool_data(url, subcategory_name))
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for j, result in enumerate(results):
                url = batch[j]
                
                if isinstance(result, Exception):
                    logger.error(f"Error processing {url}: {result}")
                    self.failed_urls[url] = {"error": str(result), "timestamp": int(time.time())}
                elif result is None:
                    logger.warning(f"No data extracted from {url}")
                    self.failed_urls[url] = {"error": "No data extracted", "timestamp": int(time.time())}
                else:
                    logger.info(f"Successfully processed: {result.get('name', 'Unnamed tool')}")
                    self.all_tools_data.append(result)
                    self.processed_urls.add(url)
                    processed_count += 1
            
            # Save checkpoint after each batch
            save_checkpoint(self.all_tools_data, self.processed_urls, self.failed_urls)
            
            # Short pause between batches
            if i + batch_size < len(unprocessed_urls):
                await asyncio.sleep(2)
        
        logger.info(f"Completed {subcategory_name}, processed {processed_count} tools")
        return processed_count
    
    async def crawl_all_categories(self, output_format="json", max_tools_per_subcategory=0, 
                                 specific_category=None, specific_subcategory=None):
        """Crawl all categories and subcategories"""
        logger.info("Starting full crawl of all categories and subcategories")
        
        # Create a session for all requests
        async with aiohttp.ClientSession() as session:
            # Determine which categories to process
            categories_to_process = {}
            if specific_category:
                if specific_category in CATEGORY_MAP:
                    categories_to_process[specific_category] = CATEGORY_MAP[specific_category]
                else:
                    logger.error(f"Category not found: {specific_category}")
                    return None
            else:
                categories_to_process = CATEGORY_MAP
            
            total_processed = 0
            
            # Process each category
            for category_name, category_data in categories_to_process.items():
                logger.info(f"Processing main category: {category_name}")
                
                # Determine which subcategories to process
                if specific_subcategory:
                    subcategories = [sc for sc in category_data["subcategories"] 
                                   if sc["name"] == specific_subcategory]
                    if not subcategories:
                        logger.warning(f"Subcategory {specific_subcategory} not found in {category_name}")
                        continue
                else:
                    subcategories = category_data["subcategories"]
                
                # Process each subcategory
                for subcategory in subcategories:
                    try:
                        processed = await self.process_subcategory(
                            session,
                            subcategory["url"],
                            subcategory["name"],
                            max_tools_per_subcategory
                        )
                        total_processed += processed
                    except Exception as e:
                        logger.error(f"Error processing subcategory {subcategory['name']}: {e}")
                        logger.error(traceback.format_exc())
                        # Continue with next subcategory
                        continue
            
            logger.info(f"Full crawl complete! Processed {total_processed} tools")
            logger.info(f"Total tools in database: {len(self.all_tools_data)}")
            
            # Save all the extracted data
            if output_format.lower() == "csv":
                return save_to_csv(self.all_tools_data)
            else:
                return save_to_json(self.all_tools_data)


async def main():
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Enhanced AI tools crawler with improved AJAX methods")
    parser.add_argument("--format", choices=["csv", "json"], default="json", help="Output format (csv or json)")
    parser.add_argument("--max-tools", type=int, default=0, help="Maximum number of tools per subcategory (0 for all)")
    parser.add_argument("--concurrency", type=int, default=5, help="Maximum concurrent tool extractions")
    parser.add_argument("--category", help="Crawl only a specific main category")
    parser.add_argument("--subcategory", help="Crawl only a specific subcategory (requires --category)")
    args = parser.parse_args()
    
    # Display startup message
    print("\n===== AITopTools Enhanced AJAX Crawler =====")
    print(f"Output format: {args.format}")
    print(f"Max tools per subcategory: {args.max_tools if args.max_tools > 0 else 'All'}")
    print(f"Concurrency: {args.concurrency}")
    if args.category:
        print(f"Filtering to category: {args.category}")
        if args.subcategory:
            print(f"Filtering to subcategory: {args.subcategory}")
    print("===================================\n")
    
    start_time = time.time()
    
    # Initialize the crawler
    crawler = EnhancedCrawler(max_concurrent_tools=args.concurrency)
    
    # Crawl all categories and save the data
    output_file = await crawler.crawl_all_categories(
        output_format=args.format, 
        max_tools_per_subcategory=args.max_tools,
        specific_category=args.category,
        specific_subcategory=args.subcategory
    )
    
    end_time = time.time()
    total_time = end_time - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print("\n===== Extraction Summary =====")
    print(f"Total time: {int(hours)}h {int(minutes)}m {int(seconds)}s")
    print(f"Total tools extracted: {len(crawler.all_tools_data)}")
    print(f"Data saved to: {output_file}")
    
    if crawler.failed_urls:
        print(f"Failed URLs: {len(crawler.failed_urls)}")
    else:
        print("No failed URLs!")
    
    print("===================================\n")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nCrawling interrupted by user. Partial results are saved in the checkpoint file.")
    except Exception as e:
        print(f"\nCrawling terminated due to an error: {e}")
        print("Please check logs for details.")

================
File: fix_extractor.py
================
import asyncio
import aiohttp
import re
import json
import logging
import sys
from bs4 import BeautifulSoup

from categories import CATEGORY_MAP

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("fix_extractor.log", encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("fix_extractor")

# Build subcategory mapping
SUBCATEGORY_TO_CATEGORY = {}
for category, data in CATEGORY_MAP.items():
    for subcat in data["subcategories"]:
        SUBCATEGORY_TO_CATEGORY[subcat["name"]] = category

async def extract_tool_data(url, subcategory="Email Finder/Verifier"):
    """Extract data from a single tool URL with exact extract_tool.py methods"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    }
    
    # Get the category from subcategory
    category = SUBCATEGORY_TO_CATEGORY.get(subcategory, "")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(url, headers=headers) as response:
            if response.status != 200:
                logger.error(f"Failed to fetch URL: {url}")
                return None
            
            html = await response.text()
    
    # Parse with BeautifulSoup
    soup = BeautifulSoup(html, 'html.parser')
    
    # Initialize result structure
    data = {
        "category": category,
        "subcategory": subcategory,
        "name": "",
        "short_description": "",
        "image_url": "",
        "website_url": "",
        "pricing_type": "",
        "pricing_amount": "",
        "full_description": "",
        "use_cases_and_features": [],
        "page_url": url
    }
    
    # 1. Extract name
    name_element = soup.select_one('h1.elementor-heading-title')
    if name_element:
        data["name"] = name_element.text.strip()
    else:
        h1 = soup.find('h1')
        if h1:
            data["name"] = h1.text.strip()
    
    # 2. Extract short description
    meta_desc = soup.select_one('meta[name="description"]')
    if meta_desc and meta_desc.has_attr('content'):
        data["short_description"] = meta_desc['content'].strip()
    
    # 3. Extract image URL - PRIORITY IS .WEBP FROM CSS
    # First, specifically look for the CSS background image
    webp_found = False
    for style in soup.select('style'):
        if not style.string:
            continue
            
        # Look for background-image:url("...") patterns
        bg_images = re.findall(r'background-image\s*:\s*url\([\'"]([^\'"]+)[\'"]\)', style.string)
        for bg_image in bg_images:
            if '/wp-content/smush-webp/' in bg_image and bg_image.endswith('.webp'):
                data["image_url"] = bg_image
                webp_found = True
                logger.info(f"Found .webp image in CSS: {bg_image}")
                break
        if webp_found:
            break
    
    # If no webp found, check meta og:image
    if not webp_found:
        meta_image = soup.select_one('meta[property="og:image"]')
        if meta_image and meta_image.has_attr('content'):
            data["image_url"] = meta_image['content']
    
    # 4. Extract website URL
    website_links = soup.select('a.jet-listing-dynamic-link__link')
    if website_links:
        for link in website_links:
            if link.has_attr('href'):
                # Get the URL without query parameters
                full_url = link['href']
                base_url = full_url.split('?')[0] if '?' in full_url else full_url
                data["website_url"] = base_url
                break
    
    # If we didn't find the URL with the above method, try looking for any "Visit" buttons
    if not data["website_url"]:
        visit_links = soup.select('a')
        for link in visit_links:
            link_text = link.text.strip().lower()
            if ('visit' in link_text or 'try' in link_text) and link.has_attr('href'):
                full_url = link['href']
                base_url = full_url.split('?')[0] if '?' in full_url else full_url
                data["website_url"] = base_url
                break
    
    # 5. Extract pricing information
    pricing_elements = soup.select('.jet-listing-dynamic-field__content')
    pricing_types = ["Free", "Freemium", "Free Trial", "Paid", "Contact for Pricing"]
    pricing_found = False
    
    for element in pricing_elements:
        element_text = element.text.strip()
        
        # Check if this element contains a pricing type
        for pricing_type in pricing_types:
            if pricing_type.lower() == element_text.lower():
                data["pricing_type"] = element_text
                pricing_found = True
                break
        
        # Check if this element contains pricing amount
        if '$' in element_text or '/month' in element_text.lower() or '/mo' in element_text.lower():
            data["pricing_amount"] = element_text
        
        # If we found both pricing type and amount, we can stop
        if pricing_found and data["pricing_amount"]:
            break
    
    # If we couldn't find pricing with the above method, try broader search
    if not pricing_found:
        for pricing_type in pricing_types:
            pricing_pattern = re.compile(pricing_type, re.IGNORECASE)
            pricing_element = soup.find(string=pricing_pattern)
            if pricing_element:
                data["pricing_type"] = pricing_type
                break
    
    # 6. Extract full description
    description_elements = soup.select('.elementor-widget-theme-post-content p')
    for p in description_elements:
        p_text = p.text.strip()
        if len(p_text) > 100:  # Only substantial paragraphs
            data["full_description"] = p_text
            break
    
    # 7. Extract use cases and features using the exact approach from extract_tool.py
    # First try to find the section with "Use Cases And Features" heading
    features_heading = soup.find(string=re.compile("Use Cases And Features", re.IGNORECASE))
    if features_heading:
        # Look for the text editor div after the heading
        feature_widget = None
        current = features_heading.parent
        
        # Navigate up to find a proper parent element
        while current and not feature_widget:
            # Try to find the closest text editor after this element
            feature_widget = current.find_next(class_="elementor-widget-text-editor")
            current = current.parent
        
        if feature_widget:
            # Get the container with the numbered list
            features_element = feature_widget.select_one('.elementor-widget-container')
            if features_element:
                # Get the raw HTML to preserve <br> tags
                features_html = str(features_element)
                
                # Extract numbered items with regex
                numbered_features = re.findall(r'(\d+)\.\s*([^<>\.]+)\.?', features_html)
                if numbered_features:
                    # Sort by the number to ensure correct order
                    sorted_features = sorted(numbered_features, key=lambda x: int(x[0]))
                    data["use_cases_and_features"] = [feature[1].strip() for feature in sorted_features]
    
    # If we couldn't extract features using the heading, try alternative methods
    if not data["use_cases_and_features"]:
        # Try to find text that looks like numbered list items
        for element in soup.select('p, div'):
            element_text = element.get_text()
            if re.search(r'1\.\s+', element_text):
                # Extract all numbered items
                feature_items = re.findall(r'(\d+)\.\s+([^\.]+)\.?', element_text)
                if feature_items:
                    # Sort by the number to ensure correct order
                    sorted_features = sorted(feature_items, key=lambda x: int(x[0]))
                    data["use_cases_and_features"] = [feature[1].strip() for feature in sorted_features]
                    break
    
    # If still no features, try ordered lists
    if not data["use_cases_and_features"]:
        for ol in soup.find_all('ol'):
            features = []
            for li in ol.find_all('li'):
                feature_text = li.text.strip()
                if len(feature_text) > 5:
                    features.append(feature_text)
            if features:
                data["use_cases_and_features"] = features
                break
    
    # Clean up features
    if data["use_cases_and_features"]:
        data["use_cases_and_features"] = [f for f in data["use_cases_and_features"] if len(f) > 5 and not any(
            x in f.lower() for x in ["submit tool", "contact", "about us", "privacy"])]

    
    return data

async def main():
    import argparse
    parser = argparse.ArgumentParser(description="Fix extraction for a specific URL")
    parser.add_argument("--url", required=True, help="URL to extract")
    parser.add_argument("--subcategory", default="Email Finder/Verifier", help="Subcategory of the tool")
    args = parser.parse_args()
    
    data = await extract_tool_data(args.url, args.subcategory)
    
    if data:
        print("\nExtracted AI Tool Data:\n")
        print(f"Category: {data.get('category', '')}")
        print(f"Subcategory: {data.get('subcategory', '')}")
        print(f"Name: {data.get('name', '')}")
        print(f"Short Description: {data.get('short_description', '')}")
        print(f"Image URL: {data.get('image_url', '')}")
        print(f"Website URL: {data.get('website_url', '')}")
        print(f"Pricing Type: {data.get('pricing_type', '')}")
        print(f"Pricing Amount: {data.get('pricing_amount', '')}")
        print("\nFull Description:")
        print(data.get('full_description', ''))
        print("\nUse Cases And Features:")
        for i, feature in enumerate(data.get('use_cases_and_features', []), 1):
            print(f"{i}. {feature}")
        
        # Also output as JSON
        print("\nJSON Output:")
        print(json.dumps(data, indent=2))
    else:
        print("Failed to extract data")

if __name__ == "__main__":
    asyncio.run(main())

================
File: json-structure-aitoptools.com.json
================
{
  "tool_data_structure": {
    "category": "Main category of the tool (e.g., 'Business & Productivity')",
    "subcategory": "Specific subcategory of the tool (e.g., 'Email Finder/Verifier')",
    "name": "Name of the AI tool",
    "short_description": "Brief description typically from meta description tag",
    "image_url": "URL to the tool's logo or image (prioritizes .webp from CSS)",
    "website_url": "External website URL of the tool",
    "pricing_type": "Type of pricing (e.g., 'Free', 'Freemium', 'Free Trial', 'Paid', 'Contact for Pricing')",
    "pricing_amount": "Specific pricing information (e.g., '$19/month')",
    "full_description": "Detailed description of the tool",
    "use_cases_and_features": [
      "List of features and use cases",
      "Often extracted from numbered lists",
      "Multiple extraction methods are attempted"
    ],
    "page_url": "URL of the tool page on aitoptools.com"
  },
  
  "example": {
    "category": "Writing & Content",
    "subcategory": "Summarizer",
    "name": "ChatGPT LinkedIn Email Generator",
    "short_description": "Use AI to create personalized LinkedIn outreach emails that get responses.",
    "image_url": "https://aitoptools.com/wp-content/uploads/2023/05/chatgpt-linkedin-email-generator.webp",
    "website_url": "https://chatgpt-linkedin-email-generator.example.com",
    "pricing_type": "Freemium",
    "pricing_amount": "$19/month",
    "full_description": "ChatGPT LinkedIn Email Generator is an AI-powered tool that helps you create personalized outreach emails for LinkedIn. It uses advanced language models to craft messages that are more likely to get responses, saving you time and improving your networking results.",
    "use_cases_and_features": [
      "Create personalized LinkedIn connection requests",
      "Generate follow-up messages for existing connections",
      "Craft persuasive sales outreach messages",
      "Personalize messages based on recipient's profile",
      "Save templates for future use"
    ],
    "page_url": "https://aitoptools.com/tool/chatgpt-linkedin-email-generator/"
  }
}

================
File: requirements.txt
================
aiohttp
beautifulsoup4

================
File: updated-readme.md
================
# AI Tools Crawler

A robust, HTTP-based web crawler specialized for extracting AI tools data from aitoptools.com. This crawler implements advanced techniques for handling dynamic content loading, AJAX pagination, and detailed data extraction.

## Features

- **AJAX Pagination Handling**: Automatically handles "load more" functionality to access all tools
- **Multiple Category ID Detection Methods**: Robust detection of category IDs for accessing additional content
- **Concurrent Processing**: Multi-threaded design for faster crawling
- **Robust Error Handling**: Gracefully handles network issues and unexpected page formats
- **Checkpoint System**: Resume interrupted crawls from where they left off
- **Flexible Output**: Export data in JSON or CSV formats
- **Category Filtering**: Focus crawling on specific categories or subcategories

## Required Files

This crawler has been simplified to require only these core files:

- **enhanced_ajax_crawler.py**: Main crawler with AJAX handling capabilities
- **categories.py**: Defines the category structure for the website
- **fix_extractor.py**: Extracts detailed tool information from individual pages
- **utils.py**: Utility functions for data management and checkpointing
- **requirements.txt**: Lists the dependencies

## Installation

### Prerequisites

- Python 3.8+
- pip package manager

### Setup

1. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

```bash
python enhanced_ajax_crawler.py [options]
```

### Command-line Options

- `--format`: Output format - `json` (default) or `csv`
- `--max-tools`: Maximum number of tools to extract per subcategory (0 for all)
- `--concurrency`: Number of tool pages to process concurrently (default: 5)
- `--category`: Crawl only a specific main category
- `--subcategory`: Crawl only a specific subcategory (requires --category)

### Examples

#### Crawl a specific category and subcategory:
```bash
python enhanced_ajax_crawler.py --category "Tech & Development" --subcategory "Detector"
```

#### Extract all tools in a specific subcategory:
```bash
python enhanced_ajax_crawler.py --category "Tech & Development" --subcategory "Detector" --max-tools 0
```

#### Crawl the entire website:
```bash
python enhanced_ajax_crawler.py --max-tools 0
```

#### Export to CSV format:
```bash
python enhanced_ajax_crawler.py --format csv
```

#### Optimize for faster crawling:
```bash
python enhanced_ajax_crawler.py --concurrency 10
```

## Output Files

- **aitoptools_data_[timestamp].json/csv**: Contains all extracted tool data
- **aitoptools_crawler_checkpoint.json**: Checkpoint data for resuming interrupted crawls
- **enhanced_crawler.log**: Detailed logging information

## Troubleshooting

### Common Issues

1. **Network Errors**: If you encounter frequent network errors, try:
   - Reducing the concurrency level
   - Adding more sleep time between requests
   - Checking your internet connection

2. **Extraction Issues**: If the crawler misses data, check:
   - The log file for specific errors
   - Whether the website structure has changed
   
3. **Performance Issues**: For slow crawling:
   - Increase the concurrency level
   - Focus on specific categories/subcategories

### Resuming Failed Crawls

The crawler automatically creates checkpoints. To resume an interrupted crawl, simply run the command again with the same parameters.

================
File: utils.py
================
import json
import csv
import os
import time
import re
from typing import List, Dict, Any, Set

# Checkpoint file for resuming the crawler
CHECKPOINT_FILE = "aitoptools_crawler_checkpoint.json"

def load_checkpoint():
    """Load checkpoint data if file exists"""
    data = {
        'tools_data': [],
        'processed_urls': set(),
        'failed_urls': {}
    }
    
    if os.path.exists(CHECKPOINT_FILE):
        try:
            with open(CHECKPOINT_FILE, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
                data['tools_data'] = checkpoint_data.get('tools_data', [])
                data['processed_urls'] = set(checkpoint_data.get('processed_urls', []))
                data['failed_urls'] = checkpoint_data.get('failed_urls', {})
            print(f"Loaded checkpoint with {len(data['tools_data'])} tools, {len(data['processed_urls'])} processed URLs, and {len(data['failed_urls'])} failed URLs")
        except Exception as e:
            print(f"Error loading checkpoint: {e}")
    
    return data

def save_checkpoint(tools_data: List[Dict[str, Any]], processed_urls: Set[str], failed_urls: Dict[str, Dict[str, Any]] = None):
    """Save checkpoint data for resume support"""
    if failed_urls is None:
        failed_urls = {}
        
    checkpoint_data = {
        'tools_data': tools_data,
        'processed_urls': list(processed_urls),
        'failed_urls': failed_urls
    }
    try:
        # First save to a temporary file
        temp_file = f"{CHECKPOINT_FILE}.tmp"
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_data, f)
        
        # Then rename it to the actual checkpoint file (safer atomic operation)
        if os.path.exists(CHECKPOINT_FILE):
            os.replace(temp_file, CHECKPOINT_FILE)
        else:
            os.rename(temp_file, CHECKPOINT_FILE)
            
        print(f"Saved checkpoint with {len(tools_data)} tools, {len(processed_urls)} processed URLs, and {len(failed_urls)} failed URLs")
        return True
    except Exception as e:
        print(f"Error saving checkpoint: {e}")
        return False

def save_to_csv(tools_data: List[Dict[str, Any]]):
    """Save the extracted tools data to a CSV file"""
    timestamp = int(time.time())
    output_file = f"aitoptools_data_{timestamp}.csv"
    
    # Define the field order to match extract_tool.py output
    fieldnames = [
        'category', 'subcategory', 'name', 'short_description', 'image_url',
        'website_url', 'pricing_type', 'pricing_amount', 'full_description',
        'use_cases_and_features', 'page_url'
    ]
    
    # Process the data for CSV format
    processed_data = []
    for tool in tools_data:
        processed_tool = dict(tool)  # Create a copy
        
        # Convert features list to pipe-separated string
        if 'use_cases_and_features' in processed_tool and isinstance(processed_tool['use_cases_and_features'], list):
            processed_tool['use_cases_and_features'] = "|".join([str(item) for item in processed_tool['use_cases_and_features']])
        
        processed_data.append(processed_tool)
    
    # Write to CSV
    with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames, extrasaction='ignore')
        writer.writeheader()
        writer.writerows(processed_data)
    
    print(f"Saved {len(processed_data)} AI tools to {output_file}")
    return output_file

def save_to_json(tools_data: List[Dict[str, Any]]):
    """Save the extracted tools data to a JSON file"""
    timestamp = int(time.time())
    output_file = f"aitoptools_data_{timestamp}.json"
    
    with open(output_file, "w", encoding="utf-8") as jsonfile:
        json.dump(tools_data, jsonfile, indent=2)
    
    print(f"Saved {len(tools_data)} AI tools to {output_file}")
    return output_file

def clean_tool_features(features_list):
    """Clean up the features list to remove navigation items and other irrelevant content"""
    if not features_list:
        return []
        
    cleaned_features = []
    for feature in features_list:
        if not isinstance(feature, str):
            continue
            
        # Clean up the feature text
        clean_feature = feature.strip()
        
        # Remove any numbering (1., 2., etc.) from the front
        clean_feature = re.sub(r'^\d+\.\s*', '', clean_feature)
        
        # Skip menu items, navigation links, etc.
        if (clean_feature and len(clean_feature) > 5 and len(clean_feature) < 200 and
            not any(x in clean_feature.lower() for x in ["submit tool", "contact", "about us", "privacy policy", "terms"])):
            cleaned_features.append(clean_feature)
    
    return cleaned_features if cleaned_features else []

def export_failed_urls(failed_urls: Dict[str, Dict[str, Any]]):
    """Export the list of failed URLs to a separate file for analysis"""
    if not failed_urls:
        return None
        
    timestamp = int(time.time())
    output_file = f"failed_urls_{timestamp}.json"
    
    with open(output_file, "w", encoding="utf-8") as jsonfile:
        json.dump(failed_urls, jsonfile, indent=2)
    
    print(f"Saved {len(failed_urls)} failed URLs to {output_file}")
    return output_file



================================================================
End of Codebase
================================================================
