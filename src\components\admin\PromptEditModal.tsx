'use client';

import React, { useState, useEffect } from 'react';
import { Save, X, TestTube, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { PromptEditor } from './PromptEditor';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: 'content' | 'description' | 'features' | 'pricing' | 'pros_cons' | 'partial' | 'seo' | 'faqs' | 'releases';
  promptType: 'system' | 'user';
  template: string;
  variables: string[];
  isActive: boolean;
  lastModified: string;
  usage: number;
  validationRules?: string[];
  formatRequirements?: string;
}

interface PromptEditModalProps {
  prompt: PromptTemplate;
  onSave: (prompt: PromptTemplate) => Promise<void>;
  onClose: () => void;
  onTest?: (prompt: PromptTemplate) => Promise<any>;
  isLoading?: boolean;
  isTesting?: boolean;
}

export function PromptEditModal({
  prompt,
  onSave,
  onClose,
  onTest,
  isLoading = false,
  isTesting = false
}: PromptEditModalProps) {
  const [editedPrompt, setEditedPrompt] = useState<PromptTemplate>(prompt);
  const [hasChanges, setHasChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showValidation, setShowValidation] = useState(false);

  // Track changes
  useEffect(() => {
    const changed = JSON.stringify(editedPrompt) !== JSON.stringify(prompt);
    setHasChanges(changed);
  }, [editedPrompt, prompt]);

  // Validate prompt
  useEffect(() => {
    const errors: string[] = [];
    
    if (!editedPrompt.name.trim()) {
      errors.push('Name is required');
    }
    
    if (!editedPrompt.description.trim()) {
      errors.push('Description is required');
    }
    
    if (!editedPrompt.template.trim()) {
      errors.push('Template content is required');
    }
    
    if (editedPrompt.template.length < 50) {
      errors.push('Template should be at least 50 characters long');
    }
    
    // Check for required variables in system prompts
    if (editedPrompt.promptType === 'system' && editedPrompt.category === 'content') {
      if (!editedPrompt.template.includes('{DATABASE_SCHEMA}')) {
        errors.push('System prompts should include {DATABASE_SCHEMA} variable');
      }
    }
    
    // Check for proper JSON escaping if this is stored as JSON
    try {
      JSON.stringify(editedPrompt.template);
    } catch (e) {
      errors.push('Template contains invalid characters for JSON storage');
    }
    
    setValidationErrors(errors);
  }, [editedPrompt]);

  const handleSave = async () => {
    if (validationErrors.length > 0) {
      setShowValidation(true);
      return;
    }
    
    try {
      await onSave(editedPrompt);
    } catch (error) {
      console.error('Failed to save prompt:', error);
      // Could add error toast here
    }
  };

  const handleTest = async () => {
    if (onTest) {
      try {
        await onTest(editedPrompt);
      } catch (error) {
        console.error('Failed to test prompt:', error);
        // Could add error toast here
      }
    }
  };

  const handleTemplateChange = (newTemplate: string) => {
    setEditedPrompt(prev => ({
      ...prev,
      template: newTemplate
    }));
  };

  const getCategoryInfo = (category: string) => {
    switch (category) {
      case 'content':
        return {
          description: 'Complete content generation for tools',
          variables: ['DATABASE_SCHEMA'],
          tips: 'Include all required fields and maintain AI Dude tone'
        };
      case 'partial':
        return {
          description: 'Partial content generation with context',
          variables: ['existingToolData', 'scrapedContent', 'toolUrl'],
          tips: 'Focus on specific sections while maintaining context'
        };
      case 'features':
        return {
          description: 'Feature list generation',
          variables: ['existingToolData', 'scrapedContent', 'toolUrl'],
          tips: 'Generate 5-8 specific, actionable features'
        };
      case 'pricing':
        return {
          description: 'Pricing information generation',
          variables: ['existingToolData', 'scrapedContent', 'toolUrl'],
          tips: 'Extract detailed pricing plans and tiers'
        };
      default:
        return {
          description: 'Custom prompt template',
          variables: [],
          tips: 'Follow AI Dude methodology and tone'
        };
    }
  };

  const categoryInfo = getCategoryInfo(editedPrompt.category);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-zinc-800 border border-black rounded-lg max-w-6xl w-full max-h-[95vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-zinc-700 flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-white">Edit Prompt Template</h2>
            <p className="text-gray-400 text-sm mt-1">{categoryInfo.description}</p>
          </div>
          
          <div className="flex items-center space-x-2">
            {hasChanges && (
              <div className="flex items-center space-x-1 text-yellow-400">
                <AlertCircle className="w-4 h-4" />
                <span className="text-xs">Unsaved changes</span>
              </div>
            )}
            
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex">
          {/* Left Panel - Form */}
          <div className="w-1/3 p-6 border-r border-zinc-700 overflow-y-auto">
            <div className="space-y-4">
              {/* Basic Info */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Name</label>
                <input
                  type="text"
                  value={editedPrompt.name}
                  onChange={(e) => setEditedPrompt(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <textarea
                  value={editedPrompt.description}
                  onChange={(e) => setEditedPrompt(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Category</label>
                  <select
                    value={editedPrompt.category}
                    onChange={(e) => setEditedPrompt(prev => ({ ...prev, category: e.target.value as any }))}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                  >
                    <option value="content">Content</option>
                    <option value="partial">Partial</option>
                    <option value="features">Features</option>
                    <option value="pricing">Pricing</option>
                    <option value="pros_cons">Pros & Cons</option>
                    <option value="seo">SEO</option>
                    <option value="faqs">FAQs</option>
                    <option value="releases">Releases</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Type</label>
                  <select
                    value={editedPrompt.promptType}
                    onChange={(e) => setEditedPrompt(prev => ({ ...prev, promptType: e.target.value as any }))}
                    className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                  >
                    <option value="system">System</option>
                    <option value="user">User</option>
                  </select>
                </div>
              </div>

              {/* Category Info */}
              <div className="bg-zinc-700 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <Info className="w-4 h-4 text-blue-400" />
                  <span className="text-sm font-medium text-blue-400">Category Guidelines</span>
                </div>
                <p className="text-xs text-gray-300 mb-2">{categoryInfo.tips}</p>
                {categoryInfo.variables.length > 0 && (
                  <div>
                    <span className="text-xs text-gray-400">Available variables: </span>
                    <span className="text-xs text-blue-300">
                      {categoryInfo.variables.map(v => `{${v}}`).join(', ')}
                    </span>
                  </div>
                )}
              </div>

              {/* Validation */}
              {(showValidation || validationErrors.length > 0) && (
                <div className="bg-red-900/20 border border-red-700 rounded-lg p-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertCircle className="w-4 h-4 text-red-400" />
                    <span className="text-sm font-medium text-red-400">Validation Issues</span>
                  </div>
                  <ul className="text-xs text-red-300 space-y-1">
                    {validationErrors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Status */}
              <div className="flex items-center justify-between">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={editedPrompt.isActive}
                    onChange={(e) => setEditedPrompt(prev => ({ ...prev, isActive: e.target.checked }))}
                    className="rounded border-zinc-600 bg-zinc-700 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-300">Active</span>
                </label>
                
                <div className="text-xs text-gray-400">
                  Usage: {editedPrompt.usage}
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - Editor */}
          <div className="flex-1 p-6">
            <PromptEditor
              value={editedPrompt.template}
              onChange={handleTemplateChange}
              height="calc(100vh - 300px)"
              variables={categoryInfo.variables}
              showPreview={true}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-zinc-700 flex justify-between">
          <div className="flex items-center space-x-2">
            {validationErrors.length === 0 ? (
              <div className="flex items-center space-x-1 text-green-400">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm">Ready to save</span>
              </div>
            ) : (
              <div className="flex items-center space-x-1 text-red-400">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">{validationErrors.length} validation issues</span>
              </div>
            )}
          </div>
          
          <div className="flex space-x-3">
            {onTest && (
              <button
                onClick={handleTest}
                disabled={isTesting || validationErrors.length > 0}
                className="flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                {isTesting ? (
                  <>
                    <LoadingSpinner size="sm" />
                    <span>Testing...</span>
                  </>
                ) : (
                  <>
                    <TestTube className="w-4 h-4" />
                    <span>Test</span>
                  </>
                )}
              </button>
            )}
            
            <button
              onClick={onClose}
              className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Cancel
            </button>
            
            <button
              onClick={handleSave}
              disabled={isLoading || validationErrors.length > 0 || !hasChanges}
              className="flex items-center space-x-2 bg-blue-700 hover:bg-blue-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              {isLoading ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  <span>Save Changes</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
