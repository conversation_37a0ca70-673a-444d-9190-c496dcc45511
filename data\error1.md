✅ Data processing successful: 1 valid items, 0 invalid items
✅ Bulk job dbc98f0d-9823-4bfe-90a2-97411ebb989f created with 1 items
 POST /api/admin/bulk-processing 200 in 370ms
✅ Updated job dbc98f0d-9823-4bfe-90a2-97411ebb989f status: undefined → undefined (vundefined)
🔄 Processing 1 batches for job dbc98f0d-9823-4bfe-90a2-97411ebb989f
📦 Processing batch 1/1 (1 items)
🔧 Ensuring 1 tools exist in database...
🔧 Ensuring 1 tools exist...
 GET /api/admin/bulk-processing 200 in 318ms
✅ Created new tool: 8e0116a9-0745-4f5e-90fc-bfb288b42dbb for https://photoai.com/
📦 Batch 1: 1 successful, 0 failed
✅ Tool creation completed: 1 tools ready
🎯 Creating job: tool_processing
📋 Status updated for job a0a01ffc-80cf-42fe-b531-1cdcad080f5f: pending
🚀 Starting enhanced job processing loop
✅ Job a0a01ffc-80cf-42fe-b531-1cdcad080f5f (tool_processing) added to queue   
🔄 Processing job a0a01ffc-80cf-42fe-b531-1cdcad080f5f (tool_processing)
📋 Status updated for job a0a01ffc-80cf-42fe-b531-1cdcad080f5f: processing
🔄 Processing existing tool 8e0116a9-0745-4f5e-90fc-bfb288b42dbb for https://photoai.com/
🎯 Creating job: web_scraping
 ✓ Compiled /api/admin/bulk-processing/[id] in 260ms (1708 modules)
📋 Status updated for job dcbeb227-0b10-4be5-8c7f-99b8df8392ec: pending
✅ Job dcbeb227-0b10-4be5-8c7f-99b8df8392ec (web_scraping) added to queue
✅ Loading configuration from JSONB column
🔄 Processing job dcbeb227-0b10-4be5-8c7f-99b8df8392ec (web_scraping)
🤖 Automated cleanup started (every 30 minutes)
📊 Cleanup monitoring started (every 15 minutes)
 GET /api/admin/bulk-processing/dbc98f0d-9823-4bfe-90a2-97411ebb989f 200 in 1091ms
📋 Status updated for job dcbeb227-0b10-4be5-8c7f-99b8df8392ec: processing
🕷️ Enhanced web scraping job started for: https://photoai.com/ (Tool ID: 8e01166a9-0745-4f5e-90fc-bfb288b42dbb)
🚀 Starting enhanced scraping for: https://photoai.com/
🎯 COST-OPTIMIZED SCRAPING: https://photoai.com/
🔧 Request parameters:
   token: 8e7e405f...
   url: https://photoai.com/
   device: desktop
   output: markdown
   timeout: 15000
🔗 Scraping URL: https://photoai.com/
📡 Request URL: https://api.scrape.do/?token=8e7e405ff81145c4afe447610ddb9a7f785f494dddc&url=https%3A%2F%2Fphotoai.com%2F&device=desktop&output=markdown&timeout=15000
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/dbc98f0d-9823-4bfe-90a2-97411ebb989f 200 in 255ms
✅ Loading configuration from JSONB column
✅ Scraping successful - 125531 characters received
✅ LARGE-CONTENT: 125531 chars with 10939 words - sufficient for AI
✅ COST-OPTIMIZED: Keeping basic content for https://photoai.com/ (1 credit)   
💰 COST-OPTIMIZER: Using basic content as determined by cost analysis
🔍 Text analysis: 8253 words, 2111 sentences, length: 124906
Content analysis: Content sufficient (ratio: 1.00, confidence: 70) - KEEPING content
💰 MEDIA-OPTIMIZED: Extracting media from basic content without additional scraping
✅ Enhanced scraping completed in 1936ms
Database storage failed: {}
📁 Content stored: G:\projects\dudeai1\data\scraped-content\photoai.com\photoai.com__2025-06-22T19-03-03-999Z.md
✅ Enhanced web scraping completed for: https://photoai.com/ (1 credits)       
 ✓ Compiled in 233ms (643 modules)
 GET /api/admin/bulk-processing/dbc98f0d-9823-4bfe-90a2-97411ebb989f 200 in 314ms
✅ Loading configuration from JSONB column
📊 Progress updated for job dcbeb227-0b10-4be5-8c7f-99b8df8392ec: 100% - Job completed successfully
✅ Job dcbeb227-0b10-4be5-8c7f-99b8df8392ec completed successfully
 GET /api/admin/bulk-processing/dbc98f0d-9823-4bfe-90a2-97411ebb989f 200 in 280ms
✅ Loading configuration from JSONB column
🎯 Creating job: content_generation
 GET /api/admin/bulk-processing/dbc98f0d-9823-4bfe-90a2-97411ebb989f 200 in 261ms
✅ Loading configuration from JSONB column
📋 Status updated for job ed9d9601-f8dc-49a1-8952-2d1c744fb220: pending
✅ Job ed9d9601-f8dc-49a1-8952-2d1c744fb220 (content_generation) added to queue
🔄 Processing job ed9d9601-f8dc-49a1-8952-2d1c744fb220 (content_generation)
🔄 Processing job ed9d9601-f8dc-49a1-8952-2d1c744fb220 (content_generation)
📋 Status updated for job ed9d9601-f8dc-49a1-8952-2d1c744fb220: processing
Starting content generation pipeline for: https://photoai.com/
Tool ID: 8e0116a9-0745-4f5e-90fc-bfb288b42dbb
Content size: 125602 characters
Options: {
  skipValidation: false,
  requireEditorialReview: false,
  autoApprove: false,
  qualityThreshold: 80,
  priority: 'normal'
}
Starting content generation pipeline for tool: 8e0116a9-0745-4f5e-90fc-bfb288b42dbb
 GET /api/admin/bulk-processing/dbc98f0d-9823-4bfe-90a2-97411ebb989f 200 in 264ms
📋 Status updated for job ed9d9601-f8dc-49a1-8952-2d1c744fb220: processing
Starting content generation pipeline for: https://photoai.com/
Tool ID: 8e0116a9-0745-4f5e-90fc-bfb288b42dbb
Content size: 125602 characters
Options: {
  skipValidation: false,
  requireEditorialReview: false,
  autoApprove: false,
  qualityThreshold: 80,
  priority: 'normal'
}
Starting content generation pipeline for tool: 8e0116a9-0745-4f5e-90fc-bfb288b42dbb
✅ Loading configuration from JSONB column
[2025-06-22T19:03:11.148Z] INFO: Using AI Dude methodology with gpt-4o-2024-11-20 | component=ai-system operation=ai-dude-generation toolUrl=https://photoai.com/ model=gpt-4o-2024-11-20 provider=openai methodology=ai_dude
[2025-06-22T19:03:11.389Z] INFO: Using AI Dude methodology with gpt-4o-2024-11-20 | component=ai-system operation=ai-dude-generation toolUrl=https://photoai.com/ model=gpt-4o-2024-11-20 provider=openai methodology=ai_dude
 GET /api/admin/bulk-processing/dbc98f0d-9823-4bfe-90a2-97411ebb989f 200 in 260ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/dbc98f0d-9823-4bfe-90a2-97411ebb989f 200 in 269ms
[2025-06-22T19:03:14.718Z] INFO: AI Dude generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more. | component=ai-system operation=ai-dude-generation-error toolUrl=https://photoai.com/ error=OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
Content generation pipeline failed: Error: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
    at ContentGenerationPipeline.execute (src\lib\content-generation\pipeline.ts:97:14)
    at async eval (src\lib\jobs\handlers\content-generation.ts:100:17)
    at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13) 
    at async Object.handleWithFallback (src\lib\error-handling\index.ts:300:13)
    at async ContentGenerationHandler.executeContentGeneration (src\lib\jobs\handlers\content-generation.ts:98:21)
    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\content-generation.ts:33:15)
    at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13) 
    at async Object.handleWithRestart (src\lib\error-handling\index.ts:359:13) 
    at async ContentGenerationHandler.handle (src\lib\jobs\handlers\content-generation.ts:31:11)
    at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21)
   95 |
   96 |       if (!generationResult.success) {
>  97 |         throw new Error(`AI generation failed: ${generationResult.error}`);
      |              ^
   98 |       }
   99 |
  100 |       // Step 3: Validate content (unless skipped)
Content generation pipeline failed: Error: Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
    at ContentGenerationHandler.executeContentGeneration (src\lib\jobs\handlers\content-generation.ts:124:14)
    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\content-generation.ts:33:15)
    at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13) 
    at async Object.handleWithRestart (src\lib\error-handling\index.ts:359:13) 
    at async ContentGenerationHandler.handle (src\lib\jobs\handlers\content-generation.ts:31:11)
    at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21)
  122 |
  123 |       if (!result.success) {
> 124 |         throw new Error(`Content generation pipeline failed: ${result.error}`);
      |              ^
  125 |       }
  126 |
  127 |       console.log(`Pipeline completed successfully for tool: ${request.toolId}`);
Enhanced error context: {
  type: 'Error',
  message: 'Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.',
  stack: 'Error: Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.\n' +
    '    at ContentGenerationHandler.executeContentGeneration (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:134:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:70:20)\n' +
    '    at async Object.withErrorHandling (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:110:20)\n' +
    '    at async Object.handleWithRestart (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:305:20)\n' +
    '    at async ContentGenerationHandler.handle (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:69:16)\n' +
    '    at async EnhancedJobQueue.processJob (webpack-internal:///(rsc)/./src/lib/jobs/enhanced-queue.ts:401:28)',
  context: {
    operation: 'content_generation_job',
    provider: undefined,
    toolId: '8e0116a9-0745-4f5e-90fc-bfb288b42dbb',
    jobId: undefined,
    userId: undefined,
    attempt: undefined,
    maxRetries: undefined
  },
  timestamp: '2025-06-22T19:03:14.759Z',
  retryable: true
}
ERROR TRACKED: {
  id: 'err_1750618994760_953za0q4g',
  type: 'UNKNOWN_ERROR',
  category: 'ai_generation',
  severity: 'low',
  message: 'Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.',
  context: {
    operation: 'content_generation_job',
    provider: undefined,
    toolId: '8e0116a9-0745-4f5e-90fc-bfb288b42dbb',
    jobId: 'ed9d9601-f8dc-49a1-8952-2d1c744fb220'
  },
  timestamp: '2025-06-22T19:03:14.760Z'
}
ERROR EVENT: {
  error: {
    id: 'err_1750618994760_953za0q4g',
    type: 'UNKNOWN_ERROR',
    category: 'ai_generation',
    severity: 'low',
    message: 'Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.',
    stack: 'Error: Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.\n' +
      '    at ContentGenerationHandler.executeContentGeneration (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:134:23)\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:70:20)\n' +
      '    at async Object.withErrorHandling (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:110:20)\n' +
      '    at async Object.handleWithRestart (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:305:20)\n' +
      '    at async ContentGenerationHandler.handle (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:69:16)\n' +
      '    at async EnhancedJobQueue.processJob (webpack-internal:///(rsc)/./src/lib/jobs/enhanced-queue.ts:401:28)',
    context: {
      operation: 'content_generation_job',
      provider: undefined,
      toolId: '8e0116a9-0745-4f5e-90fc-bfb288b42dbb',
      jobId: 'ed9d9601-f8dc-49a1-8952-2d1c744fb220',
      userId: undefined,
      metadata: [Object],
      retryOperation: [AsyncFunction (anonymous)],
      maxRetries: 3
    },
    classification: {
      category: 'ai_generation',
      severity: 'low',
      retryable: false,
      autoRecoverable: true,
      requiresManualIntervention: false,
      affectedSystems: [Array],
      estimatedImpact: [Object]
    },
    timestamp: '2025-06-22T19:03:14.760Z',
    originalError: Error: Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
        at ContentGenerationHandler.executeContentGeneration (src\lib\jobs\handlers\content-generation.ts:124:14)
        at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\content-generation.ts:33:15)
        at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13)
        at async Object.handleWithRestart (src\lib\error-handling\index.ts:359:13)
        at async ContentGenerationHandler.handle (src\lib\jobs\handlers\content-generation.ts:31:11)
        at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21)
      122 |
      123 |       if (!result.success) {
    > 124 |         throw new Error(`Content generation pipeline failed: ${result.error}`);
          |              ^
      125 |       }
      126 |
      127 |       console.log(`Pipeline completed successfully for tool: ${request.toolId}`);
  },
  context: {
    operation: 'content_generation_job',
    provider: undefined,
    toolId: '8e0116a9-0745-4f5e-90fc-bfb288b42dbb',
    jobId: 'ed9d9601-f8dc-49a1-8952-2d1c744fb220',
    userId: undefined,
    metadata: {
      url: 'https://photoai.com/',
      jobId: 'ed9d9601-f8dc-49a1-8952-2d1c744fb220',
      priority: undefined,
      complexity: undefined
    },
    retryOperation: [AsyncFunction (anonymous)],
    maxRetries: 3
  },
  recovery: {
    success: false,
    strategy: 'none',
    error: 'No applicable recovery strategy found',
    timestamp: '2025-06-22T19:03:14.760Z',
    requiresManualIntervention: true
  },
  timestamp: '2025-06-22T19:03:14.760Z'
}
❌ Job ed9d9601-f8dc-49a1-8952-2d1c744fb220 failed: Error: Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
    at ContentGenerationHandler.executeContentGeneration (src\lib\jobs\handlers\content-generation.ts:124:14)
    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\content-generation.ts:33:15)
    at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13) 
    at async Object.handleWithRestart (src\lib\error-handling\index.ts:359:13) 
    at async ContentGenerationHandler.handle (src\lib\jobs\handlers\content-generation.ts:31:11)
    at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21)
  122 |
  123 |       if (!result.success) {
> 124 |         throw new Error(`Content generation pipeline failed: ${result.error}`);
      |              ^
  125 |       }
  126 |
  127 |       console.log(`Pipeline completed successfully for tool: ${request.toolId}`);
[2025-06-22T19:03:14.912Z] INFO: AI Dude generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more. | component=ai-system operation=ai-dude-generation-error toolUrl=https://photoai.com/ error=OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
Content generation pipeline failed: Error: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
    at ContentGenerationPipeline.execute (src\lib\content-generation\pipeline.ts:97:14)
    at async eval (src\lib\jobs\handlers\content-generation.ts:100:17)
    at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13) 
    at async Object.handleWithFallback (src\lib\error-handling\index.ts:300:13)
    at async ContentGenerationHandler.executeContentGeneration (src\lib\jobs\handlers\content-generation.ts:98:21)
    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\content-generation.ts:33:15)
    at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13) 
    at async Object.handleWithRestart (src\lib\error-handling\index.ts:359:13) 
    at async ContentGenerationHandler.handle (src\lib\jobs\handlers\content-generation.ts:31:11)
    at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21)
   95 |
   96 |       if (!generationResult.success) {
>  97 |         throw new Error(`AI generation failed: ${generationResult.error}`);
      |              ^
   98 |       }
   99 |
  100 |       // Step 3: Validate content (unless skipped)
Content generation pipeline failed: Error: Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
    at ContentGenerationHandler.executeContentGeneration (src\lib\jobs\handlers\content-generation.ts:124:14)
    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\content-generation.ts:33:15)
    at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13) 
    at async Object.handleWithRestart (src\lib\error-handling\index.ts:359:13) 
    at async ContentGenerationHandler.handle (src\lib\jobs\handlers\content-generation.ts:31:11)
    at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21)
  122 |
  123 |       if (!result.success) {
> 124 |         throw new Error(`Content generation pipeline failed: ${result.error}`);
      |              ^
  125 |       }
  126 |
  127 |       console.log(`Pipeline completed successfully for tool: ${request.toolId}`);
Enhanced error context: {
  type: 'Error',
  message: 'Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.',
  stack: 'Error: Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.\n' +
    '    at ContentGenerationHandler.executeContentGeneration (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:134:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:70:20)\n' +
    '    at async Object.withErrorHandling (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:110:20)\n' +
    '    at async Object.handleWithRestart (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:305:20)\n' +
    '    at async ContentGenerationHandler.handle (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:69:16)\n' +
    '    at async EnhancedJobQueue.processJob (webpack-internal:///(rsc)/./src/lib/jobs/enhanced-queue.ts:401:28)',
  context: {
    operation: 'content_generation_job',
    provider: undefined,
    toolId: '8e0116a9-0745-4f5e-90fc-bfb288b42dbb',
    jobId: undefined,
    userId: undefined,
    attempt: undefined,
    maxRetries: undefined
  },
  timestamp: '2025-06-22T19:03:14.939Z',
  retryable: true
}
ERROR TRACKED: {
  id: 'err_1750618994939_ikell4cj5',
  type: 'UNKNOWN_ERROR',
  category: 'ai_generation',
  severity: 'low',
  message: 'Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.',
  context: {
    operation: 'content_generation_job',
    provider: undefined,
    toolId: '8e0116a9-0745-4f5e-90fc-bfb288b42dbb',
    jobId: 'ed9d9601-f8dc-49a1-8952-2d1c744fb220'
  },
  timestamp: '2025-06-22T19:03:14.939Z'
}
ERROR EVENT: {
  error: {
    id: 'err_1750618994939_ikell4cj5',
    type: 'UNKNOWN_ERROR',
    category: 'ai_generation',
    severity: 'low',
    message: 'Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.',
    stack: 'Error: Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.\n' +
      '    at ContentGenerationHandler.executeContentGeneration (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:134:23)\n' +
      '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
      '    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:70:20)\n' +
      '    at async Object.withErrorHandling (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:110:20)\n' +
      '    at async Object.handleWithRestart (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:305:20)\n' +
      '    at async ContentGenerationHandler.handle (webpack-internal:///(rsc)/./src/lib/jobs/handlers/content-generation.ts:69:16)\n' +
      '    at async EnhancedJobQueue.processJob (webpack-internal:///(rsc)/./src/lib/jobs/enhanced-queue.ts:401:28)',
    context: {
      operation: 'content_generation_job',
      provider: undefined,
      toolId: '8e0116a9-0745-4f5e-90fc-bfb288b42dbb',
      jobId: 'ed9d9601-f8dc-49a1-8952-2d1c744fb220',
      userId: undefined,
      metadata: [Object],
      retryOperation: [AsyncFunction (anonymous)],
      maxRetries: 3
    },
    classification: {
      category: 'ai_generation',
      severity: 'low',
      retryable: false,
      autoRecoverable: true,
      requiresManualIntervention: false,
      affectedSystems: [Array],
      estimatedImpact: [Object]
    },
    timestamp: '2025-06-22T19:03:14.939Z',
    originalError: Error: Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
        at ContentGenerationHandler.executeContentGeneration (src\lib\jobs\handlers\content-generation.ts:124:14)
        at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\content-generation.ts:33:15)
        at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13)
        at async Object.handleWithRestart (src\lib\error-handling\index.ts:359:13)
        at async ContentGenerationHandler.handle (src\lib\jobs\handlers\content-generation.ts:31:11)
        at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21)
      122 |
      123 |       if (!result.success) {
    > 124 |         throw new Error(`Content generation pipeline failed: ${result.error}`);
          |              ^
      125 |       }
      126 |
      127 |       console.log(`Pipeline completed successfully for tool: ${request.toolId}`);
  },
  context: {
    operation: 'content_generation_job',
    provider: undefined,
    toolId: '8e0116a9-0745-4f5e-90fc-bfb288b42dbb',
    jobId: 'ed9d9601-f8dc-49a1-8952-2d1c744fb220',
    userId: undefined,
    metadata: {
      url: 'https://photoai.com/',
      jobId: 'ed9d9601-f8dc-49a1-8952-2d1c744fb220',
      priority: undefined,
      complexity: undefined
    },
    retryOperation: [AsyncFunction (anonymous)],
    maxRetries: 3
  },
  recovery: {
    success: false,
    strategy: 'none',
    error: 'No applicable recovery strategy found',
    timestamp: '2025-06-22T19:03:14.939Z',
    requiresManualIntervention: true
  },
  timestamp: '2025-06-22T19:03:14.939Z'
}
❌ Job ed9d9601-f8dc-49a1-8952-2d1c744fb220 failed: Error: Content generation pipeline failed: AI generation failed: OpenAI rate limit exceeded: 429 Request too large for gpt-4o in organization org-FJcOJh5YYAIOWqe87N30O35y on tokens per min (TPM): Limit 30000, Requested 32602. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
    at ContentGenerationHandler.executeContentGeneration (src\lib\jobs\handlers\content-generation.ts:124:14)
    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.jobProcessing.handleWithRestart._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\content-generation.ts:33:15)
    at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13) 
    at async Object.handleWithRestart (src\lib\error-handling\index.ts:359:13) 
    at async ContentGenerationHandler.handle (src\lib\jobs\handlers\content-generation.ts:31:11)
    at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21)
  122 |
  123 |       if (!result.success) {
> 124 |         throw new Error(`Content generation pipeline failed: ${result.error}`);
      |              ^
  125 |       }
  126 |
  127 |       console.log(`Pipeline completed successfully for tool: ${request.toolId}`);