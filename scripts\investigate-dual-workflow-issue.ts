#!/usr/bin/env tsx

/**
 * Investigate Dual Workflow Issue
 * 
 * This script investigates why tools go to "pending_manual_review" initially
 * but then get "publishing directly" later - indicating two different code paths.
 */

import * as dotenv from 'dotenv';

// Load environment variables FIRST
dotenv.config({ path: '.env.local' });

console.log('🔍 INVESTIGATING DUAL WORKFLOW ISSUE\n');
console.log('=' .repeat(70) + '\n');

console.log('🎯 PROBLEM ANALYSIS:');
console.log('   The logs show a tool going through TWO different workflows:');
console.log('   1. First: "pending_manual_review" (manual review workflow)');
console.log('   2. Later: "publishing directly" (bulk processing workflow)');
console.log('');

console.log('🔍 ROOT CAUSE INVESTIGATION:');
console.log('');
console.log('   📋 HYPOTHESIS 1: Two Different Job Types');
console.log('      • Content generation job runs first (uses pipeline.ts)');
console.log('      • Tool submission job runs later (uses tool-submission.ts)');
console.log('      • Both check submission source independently');
console.log('      • Different timing = different detection results');
console.log('');
console.log('   📋 HYPOTHESIS 2: Race Condition');
console.log('      • Tool created without submission_source initially');
console.log('      • First job runs before submission_source is set');
console.log('      • Second job runs after submission_source is fixed');
console.log('      • Database timing issue between jobs');
console.log('');
console.log('   📋 HYPOTHESIS 3: Database Transaction Issue');
console.log('      • Tool creation and submission_source update in separate transactions');
console.log('      • First job sees uncommitted state');
console.log('      • Second job sees committed state');
console.log('      • Transaction isolation level problem');
console.log('');

console.log('🔧 CODE PATH ANALYSIS:');
console.log('');
console.log('   📁 src/lib/content-generation/pipeline.ts:');
console.log('      • Called by content_generation jobs');
console.log('      • Uses isBulkProcessingTool() method');
console.log('      • If detection fails → goes to manual review');
console.log('      • If detection succeeds → publishes directly');
console.log('');
console.log('   📁 src/lib/jobs/handlers/tool-submission.ts:');
console.log('      • Called by tool_processing jobs');
console.log('      • Has its own bulk processing detection');
console.log('      • Updates tool with generated content');
console.log('      • Can publish directly if bulk processing detected');
console.log('');

console.log('🕐 TIMING ANALYSIS:');
console.log('');
console.log('   ⏱️ Job Execution Order:');
console.log('      1. web_scraping job (creates scraped content)');
console.log('      2. content_generation job (generates AI content)');
console.log('         └─ Uses pipeline.ts → isBulkProcessingTool()');
console.log('         └─ If fails → pending_manual_review');
console.log('      3. tool_processing job (processes final tool)');
console.log('         └─ Uses tool-submission.ts → own detection');
console.log('         └─ If succeeds → publishing directly');
console.log('');

console.log('🔍 DATABASE QUERY DIFFERENCES:');
console.log('');
console.log('   📋 pipeline.ts query (FIXED):');
console.log('      • Was using .single() → failed on multiple/no rows');
console.log('      • Now uses regular query → handles edge cases');
console.log('      • Better error handling and logging');
console.log('');
console.log('   📋 tool-submission.ts query:');
console.log('      • May use different query pattern');
console.log('      • May have different error handling');
console.log('      • Could succeed where pipeline.ts fails');
console.log('');

console.log('💡 SOLUTION STRATEGY:');
console.log('');
console.log('   ✅ IMMEDIATE FIXES APPLIED:');
console.log('      1. Fixed isBulkProcessingTool() to handle database errors');
console.log('      2. Replaced .single() with regular query + array handling');
console.log('      3. Added detection for duplicate primary keys');
console.log('      4. Enhanced error logging and context');
console.log('');
console.log('   🔧 ADDITIONAL FIXES NEEDED:');
console.log('      1. Ensure consistent submission_source setting in bulk creation');
console.log('      2. Add database integrity checks for duplicate IDs');
console.log('      3. Synchronize detection logic between pipeline.ts and tool-submission.ts');
console.log('      4. Add transaction-level consistency for tool creation');
console.log('');

console.log('🧪 TESTING STRATEGY:');
console.log('');
console.log('   📋 Test Cases:');
console.log('      1. Tool exists with correct submission_source');
console.log('      2. Tool exists with incorrect submission_source');
console.log('      3. Tool does not exist (invalid ID)');
console.log('      4. Multiple tools with same ID (integrity issue)');
console.log('      5. Database connection/query errors');
console.log('');
console.log('   📊 Expected Results:');
console.log('      • Case 1: Bulk processing detected → direct publishing');
console.log('      • Case 2: User submission detected → manual review');
console.log('      • Case 3: Error handled gracefully → manual review');
console.log('      • Case 4: Warning logged, first tool used → depends on submission_source');
console.log('      • Case 5: Error handled gracefully → manual review');
console.log('');

console.log('🎯 VERIFICATION STEPS:');
console.log('');
console.log('   1. 🔍 Run diagnostic script to check specific tool');
console.log('      npx tsx scripts/diagnose-tool-database-issue.ts');
console.log('');
console.log('   2. 🧪 Test fixed detection logic');
console.log('      Process a new tool through bulk processing');
console.log('      Monitor logs for consistent detection');
console.log('');
console.log('   3. 📊 Check database integrity');
console.log('      Look for duplicate primary keys');
console.log('      Verify submission_source consistency');
console.log('');
console.log('   4. 🔄 Monitor workflow consistency');
console.log('      Ensure single workflow path per tool');
console.log('      No more dual manual review → direct publishing');
console.log('');

console.log('✅ EXPECTED OUTCOME:');
console.log('');
console.log('   After fixes:');
console.log('   • 🎯 Consistent submission source detection');
console.log('   • 🔄 Single workflow path per tool');
console.log('   • 🛡️ Graceful handling of database errors');
console.log('   • 📊 Clear logging for troubleshooting');
console.log('   • 🚀 Reliable bulk processing workflow');

export {};
