#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Generate a human-readable title from category ID (same logic as pipeline)
 */
function generateCategoryTitle(categoryId: string): string {
  return categoryId
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
    .toUpperCase();
}

/**
 * Test the simplified category creation logic
 */
async function testSimplifiedCategoryCreation() {
  console.log('🧪 TESTING SIMPLIFIED CATEGORY CREATION');
  console.log('=' .repeat(60));

  const testCategoryId = 'test-food-ai';

  try {
    // 1. Clean up any existing test category
    console.log('\n1️⃣ Cleaning up any existing test category...');
    await supabase.from('categories').delete().eq('id', testCategoryId);
    console.log('✅ Cleanup completed');

    // 2. Test category existence check
    console.log('\n2️⃣ Testing category existence check...');
    const { data: existingCategory, error: checkError } = await supabase
      .from('categories')
      .select('id')
      .eq('id', testCategoryId)
      .single();

    if (checkError && checkError.code === 'PGRST116') {
      console.log(`✅ Category ${testCategoryId} doesn't exist (as expected)`);
    } else if (existingCategory) {
      console.log(`❌ Category ${testCategoryId} unexpectedly exists`);
    } else {
      console.log(`❌ Unexpected error: ${checkError?.message}`);
    }

    // 3. Test category creation
    console.log('\n3️⃣ Testing category creation...');
    const newCategory = {
      id: testCategoryId,
      title: generateCategoryTitle(testCategoryId),
      description: `AI-generated category for ${testCategoryId}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log(`Creating category with:
   ID: ${newCategory.id}
   Title: ${newCategory.title}
   Description: ${newCategory.description}`);

    const { data: createdCategory, error: createError } = await supabase
      .from('categories')
      .insert([newCategory])
      .select()
      .single();

    if (createError) {
      console.log(`❌ Failed to create category: ${createError.message}`);
    } else {
      console.log(`✅ Successfully created category: ${createdCategory.id}`);
      console.log(`   Title: ${createdCategory.title}`);
    }

    // 4. Test that the category now exists
    console.log('\n4️⃣ Verifying category was created...');
    const { data: verifyCategory, error: verifyError } = await supabase
      .from('categories')
      .select('id, title, description')
      .eq('id', testCategoryId)
      .single();

    if (verifyError) {
      console.log(`❌ Failed to verify category: ${verifyError.message}`);
    } else {
      console.log(`✅ Category verified:
   ID: ${verifyCategory.id}
   Title: ${verifyCategory.title}
   Description: ${verifyCategory.description}`);
    }

    // 5. Test creating a tool with the new category
    console.log('\n5️⃣ Testing tool creation with new category...');
    const testTool = {
      id: 'test-tool-with-new-category',
      name: 'Test Tool with New Category',
      slug: 'test-tool-with-new-category',
      link: '/tools/test-tool-with-new-category',
      category_id: testCategoryId,
      description: 'Test tool with newly created category',
      content_status: 'draft'
    };

    // Clean up any existing test tool
    await supabase.from('tools').delete().eq('id', testTool.id);

    const { data: createdTool, error: toolError } = await supabase
      .from('tools')
      .insert([testTool])
      .select()
      .single();

    if (toolError) {
      console.log(`❌ Failed to create tool: ${toolError.message}`);
    } else {
      console.log(`✅ Successfully created tool with new category:
   Tool ID: ${createdTool.id}
   Tool Name: ${createdTool.name}
   Category ID: ${createdTool.category_id}`);
    }

    // 6. Clean up test data
    console.log('\n6️⃣ Cleaning up test data...');
    await supabase.from('tools').delete().eq('id', testTool.id);
    await supabase.from('categories').delete().eq('id', testCategoryId);
    console.log('✅ Test cleanup completed');

    console.log('\n📋 Test Summary:');
    console.log('✅ Category existence check works correctly');
    console.log('✅ Category creation works with exact AI-provided ID');
    console.log('✅ Tools can be created with newly created categories');
    console.log('✅ No foreign key constraint violations');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testSimplifiedCategoryCreation().catch(console.error);
}

export { testSimplifiedCategoryCreation };
