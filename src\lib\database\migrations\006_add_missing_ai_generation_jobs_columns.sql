-- Migration 006: Add missing columns to ai_generation_jobs table
-- This migration adds all the missing columns that the enhanced job queue system expects
-- to exist in the ai_generation_jobs table for proper retry mechanisms and job control

-- =====================================================
-- ADD MISSING COLUMNS TO AI_GENERATION_JOBS TABLE
-- =====================================================

-- Add retry and attempt tracking columns
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS attempts INTEGER DEFAULT 0 CHECK (attempts >= 0);
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS max_attempts INTEGER DEFAULT 3 CHECK (max_attempts > 0);

-- Add job scheduling and priority columns
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 10);
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS scheduled_for TIMESTAMP WITH TIME ZONE;

-- Add job data and metadata columns
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS job_data JSONB;
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS tags JSONB DEFAULT '[]'::jsonb;
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS result JSONB;

-- Add job control flags
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS can_pause BOOLEAN DEFAULT true;
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS can_resume BOOLEAN DEFAULT false;
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS can_stop BOOLEAN DEFAULT true;

-- Add timing and performance tracking columns
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS estimated_duration INTEGER; -- in milliseconds
ALTER TABLE ai_generation_jobs ADD COLUMN IF NOT EXISTS actual_duration INTEGER; -- in milliseconds

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for job scheduling and priority-based processing
CREATE INDEX IF NOT EXISTS idx_ai_generation_jobs_priority ON ai_generation_jobs(priority DESC, created_at ASC);
CREATE INDEX IF NOT EXISTS idx_ai_generation_jobs_scheduled_for ON ai_generation_jobs(scheduled_for) WHERE scheduled_for IS NOT NULL;

-- Index for retry mechanism
CREATE INDEX IF NOT EXISTS idx_ai_generation_jobs_attempts ON ai_generation_jobs(attempts, status) WHERE status IN ('failed', 'retrying');

-- Composite index for job queue processing
CREATE INDEX IF NOT EXISTS idx_ai_generation_jobs_queue_processing ON ai_generation_jobs(status, priority DESC, created_at ASC) 
WHERE status IN ('pending', 'retrying');

-- Index for job control and monitoring
CREATE INDEX IF NOT EXISTS idx_ai_generation_jobs_control_flags ON ai_generation_jobs(can_pause, can_resume, can_stop) 
WHERE status = 'processing';

-- =====================================================
-- UPDATE EXISTING RECORDS WITH DEFAULT VALUES
-- =====================================================

-- Update existing records to have sensible defaults for new columns
-- This ensures backward compatibility with existing job records

UPDATE ai_generation_jobs 
SET 
    attempts = 0,
    max_attempts = 3,
    priority = 1,
    tags = '[]'::jsonb,
    can_pause = true,
    can_resume = false,
    can_stop = true
WHERE 
    attempts IS NULL 
    OR max_attempts IS NULL 
    OR priority IS NULL 
    OR tags IS NULL 
    OR can_pause IS NULL 
    OR can_resume IS NULL 
    OR can_stop IS NULL;

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON COLUMN ai_generation_jobs.attempts IS 'Current number of processing attempts for this job';
COMMENT ON COLUMN ai_generation_jobs.max_attempts IS 'Maximum number of retry attempts allowed for this job';
COMMENT ON COLUMN ai_generation_jobs.priority IS 'Job priority (1-10, higher numbers = higher priority)';
COMMENT ON COLUMN ai_generation_jobs.scheduled_for IS 'Timestamp when job should be processed (for delayed/retry jobs)';
COMMENT ON COLUMN ai_generation_jobs.job_data IS 'Job-specific data and parameters';
COMMENT ON COLUMN ai_generation_jobs.tags IS 'Array of tags for job categorization and filtering';
COMMENT ON COLUMN ai_generation_jobs.result IS 'Job execution result data';
COMMENT ON COLUMN ai_generation_jobs.can_pause IS 'Whether this job type supports pausing';
COMMENT ON COLUMN ai_generation_jobs.can_resume IS 'Whether this job can be resumed from paused state';
COMMENT ON COLUMN ai_generation_jobs.can_stop IS 'Whether this job can be manually stopped';
COMMENT ON COLUMN ai_generation_jobs.estimated_duration IS 'Estimated processing time in milliseconds';
COMMENT ON COLUMN ai_generation_jobs.actual_duration IS 'Actual processing time in milliseconds';

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'Migration 006 completed: Added missing columns to ai_generation_jobs table';
    RAISE NOTICE 'Added columns: attempts, max_attempts, priority, scheduled_for, job_data, tags, result, can_pause, can_resume, can_stop, estimated_duration, actual_duration';
    RAISE NOTICE 'Created indexes for improved job queue performance';
    RAISE NOTICE 'Updated existing records with default values for backward compatibility';
END $$;
