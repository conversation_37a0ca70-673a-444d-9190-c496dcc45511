'use client';

import { useState, useEffect } from 'react';

interface ProgressTrackerProps {
  jobId: string;
  onComplete: () => void;
  onError: (error: string) => void;
}

interface JobProgress {
  id: string;
  status: string;
  progress: number;
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  currentBatch: number;
  totalBatches: number;
  estimatedTimeRemaining: number;
  details?: {
    message?: string;
    currentItem?: string;
    errors?: string[];
  };
}

/**
 * Progress Tracker Component
 * 
 * Real-time progress tracking for bulk processing jobs
 * with detailed metrics and visual indicators.
 */
export function ProgressTracker({
  jobId,
  onComplete,
  onError,
}: ProgressTrackerProps) {
  const [progress, setProgress] = useState<JobProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [logs, setLogs] = useState<string[]>([]);

  useEffect(() => {
    if (!jobId) return;

    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/admin/bulk-processing/${jobId}`, {
          headers: {
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || '',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch job progress');
        }

        const data = await response.json();
        if (data.success && data.job) {
          const job = data.job;
          setProgress({
            id: job.id,
            status: job.status,
            progress: job.progress || 0,
            totalItems: job.data?.totalItems || 0,
            processedItems: job.data?.processedItems || 0,
            successfulItems: job.data?.successfulItems || 0,
            failedItems: job.data?.failedItems || 0,
            currentBatch: job.data?.currentBatch || 0,
            totalBatches: job.data?.totalBatches || 0,
            estimatedTimeRemaining: job.data?.estimatedTimeRemaining || 0,
            details: job.data?.details,
          });

          // Add new log entries
          if (job.data?.details?.message) {
            setLogs(prev => {
              const newLog = `${new Date().toLocaleTimeString()}: ${job.data.details.message}`;
              if (!prev.includes(newLog)) {
                return [...prev.slice(-9), newLog]; // Keep last 10 logs
              }
              return prev;
            });
          }

          setLoading(false);

          // Check if job is complete
          if (job.status === 'completed' || job.status === 'failed' || job.status === 'cancelled') {
            clearInterval(pollInterval);
            setLoading(false);

            if (job.status === 'completed') {
              console.log('✅ Job completed successfully, calling onComplete');
              onComplete();
            } else if (job.status === 'failed') {
              console.log('❌ Job failed, calling onError');
              onError('Bulk processing job failed');
            } else {
              console.log('⏹️ Job cancelled');
              onError('Bulk processing job was cancelled');
            }
          }
        }
      } catch (err) {
        console.error('Error polling job progress:', err);
        onError(err instanceof Error ? err.message : 'Failed to track progress');
        clearInterval(pollInterval);
      }
    }, 2000); // Poll every 2 seconds

    return () => clearInterval(pollInterval);
  }, [jobId, onComplete, onError]);

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'running': return 'text-blue-400';
      case 'completed': return 'text-green-400';
      case 'failed': return 'text-red-400';
      case 'paused': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string): string => {
    switch (status) {
      case 'running': return '🔄';
      case 'completed': return '✅';
      case 'failed': return '❌';
      case 'paused': return '⏸️';
      default: return '⏳';
    }
  };

  if (loading) {
    return (
      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        <div className="flex items-center justify-center space-x-3">
          <div className="animate-spin w-6 h-6 border-2 border-orange-500 border-t-transparent rounded-full"></div>
          <span className="text-white">Loading job progress...</span>
        </div>
      </div>
    );
  }

  if (!progress) {
    return (
      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        <div className="text-center text-red-400">
          Failed to load job progress
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Progress Card */}
      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">Processing Progress</h2>
          <div className={`flex items-center space-x-2 ${getStatusColor(progress.status)}`}>
            <span className="text-lg">{getStatusIcon(progress.status)}</span>
            <span className="font-medium capitalize">{progress.status}</span>
          </div>
        </div>

        {/* Overall Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-400 mb-2">
            <span>Overall Progress</span>
            <span>{Math.round(progress.progress)}%</span>
          </div>
          <div className="w-full bg-zinc-700 rounded-full h-3">
            <div 
              className="bg-orange-500 h-3 rounded-full transition-all duration-500"
              style={{ width: `${progress.progress}%` }}
            />
          </div>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-zinc-700 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-400">{progress.processedItems}</div>
            <div className="text-sm text-gray-400">Processed</div>
            <div className="text-xs text-gray-500">of {progress.totalItems}</div>
          </div>
          <div className="bg-zinc-700 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-400">{progress.successfulItems}</div>
            <div className="text-sm text-gray-400">Successful</div>
            <div className="text-xs text-gray-500">
              {progress.totalItems > 0 ? Math.round((progress.successfulItems / progress.totalItems) * 100) : 0}%
            </div>
          </div>
          <div className="bg-zinc-700 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-red-400">{progress.failedItems}</div>
            <div className="text-sm text-gray-400">Failed</div>
            <div className="text-xs text-gray-500">
              {progress.totalItems > 0 ? Math.round((progress.failedItems / progress.totalItems) * 100) : 0}%
            </div>
          </div>
          <div className="bg-zinc-700 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-400">
              {progress.estimatedTimeRemaining > 0 ? formatTime(progress.estimatedTimeRemaining) : '--'}
            </div>
            <div className="text-sm text-gray-400">Time Left</div>
            <div className="text-xs text-gray-500">Estimated</div>
          </div>
        </div>

        {/* Batch Progress */}
        {progress.totalBatches > 1 && (
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-400 mb-2">
              <span>Batch Progress</span>
              <span>Batch {progress.currentBatch} of {progress.totalBatches}</span>
            </div>
            <div className="w-full bg-zinc-700 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(progress.currentBatch / progress.totalBatches) * 100}%` }}
              />
            </div>
          </div>
        )}

        {/* Current Item */}
        {progress.details?.currentItem && (
          <div className="bg-zinc-700 rounded-lg p-4 mb-6">
            <div className="text-sm text-gray-400 mb-1">Currently Processing:</div>
            <div className="text-white font-mono text-sm break-all">
              {progress.details.currentItem}
            </div>
          </div>
        )}
      </div>

      {/* Activity Log */}
      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Activity Log</h3>
        <div className="bg-zinc-900 rounded-lg p-4 h-48 overflow-y-auto">
          {logs.length > 0 ? (
            <div className="space-y-1">
              {logs.map((log, index) => (
                <div key={index} className="text-sm text-gray-300 font-mono">
                  {log}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No activity logs yet...</div>
          )}
        </div>
      </div>

      {/* Error Details */}
      {progress.details?.errors && progress.details.errors.length > 0 && (
        <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-400 mb-4">Errors</h3>
          <div className="space-y-2">
            {progress.details.errors.map((error, index) => (
              <div key={index} className="text-sm text-red-300 bg-red-900/30 rounded p-2">
                {error}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
