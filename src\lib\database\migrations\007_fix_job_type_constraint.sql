-- Migration 007: Fix job_type constraint to include 'tool_processing'
-- This migration updates the ai_generation_jobs table constraint to include
-- the 'tool_processing' job type that is used by the bulk processing system

-- =====================================================
-- UPDATE JOB TYPE CONSTRAINT
-- =====================================================

-- Drop the existing constraint
ALTER TABLE ai_generation_jobs DROP CONSTRAINT IF EXISTS ai_generation_jobs_job_type_check;

-- Add the updated constraint with 'tool_processing' included
ALTER TABLE ai_generation_jobs ADD CONSTRAINT ai_generation_jobs_job_type_check 
CHECK (job_type IN ('scrape', 'generate', 'bulk', 'media_extraction', 'tool_processing'));

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check the updated constraint (using modern PostgreSQL approach)
SELECT
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conname = 'ai_generation_jobs_job_type_check';

-- Test inserting a job with 'tool_processing' type (should succeed)
-- This is a dry run test - we'll rollback immediately
BEGIN;
INSERT INTO ai_generation_jobs (id, job_type, status) 
VALUES ('00000000-0000-0000-0000-000000000001', 'tool_processing', 'pending');
ROLLBACK;

-- =====================================================
-- DOCUMENTATION
-- =====================================================

COMMENT ON CONSTRAINT ai_generation_jobs_job_type_check ON ai_generation_jobs 
IS 'Ensures job_type is one of the valid types: scrape, generate, bulk, media_extraction, tool_processing';

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'Migration 007 completed: Updated job_type constraint to include tool_processing';
    RAISE NOTICE 'Valid job types are now: scrape, generate, bulk, media_extraction, tool_processing';
    RAISE NOTICE 'Bulk processing system can now create individual tool_processing jobs';
END $$;
